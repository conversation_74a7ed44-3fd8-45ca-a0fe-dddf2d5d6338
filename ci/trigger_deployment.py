#!/usr/bin/env python3
"""Script to trigger the deployment pipeline."""

# ruff: noqa: S603, S607
# nosec: B603, B607
import argparse
import logging
import os
import sys
from pathlib import Path
from env_vars import *

from build_lib import setup_logging, trigger_deploy_pipeline

logger = logging.getLogger(__name__)
MAIN_BRANCH = "main"
REMOTE_NAME = "origin"
PYPROJECT_TOML = Path("pyproject.toml")


def main() -> None:
    """Check if latest Repo tag exists in CodeArtifacts. Tag and publish if not."""
    parser = argparse.ArgumentParser(
        description="Deploy release pipeline to the Cluster.",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
    )
    parser.add_argument(
        "environment",
        choices=["INT", "TEST", "PROD"],
        help="Exactly three environment values: INT, TEST, and PROD.",
    )
    parser.add_argument("version", help="Release version")
    parser.add_argument(
        "--token",
        required=False,
        help="Bitbucket token used to trigger"
        " the pipeline. Preferred: set with the BB_REMOTE_ACCESS_TOKEN environment variable.",
    )
    args = parser.parse_args()
    BB_TRIGGER_TOKEN = args.token or BB_REMOTE_ACCESS_TOKEN

    setup_logging(logging.INFO)

    if not BB_TRIGGER_TOKEN:
        logger.error("Missing required environment variable: BB_REMOTE_ACCESS_TOKEN!")
        sys.exit(1)

    logger.info(
        f"Performing release of v{args.version} to environment {args.environment} ..."
    )

    env_map = {
        "INT": "CUJU-AI-Integration",
        "TEST": "CUJU-AI-Test",
        "PROD": "CUJU-AI-Production",
    }
    env = env_map.get(args.environment, "INT")
    os.environ["BB_REMOTE_ACCESS_TOKEN"] = BB_TRIGGER_TOKEN

    trigger_deploy_pipeline(version=args.version, environment=env)


if __name__ == "__main__":
    main()

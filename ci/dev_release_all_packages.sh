#!/bin/bash

# This script publishes a development version of all plugins within the UV Workspace to CodeArtifacts.
# It can only be executed on an up-to-date main branch. The latest dev version on CodeArtifacts will always
# represent the Code on origin/main.

# We expect aws-cli to be set up with the following settings:
# aws configure set aws_access_key_id ${AWS_ACCESS_KEY_ID}
# aws configure set aws_secret_access_key ${AWS_SECRET_ACCESS_KEY}
# aws configure set default.region $AWS_DEFAULT_REGION

# =========== CHECKS ===========
if [[ -z "$UV_PUBLISH_TOKEN" ]]; then
    if [[ -z "$CODEARTIFACT_DOMAIN" || -z "$AWS_ACCOUNT_ID" || -z "$AWS_DEFAULT_REGION" ]]; then
        echo "ERROR: Environment variable UV_PUBLISH_TOKEN not found."
        echo "Either set UV_PUBLISH_TOKEN yourself, or run 'aws configure' and provide the following environment variables: "
        echo "CODEARTIFACT_DOMAIN, AWS_ACCOUNT_ID, AWS_DEFAULT_REGION"
        exit 1
    else
        # Get AWS CodeArtifact Authorization Token to publish packages with UV
        export UV_PUBLISH_TOKEN="$(aws codeartifact get-authorization-token \
            --domain $CODEARTIFACT_DOMAIN \
            --domain-owner $AWS_ACCOUNT_ID \
            --region $AWS_DEFAULT_REGION \
            --query authorizationToken \
            --output text
        )"
    fi
fi

if [ ! $(git branch --show-current) = 'main' ];then
    echo "To keep a consistent state of all packages in CodeArtifacts, this script can only be executed on the main branch.";
    exit 1
fi

git fetch origin main &> /dev/null
if [ ! -z "$(git diff main origin/main)" ]; then
  echo "Your local main branch is not up-to-date with origin. To keep a consistent state in CodeArtifacts, we don't want to publish anything that has not landed on origin/main yet."
  exit 1
fi

echo "Checking if dist/* does not contain packages to publish already ..."
if [ "$(ls dist/*.{tar.gz,whl})" ]; then
    # by default, UV will publish everything inside dist/, so we need to ensure that it is empty before we execute this script
    echo "The dist folder contains packages for publication. In order to run this script locally, you need to have a clean dist directory."
    read -p "Do you want to delete all source distributions and wheels in dist? (y/n): " response
    if [[ "$response" =~ ^[Yy]$ ]]; then
        rm -f dist/*.{tar.gz,whl}
    else
        exit 1
    fi
fi

# =========== VARIABLES AND FUNCTIONS ===========
SUBMODULES_TO_PUBLISH=()
# Make toml and dunamai available from the version pinned in uv.lock, updated by update-bot
uv sync --only-group dev --no-install-project --frozen
source .venv/bin/activate

bump_dev_version() {
    local submodule_path=$1

    if [[ ! -f "$submodule_path/pyproject.toml" ]]; then
        echo "No pyproject.toml found in $submodule_path, skipping..."
        return
    fi

    local pyproject_toml_path="$submodule_path/pyproject.toml"
    local module_name="$(toml get --toml-path=$pyproject_toml_path project.name)"
    # This is a workaround until UV supports a version-bumping-command https://github.com/astral-sh/uv/issues/6298
    local new_version=$(dunamai from git --tag-branch remotes/origin/main --bump --style pep440 --pattern "$module_name-(?P<base>\d+\.\d+\.\d+)")
    echo "Bumping dev release version: $module_name ($new_version)"
    toml set --toml-path=$pyproject_toml_path project.version $new_version
    SUBMODULES_TO_PUBLISH+=("$submodule_path")
}

# =========== MAIN PROCEDURE ===========
echo "Preparing DEV RELEASE ..."

# Get all workspace members from the main pyproject.toml
export WORKSPACE_MEMBERS="$(toml get --toml-path=pyproject.toml tool.uv.workspace.members | sed -E 's/(\[|\]|,)//g' | tr -d \')"
# Get a list of folders from the glob patterns
export WORKSPACE_MEMBERS="$(echo $WORKSPACE_MEMBERS | xargs -n1 -I{} bash -O nullglob -c "echo {}" | xargs -n1)"
git stash --include-untracked

# If we move away from UV in the future, we can use static folder globs here instead of the workspace-feature
# for submodule in imgeta components/cuju/* components/zylo/* services/* ; do
for submodule in $WORKSPACE_MEMBERS ; do
    if [ -d "$submodule" ] && [ -f "$submodule/pyproject.toml" ]; then
        # Create a new development version in each pyproject.toml before building all packages
        bump_dev_version "$submodule"
    fi
done

# Build all workspace members - see above
uv build --all-packages || { echo "Build failed. See above. To avoid inconsistent state, no modules will be published until this problem is fixed."; git checkout -f; git stash pop; exit 1; }

# Publish all packages in ./dist
if uv publish --index cuju-ai-workbench; then
    echo "Successfully published development versions of all plugins."
    git checkout -f # remove changes from bumped versions
    git stash pop # restore saved changes
    exit 0
fi

# If we end up here, publication of one or more plugins failed
echo "Publication for one or more plugins failed. All or nothing. To avoid inconsistent state, previously published packages from this pipeline will be rolled back."
if [[ ${#SUBMODULES_TO_PUBLISH[@]} -gt 0 ]]; then
    for submodule_path in "${SUBMODULES_TO_PUBLISH[@]}"; do
        export PYPROJECT_TOML_PATH="$submodule_path/pyproject.toml"
        export MODULE_NAME="$(toml get --toml-path=$PYPROJECT_TOML_PATH project.name)"
        # Here we rely on the fact, that the bumped version is still present in pyproject.toml (no git stash/checkout was run yet)
        export PACKAGE_VERSION="$(toml get --toml-path=$PYPROJECT_TOML_PATH project.version)"
        echo "Removing $MODULE_NAME ($PACKAGE_VERSION) from CodeArtifacts ..."
        python ci/prune_codeartifacts_packages.py --packages $MODULE_NAME --repository cuju-ai-workbench --dev --versions $PACKAGE_VERSION
    done
fi
git checkout -f # remove changes from bumped versions
git stash pop # restore saved changes
echo "No plugins were published to CodeArtifacts. Fix the errors and run this pipeline again."
exit 1

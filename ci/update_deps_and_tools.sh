#!/bin/bash

# This script can be used to update all dependencies and tools of the project
# It is used to update the projects dependencies on a regular schedule.
# In order for this script to work, ensure that the Repository Access Token is not expired.

BB_TOKEN=$1
PR_BRANCH_NAME=$2

if [ "$(git config user.email)" = "" ]; then
    git config user.email "<EMAIL>"
fi
if [ "$(git config user.name)" = "" ]; then
    git config user.name "Update Bot"
fi

if [ "$BB_TOKEN" = "" ]; then
  echo "This script will update the dependencies of cuju-ai-workbench and its tooling to the respective latest versions."
  echo "Usage: $0 <BITBUCKET_ACCESS_TOKEN> [PR_BRANCH_NAME=chore/dependency-and-tool-upgrade]"
  exit 1
fi
if [ "$PR_BRANCH_NAME" = "" ]; then
  export PR_BRANCH_NAME="chore/dependency-and-tool-upgrade"
fi

if git ls-remote --exit-code --heads origin "refs/heads/$PR_BRANCH_NAME"; then
    echo "The git branch $PR_BRANCH_NAME already exists. Assuming that there are pending updates."
    echo "If there is no open pull request to update the dependencies, ensure that you no longer need the contents of $PR_BRANCH_NAME and then do:"
    echo "git push origin --delete $PR_BRANCH_NAME"
    exit 1
fi

# for local execution only
git stash --include-untracked
export GIT_PREV_BRANCH=$(git branch --show-current)
git switch main
git pull

# Update Python dependencies
uv lock --upgrade &> updates.txt
git checkout -b $PR_BRANCH_NAME
git add uv.lock

# Update UV package manager in Pipeline
export CURRENT_UV_VERSION="$(uv --version)"
export LATEST_UV_VERSION="$(curl -s https://pypi.org/pypi/uv/json | jq -r '.info.version')"
sed -i -E "s/UV_VERSION=.*/UV_VERSION=$LATEST_UV_VERSION/" bitbucket-pipelines.yml
sed -i -E "s/rev:.*# uv version.*/rev: $LATEST_UV_VERSION # uv version - keep this comment for automation/" .pre-commit-config.yaml
git add bitbucket-pipelines.yml .pre-commit-config.yaml
git commit -m "chore: update python dependencies & pipeline tools" --no-verify

# If no error happened until here, push changes to remote
git push origin $PR_BRANCH_NAME

# for local execution only
git switch $GIT_PREV_BRANCH
git stash pop
git branch -D $PR_BRANCH_NAME

# Open a Pull Request
export UPDATED_DEPS="$(cat updates.txt | grep 'Updated' | sed 's/->/to/' | column -t)"
export PR_CREATION_RESULT=$(curl --request POST \
  --url 'https://api.bitbucket.org/2.0/repositories/rogontechnologies/cuju-ai-workbench/pullrequests' \
  --header "Authorization: Bearer $BB_TOKEN" \
  --header 'Content-Type: application/json' \
  --data "{
        \"title\": \"chore: dependency & tool update\",
        \"description\": \"This PR updates the dependencies and tools of cuju-ai-workbench. The update is \
        performed on a regular schedule to avoid technical debt. This PR gives you the chance to review \
        and test the updates. Perform changes if necessary. Thanks for keeping our project up-to-date 🤩! \
        \n\n# Python Dependencies \n\`\`\`\n${UPDATED_DEPS//$'\n'/\\n}\n\`\`\` \n\n# UV: update \
        from ‘$CURRENT_UV_VERSION' to '$LATEST_UV_VERSION'\",
        \"close_source_branch\": true,
        \"source\": {
            \"branch\": {
                \"name\": \"$PR_BRANCH_NAME\"
            }
        }
    }")
echo "PR created!"
echo $PR_CREATION_RESULT | jq -r '.links.html.href'

#!/bin/bash

# Install poetry
echo "Installing poetry"
curl -sSL https://install.python-poetry.org | python3 -

echo "Poetry is installed"

echo "Adding poetry to PATH"
export PATH="/root/.local/bin:$PATH"

# This script is used to install given project in the projects directory
install_project() {
    echo "Installing $1"
    cd $1
    if [ -f "$1/install.sh" ]; then
        echo "Running install.sh" && chmod +x install.sh && ./install.sh
    else
        echo "Running uv sync" && uv sync
    fi
}

# Install the project
echo "Installing project $1"
install_project $1

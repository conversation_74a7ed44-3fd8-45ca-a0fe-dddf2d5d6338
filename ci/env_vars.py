import os

AWS_ACCOUNT_ID= os.getenv("NEW_AWS_ACCOUNT_ID", "************")
AWS_ACCESS_KEY_ID = os.getenv("NEW_AWS_ACCESS_KEY_ID")
AWS_SECRET_ACCESS_KEY = os.getenv("NEW_AWS_SECRET_ACCESS_KEY")
AWS_DEFAULT_REGION = os.getenv("AWS_DEFAULT_REGION", "eu-central-1")

CODEARTIFACT_DOMAIN = os.getenv("CODEARTIFACT_DOMAIN", "cuju-ai")
CODEARTIFACT_REPO = os.getenv("CODEARTIFACT_REPOSITORY", "cuju-ai-workbench")

UV_PUBLISH_TOKEN = os.getenv("UV_PUBLISH_TOKEN") # optional - can be fetched with AWS credentials

BB_LOCAL_ACCESS_TOKEN = os.getenv("REPO_ACCESS_TOKEN") # token to open PRs in the local BB Repo
BB_REMOTE_ACCESS_TOKEN = os.getenv("NEW_BB_REMOTE_ACCESS_TOKEN") # Token to trigger inference-server pipeline
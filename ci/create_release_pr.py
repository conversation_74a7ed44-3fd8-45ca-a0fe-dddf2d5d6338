#!/usr/bin/env python3
"""Script to open a release PR."""

# ruff: noqa: S603, S607
# nosec: B603, B607
import argparse
import logging
import os
import sys
from dataclasses import asdict, dataclass
from pathlib import Path
from subprocess import CalledProcessError, run  # nosec

import requests  # Used for BitBucket API call
from build_lib import (
    bump_version_from_tag,
    check_branch_exists,
    check_command_exists,
    check_tag_exists,
    fetch_main,
    get_commit_hash,
    get_current_branch,
    get_module_name,
    get_next_semantic_version,
    is_commit_on_main,
    is_main_branch_up_to_date,
    restore_workspace_state,
    setup_logging,
    store_workspace_state,
)
from release import main as release_main
from env_vars import *

logger = logging.getLogger(__name__)
BITBUCKET_API_URL = "https://api.bitbucket.org/2.0/repositories/rogontechnologies/cuju-ai-workbench/pullrequests"
MAIN_BRANCH = "main"
TEMP_BRANCH = "release/branch-off-for-release"  # used to avoid detatched head
REMOTE_NAME = "origin"
PYPROJECT_TOML = Path("pyproject.toml")

# --- Helper Functions ---


def determine_target_commit(commit_hash: str | None) -> str:
    """Get the target commit or REMOTE/MAIN by default."""
    if commit_hash:
        logger.info("Checking provided commit hash: %s", commit_hash)
        if is_commit_on_main(commit_hash):
            target_commit = commit_hash
            logger.info("Using provided valid commit hash: %s", target_commit)
        else:
            logger.error(
                "Provided commit hash '%s' is not a valid commit on %s/%s.",
                commit_hash,
                REMOTE_NAME,
                MAIN_BRANCH,
            )
            sys.exit(1)
    else:
        logger.info(
            "No commit hash provided, using latest from %s/%s.",
            REMOTE_NAME,
            MAIN_BRANCH,
        )
        target_commit = get_commit_hash(f"{REMOTE_NAME}/{MAIN_BRANCH}")
        if not target_commit:
            logger.error(
                "Could not determine the latest commit hash for %s/%s.",
                REMOTE_NAME,
                MAIN_BRANCH,
            )
            sys.exit(1)
        logger.info("Using latest commit hash from remote main: %s", target_commit)
    return target_commit


@dataclass
class PrPayload:
    """Payload to Create a Bitbucket Pull Request."""

    source: dict  # {"branch": {"name": "..."}}
    destination: dict  # {"branch": {"name": "..."}}
    title: str = "No Title specified"
    description: str = ""
    close_source_branch: bool = False


def create_bitbucket_pr(payload: PrPayload) -> None:
    """Create a Pull Request in Bitbucket."""
    if not BB_LOCAL_ACCESS_TOKEN:
        logger.error(
            "Bitbucket API token not found. Please set the BB_TOKEN environment "
            "variable."
        )
        sys.exit(1)
    headers = {
        "Authorization": f"Bearer {BB_LOCAL_ACCESS_TOKEN}",
        "Content-Type": "application/json",
        "Accept": "application/json",
    }

    payload_dict = asdict(payload)
    source_branch_name = payload.source.get("branch", {}).get(
        "name", "N/A"
    )  # Get name for logging

    logger.info(
        "Creating Pull Request on Bitbucket for branch '%s'...", source_branch_name
    )
    try:
        response = requests.post(
            BITBUCKET_API_URL,
            headers=headers,
            json=payload_dict,
            timeout=30,
        )
        response.raise_for_status()  # Raise HTTPError for bad responses(4xx or 5xx)

        pr_data = response.json()
        pr_url = pr_data.get("links", {}).get("html", {}).get("href", "N/A")
        logger.info("Pull Request created successfully!")
        logger.info("PR URL: %s", pr_url)

    except requests.exceptions.RequestException as e:
        logger.exception("Failed to create Pull Request.")
        if hasattr(e, "response") and e.response is not None:
            logger.error("Response status: %s", e.response.status_code)
            # Log only first 500 chars of response to avoid flooding logs
            logger.error("Response body: %s ...", e.response.text[:500])
        # Don't exit immediately, allow finally block to clean up git state
        raise  # Re-raise the exception after logging

    logger.info(
        "Successfully created release branch '%s' and associated Pull Request.",
        source_branch_name,
    )


# --- Main Script Logic ---


def main() -> None:
    """Create a Release PR for a release."""
    parser = argparse.ArgumentParser(
        description="Create a release Pull Request from the main branch.",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
    )
    parser.add_argument(
        "--commit-hash",
        help=f"Specific commit hash on the {MAIN_BRANCH} branch to base the release PR "
        f"off. Defaults to the latest commit on {REMOTE_NAME}/{MAIN_BRANCH}.",
        default=None,
        type=str,
    )
    parser.add_argument(
        "--force-bump",
        help="Force semantic-release to perform a specific version bump.",
        choices=["AUTOMATIC", "MAJOR", "MINOR", "PATCH"],
        default="AUTOMATIC",
        type=str.upper,  # Ensure choices are compared case-insensitively
    )
    parser.add_argument(
        "-v",
        "--verbose",
        help="Enable debug logging.",
        action="store_const",
        dest="loglevel",
        const=logging.DEBUG,
        default=logging.INFO,
    )
    args = parser.parse_args()

    setup_logging(args.loglevel)

    # --- Pre-checks ---
    logger.info("Performing pre-checks...")
    check_command_exists("git")
    check_command_exists("cz")

    if not PYPROJECT_TOML.is_file():
        logger.error("%s not found in the current directory.", PYPROJECT_TOML)
        sys.exit(1)

    if not BB_LOCAL_ACCESS_TOKEN:
        logger.error(
            "Bitbucket Token not found. Please set the environment variable BB_LOCAL_ACCESS_TOKEN "
            "with a valid Bitbucket Access Token to the Repository and proper rights "
            "to create a Pull Request."
        )
        sys.exit(1)

    current_branch = get_current_branch()
    if current_branch != MAIN_BRANCH:
        logger.error(
            "To keep a consistent state, this script must be executed on the '%s' "
            "branch (currently on '%s').",
            MAIN_BRANCH,
            current_branch,
        )
        sys.exit(1)
    logger.info("Currently on branch: %s", current_branch)

    fetch_main()
    if not is_main_branch_up_to_date():
        logger.error(
            "Your local '%s' branch is not up-to-date with '%s/%s'. "
            "Please sync it first (e.g., git push/pull).",
            MAIN_BRANCH,
            REMOTE_NAME,
            MAIN_BRANCH,
        )
        sys.exit(1)
    logger.info("Local '%s' is up-to-date with remote.", MAIN_BRANCH)

    # Determine target commit
    target_commit = determine_target_commit(args.commit_hash)

    # Ensure target commit is checked out *before* stashing to avoid issues
    # Stashing first might save state based on the *current* HEAD, not the target.

    # --- Main Procedure ---
    logger.info("Starting Release Pull Request creation process...")

    # Store current state *before* checking out the target commit
    store_workspace_state(Path.cwd())
    previous_branch = get_current_branch()  # Remember the original branch for later

    try:
        # Checkout the target commit (detaching HEAD is fine here)
        logger.info("Checking out target commit: %s...", target_commit[:7])
        # We cannot be in detatched head while running the following commands
        # Therefore we use a temp branch that we delete afterwards
        run(["git", "checkout", "-b", TEMP_BRANCH, target_commit], check=True)  # nosec

        if args.force_bump == "AUTOMATIC":
            new_version, new_tag_version = get_next_semantic_version(Path(), "FINAL")
        else:
            new_version, new_tag_version = bump_version_from_tag(args.force_bump)

        if not new_version or not new_tag_version:
            logger.warning("Failed to get version info from semantic-release.")
            # Cleanup is handled by the finally block
            sys.exit(1)
        logger.info("Next version calculated: %s", new_version)
        logger.info("Next tag version: %s", new_tag_version)

        branch_name = f"release/v{new_version}"
        logger.info("Release branch name will be: %s", branch_name)

        check_tag_exists(new_tag_version, include_remote=True)
        check_branch_exists(branch_name, include_remote=True)

        # Create the new branch from the checked-out target commit
        logger.info(
            "Creating branch '%s' from commit %s...", branch_name, target_commit[:7]
        )
        # Rename temp release branch (needed to avoid detatched head) to release/{new_version}
        run(["git", "branch", "-m", TEMP_BRANCH, branch_name], check=True)  # nosec

        release_main(
            Path(), new_version, skip_build=True, skip_tag=True, skip_publish=True
        )

        # Prepare and Create Pull Request
        logger.info("Preparing Pull Request...")

        # Get project name from main pyproject.toml for the PR title
        project_name = get_module_name(PYPROJECT_TOML)

        pr_description = f"""\
### This Pull-Request prepares a release of {project_name} {new_tag_version}.

**Note:** Version bump was performed with level: `{args.force_bump}`.

- **Every commit that lands on this PR will create a new release-candidate** that will be used for testing.
- Keep in mind, that **you cannot rebase or squash in this PR** to preserve the tag-consistency. So commit with care.
- If errors are encountered during testing, you can fix them in this PR
- **A final release is created when this PR is merged into main**
- If errors are encountered after the PR is merged and the final release is published, a hotfix can be created by checking out the release tag and adding the fix.
- **ONLY MERGE THIS PR (fast-forward or regular).** No squashing or rebasing because this will not migrate the created tags.
"""  # noqa: E501

        pr_payload = PrPayload(
            title=f"release: {project_name} {new_tag_version}",
            description=pr_description,
            close_source_branch=False,
            source={"branch": {"name": branch_name}},
            destination={"branch": {"name": MAIN_BRANCH}},
        )
        create_bitbucket_pr(pr_payload)

    except (
        CalledProcessError,
        FileNotFoundError,
        SystemExit,
        requests.exceptions.RequestException,
    ) as e:
        # Log stderr from CalledProcessError if available
        stderr_info = getattr(e, "stderr", "")
        if stderr_info:
            logger.error(f"Process stderr:\n{stderr_info.strip()}")
        # Catch known exit conditions or errors during the process
        logger.exception("Release process failed. Check logs for details. ")
        # Let the finally block handle cleanup
        sys.exit(1)  # Ensure script exits with error status
    except Exception as e:
        # Catch any unexpected errors
        logger.exception(f"An unexpected error occurred: {e}")
        sys.exit(1)

    finally:
        # --- Cleanup ---
        logger.info("Cleaning up local workspace...")
        # Switch back to the original branch *before* popping stash
        logger.debug("Switching back to original branch: %s", previous_branch)
        run(["git", "switch", previous_branch], check=False)  # nosec
        # remove temporary branch, if it was created
        run(["git", "branch", "-D", TEMP_BRANCH], check=False)  # nosec
        # Restore stashed changes (handles git checkout -f and stash pop)
        restore_workspace_state()
        logger.info("Cleanup complete.")


if __name__ == "__main__":
    main()

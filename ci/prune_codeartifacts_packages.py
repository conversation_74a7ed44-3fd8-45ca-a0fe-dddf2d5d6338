#!/usr/bin/env python3

"""Script that prunes the AWS Codeartifacts Registry of old Dev-Releasees."""

from __future__ import annotations

import argparse
import logging
import os
import re
import sys
from dataclasses import dataclass
from functools import cmp_to_key
from typing import Literal, NamedTuple
from env_vars import *

import boto3


def main(
    repo: AwsRepo,
    config: PruneConfig,
) -> None:
    """Prune versions from CodeArtifacts."""
    client = boto3.client(
        "codeartifact",
        aws_access_key_id=ACCESS_KEY,
        aws_secret_access_key=SECRET_KEY,
        region_name=REGION,
    )
    pkgs = get_package_list(client, repo)

    for pkg in pkgs:
        pkg_name, pkg_format = pkg.get("package"), pkg.get("format")
        if config.prune_packages is not None and pkg_name not in config.prune_packages:
            logging.debug(
                "Skipping %s as it is not on the pruning list provided by --packages",
                pkg_name,
            )
            continue

        logging.debug("Pruning package: %s", pkg_name)

        versions = client.list_package_versions(
            domainOwner=repo.domain_owner,
            domain=repo.domain,
            repository=repo.name,
            format=pkg_format,
            package=pkg_name,
        ).get("versions", [])
        versions = (BotoVersion(**v) for v in versions)
        version_names = [v.version for v in versions if v.version]
        candidates = filter_versions_to_prune(
            version_names, config.prune_versions, dev=config.dev
        )

        # sort according to version number
        candidates.sort(key=cmp_to_key(version_compare), reverse=config.keep_oldest)
        versions_to_prune = (
            candidates[: -config.keep_n] if config.keep_n > 0 else candidates
        )
        if len(versions_to_prune) == 0:
            logging.debug("No versions to prune in %s", pkg_name)
            continue

        if config.interactive_mode:
            confirm = input(
                f"Do you want to delete versions {', '.join(versions_to_prune)} from "
                f"package {pkg_name}? (y/n): "
            )
            if confirm.lower() != "y":
                logging.info("Skipping deletion of versions.")
                continue

        delete_package_versions(client, repo, pkg_format, pkg_name, versions_to_prune)
    logging.info("Pruning complete.")


def setup_logging(log_level: int = logging.INFO) -> None:
    """Set up default logger."""
    blue = "\x1b[1;34m"
    reset = "\x1b[0m"
    logging.basicConfig(
        format=blue
        + "%(asctime)s | %(levelname)s | %(filename)s:%(lineno)d "
        + reset
        + "%(message)s",
        datefmt="%m/%d/%Y %I:%M:%S",
        level=log_level,
    )


def version_exists_in_code_artifacts(
    client: boto3.client, repo: AwsRepo, package: str, version: str
) -> bool:
    """Check if a package version exists in the CodeArtifacts repository."""
    try:
        client.list_package_version_assets(
            domain=repo.domain,
            domainOwner=repo.domain_owner,
            repository=repo.name,
            format="pypi",
            package=package,
            packageVersion=version,
            maxResults=1,
        )
        return True
    except client.exceptions.ResourceNotFoundException:
        return False


def get_package_list(client: boto3.client, repo: AwsRepo) -> list[dict]:
    """Get the list of packages in an AWS repository."""
    response = client.list_repositories_in_domain(domain=repo.domain, domainOwner=repo.domain_owner)
    if "repositories" not in response:
        logging.error("No repositories found in Domain '%s'", repo.domain)
        sys.exit(1)
    repos: list = response.get("repositories")
    if repo.name not in (r.get("name") for r in repos):
        logging.error(
            "Repository '%s' not found in Domain '%s'",
            repo.name,
            repo.domain,
        )
        sys.exit(1)

    logging.info("Pruning repository: %s", repo.name)
    return client.list_packages(domain=repo.domain, repository=repo.name, domainOwner=repo.domain_owner).get(
        "packages",
        [],
    )


def filter_versions_to_prune(
    version_names: list[str],
    prune_versions: list[str],
    *,
    dev: bool,
) -> list[str]:
    """Filter the given version names for dev versions and whitelist."""
    if prune_versions is not None:
        version_names = [v for v in version_names if v in prune_versions]

    if dev:
        # Filter out versions with 'dev' in their version number
        # Example: 1.0.0dev1, 1.0.0dev2, 1.0.0dev3, etc.
        dev_pattern = re.compile(r"^[\d\.]*(dev|post)\d+")
        version_names = [v for v in version_names if dev_pattern.match(v)]
    return version_names


def delete_package_versions(
    client: boto3.client,
    repo: AwsRepo,
    pkg_format: str,
    pkg_name: str,
    versions_to_prune: list[str],
) -> None:
    """Delete package versions from an AWS Repository."""
    logging.info(
        "Deleting versions %s from package %s",
        ", ".join(versions_to_prune),
        pkg_name,
    )
    response = client.delete_package_versions(
        domain=repo.domain,
        domainOwner=repo.domain_owner,
        repository=repo.name,
        format=pkg_format,
        package=pkg_name,
        versions=versions_to_prune,
    )
    failed = response.get("failedVersions")
    if failed:
        f_vers = failed.keys()
        logging.error("Failed to delete versions: %s", ", ".join(f_vers))
        for ver, err_dict in failed.items():
            logging.error(
                "Version: %s, Error: %s - %s",
                ver,
                err_dict.get("errorCode"),
                err_dict.get("errorMessage", ""),
            )
        sys.exit(1)


def convert_comma_separated_str_to_set(input_str: str) -> set[str]:
    """Convert a string of a comma-separated list to a set of strings."""
    return set(map(str.strip, input_str.split(",")))


class PruneConfig(NamedTuple):
    """Named tuple to represent Botocore versions."""

    keep_n: int = 0
    prune_packages: list[str] | None = None
    prune_versions: list[str] | None = None
    dev: bool = False
    keep_oldest: bool = False
    interactive_mode: bool = False


class BotoVersion(NamedTuple):
    """Named tuple to represent Botocore versions."""

    version: str
    revision: str
    status: Literal["Published", "Unlisted", "Archived"]
    origin: dict


@dataclass
class AwsRepo:
    """Dataclass to represent an AWS Codeartifacts Repository."""

    name: str
    domain: str
    domain_owner: str | None = None


def version_compare(a: str, b: str) -> int:
    """Compare two Semver version strings for ordering.

    Parameters
    ----------
    a : str
        First Version
    b : str
        Second Version

    Returns
    -------
    int
        0 if versions are equal; 1 if a >b; -1 if b > a
    """
    pattern = re.compile(
        r"^(?P<major>\d*)\.?(?P<minor>\d*)\.?(?P<patch>\d*)"
        r"(?:\.(?P<pre>dev|post)(?P<inc>\d*))?",
    )
    a_match, b_match = pattern.match(a), pattern.match(b)
    if a_match is None or b_match is None:
        logging.warning("Version '%s' and '%s' were not compared. Parsing error", a, b)
        return 0

    [a_major, a_minor, a_patch, a_pre, a_inc] = a_match.groups("0")
    [b_major, b_minor, b_patch, b_pre, b_inc] = b_match.groups("0")
    a_patch = int(a_patch) - (1 if a_pre == "dev" else 0)
    b_patch = int(b_patch) - (1 if b_pre == "dev" else 0)

    if abs(d := (int(a_major) - int(b_major))) > 0:
        return d
    if abs(d := (int(a_minor) - int(b_minor))) > 0:
        return d
    if abs(d := (int(a_patch) - int(b_patch))) > 0:
        return d
    if abs(d := (int(a_inc) - int(b_inc))) > 0:
        return d
    return 0


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Prune AWS CodeArtifacts Registry. "
        "If not provided with --keep or --dev, the script will delete all versions of "
        "every package in the Codeartifacts repository.",
    )
    parser.add_argument("--access-key", required=False, help="AWS Access Key")
    parser.add_argument("--secret-key", required=False, help="AWS Secret Key")
    parser.add_argument("--domain-owner", required=False, help="AWS Domain Owner")
    parser.add_argument(
        "--region",
        required=False,
        default="eu-central-1",
        help="AWS Region",
    )
    parser.add_argument("--domain", required=False, help="AWS Domain")
    parser.add_argument(
        "--debug",
        required=False,
        action="store_true",
        help="Enable verbose logging",
    )
    parser.add_argument(
        "--force",
        required=False,
        action="store_true",
        help="Override safety constraints",
    )
    parser.add_argument(
        "--repository",
        required=False,
        help="AWS CodeArtifacts repository",
    )
    parser.add_argument(
        "--keep",
        help=(
            "Number (N) of dev versions to keep. Will keep the latest (according to "
            "SemVer) N dev versions for each package and delete the rest."
        ),
        required=False,
    )
    parser.add_argument(
        "--packages",
        help=(
            "Limits the pruning to a one or a comma-separated list of packages. \n"
            'Example: `--packages "cuju-block-counter,cuju-agg-frames"`'
        ),
        required=False,
    )
    parser.add_argument(
        "--versions",
        help=(
            "Comma-separated list of versions that should be pruned. Can be combined "
            "with --package. Cannot be combined with --keep/--keep-oldest"
        ),
        required=False,
    )
    parser.add_argument(
        "--interactive",
        help=("Ask before permanently deleting. Recommended."),
        required=False,
        action="store_true",
        default=False,
    )
    parser.add_argument(
        "--dev",
        help="Prune only development releases (ending in devN or postN).",
        required=False,
        action="store_true",
    )
    parser.add_argument(
        "--keep-oldest",
        help="Reverses the order of packages to prune. Example: --keep 3 --keep-oldest "
        "keeps the oldest 3 versions of a package while the default behavior "
        "keeps the latest (according to SemVer) versions of a package.",
        required=False,
        action="store_true",
    )
    args = parser.parse_args()
    ACCESS_KEY = args.access_key or AWS_ACCESS_KEY_ID
    SECRET_KEY = args.secret_key or AWS_SECRET_ACCESS_KEY
    REGION = args.region or AWS_DEFAULT_REGION
    REPO = args.repository or CODEARTIFACT_REPO
    DOMAIN = args.domain or CODEARTIFACT_DOMAIN
    DOMAIN_OWNER = args.domain_owner or AWS_ACCOUNT_ID
    KEEP_N = int(args.keep or 0)
    LOG_LEVEL = logging.INFO if not args.debug else logging.DEBUG

    setup_logging(log_level=LOG_LEVEL)

    if not args.dev and not args.interactive and not args.force:
        logging.error(
            "Pruning of non-development packages should be only done in interactive mode"
            " to avoid accidental deletion of production packages. Add --force to "
            "override this constraint if you know what you are doing."
        )
        sys.exit(1)

    if args.keep and args.versions:
        logging.error(
            "--keep and --versions cannot be used together. Please choose only one."
        )
        sys.exit(1)

    # Convert str of comma-seaparated parameters into sets
    if args.packages is not None:
        args.packages = convert_comma_separated_str_to_set(args.packages)
        logging.debug("Pruning packages: %s", ", ".join(args.packages))
    if args.versions is not None:
        args.versions = convert_comma_separated_str_to_set(args.versions)
        logging.debug("Pruning versions: %s", ", ".join(args.versions))

    config = PruneConfig(
        keep_n=KEEP_N,
        keep_oldest=args.keep_oldest,
        prune_packages=args.packages,
        prune_versions=args.versions,
        dev=args.dev,
        interactive_mode=args.interactive,
    )

    main(
        repo=AwsRepo(domain=DOMAIN, name=REPO, domain_owner=DOMAIN_OWNER), config=config
    )

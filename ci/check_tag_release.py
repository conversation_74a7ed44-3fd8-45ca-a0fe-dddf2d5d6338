#!/usr/bin/env python3
"""Script to open a release PR."""

# ruff: noqa: S603, S607
# nosec: B603, B607
import argparse
import logging
import os
import sys
from pathlib import Path

import boto3
from build_lib import (
    check_main_branch_up_to_date,
    create_release_tag_and_push,
    get_module_name,
    get_most_recent_tag,
    get_workspace_member_paths,
    parse_version_str,
    setup_logging,
    tag_exists,
)
from create_release_pr import check_command_exists
from prune_codeartifacts_packages import AwsRepo, version_exists_in_code_artifacts
from env_vars import *

logger = logging.getLogger(__name__)
MAIN_BRANCH = "main"
REMOTE_NAME = "origin"
PYPROJECT_TOML = Path("pyproject.toml")


def main() -> None:
    """Check if latest Repo tag exists in CodeArtifacts. Tag and publish if not."""
    parser = argparse.ArgumentParser(
        description="Check if latest Repo tag exists in CodeArtifacts. Tag and publish if not.",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
    )
    parser.add_argument("--access-key", required=False, help="AWS Access Key ID")
    parser.add_argument("--secret-key", required=False, help="AWS Secret Key")
    parser.add_argument(
        "--region", required=False, default="eu-central-1", help="AWS Region"
    )
    parser.add_argument(
        "--repository", required=False, help="AWS CodeArtifacts repository"
    )
    parser.add_argument("--domain", required=False, help="AWS Domain")
    parser.add_argument("--domain-owner", required=False, help="AWS Domain Owner")
    args = parser.parse_args()
    ACCESS_KEY = args.access_key or AWS_ACCESS_KEY_ID
    SECRET_KEY = args.secret_key or AWS_SECRET_ACCESS_KEY
    REGION = args.region or AWS_DEFAULT_REGION
    REPO = args.repository or CODEARTIFACT_REPO
    DOMAIN = args.domain or CODEARTIFACT_DOMAIN
    OWNER = args.domain_owner or AWS_ACCOUNT_ID

    setup_logging(logging.INFO)

    # --- Pre-checks ---
    logger.info("Performing pre-checks...")
    check_command_exists("git")

    if not any([ACCESS_KEY, SECRET_KEY, REGION, REPO, DOMAIN]):
        logger.error(
            "Missing required environment variables. Please set them up or provide them as command-line arguments."
        )
        sys.exit(1)

    check_main_branch_up_to_date()

    most_recent_tag = get_most_recent_tag(pattern="v*")
    re_match = parse_version_str(most_recent_tag)
    if re_match is None:
        logger.error("Invalid version string in tag: %s", most_recent_tag)
        logger.info("Ignoring tag %s for publication.", most_recent_tag)
        sys.exit(0)
    is_release_candidate = re_match.group("pre_l") == "rc"
    if not is_release_candidate:
        logger.info("%s is not a release candidate. Skipping tagging.", most_recent_tag)
        sys.exit(0)
    release_version = re_match.group("release")
    if tag_exists(release_version):
        logger.warning("Tag %s found on origin. Skipping tagging.", release_version)
        sys.exit(0)

    # Bitbucket does not have a trigger for merged PRs. Instead, we rely on the following
    # approach:
    # - when a release PR is merged, the last tag will be a release candidate
    # - check if the most recent tag is a RC and if it exists on CodeArtifacts
    # - if the release does't exist, tag the release and let the tag-pipeline handle publication
    # - if the release does exist, we skip tagging and assume the release is already published.

    # check if package exists on CodeArtifacts
    repo = AwsRepo(domain=DOMAIN, name=REPO, domain_owner=OWNER)
    workspace_paths = get_workspace_member_paths(Path())
    workspace_paths.remove(Path())  # we don't publish the main pyproject.toml
    add_toml = lambda p: p.joinpath(PYPROJECT_TOML)
    toml_paths = map(add_toml, workspace_paths)
    members = list(map(get_module_name, toml_paths))

    client = boto3.client(
        "codeartifact",
        aws_access_key_id=ACCESS_KEY,
        aws_secret_access_key=SECRET_KEY,
        region_name=REGION,
    )
    package_version_missing = [
        not version_exists_in_code_artifacts(client, repo, member, release_version)
        for member in members
    ]

    if all(package_version_missing):
        logger.info(
            "No workspace-member has version %s on CodeArtifacts. Creating Tag"
            " to publish.",
            release_version,
        )
        # Create and push the release tag at the latest tag's commit hash
        create_release_tag_and_push(release_version, commit_hash=most_recent_tag)
    else:
        logger.info(
            "Found workspace-members with version %s on CodeArtifacts. Skipping Tagging.",
            release_version,
        )
    sys.exit(0)


if __name__ == "__main__":
    main()

import argparse
from enum import Str<PERSON><PERSON>, auto
import logging
import sys
from pathlib import Path
from subprocess import CalledProcessError

from build_lib import (
    build_all_packages,
    check_for_publication_credentials,
    create_changelog,
    create_release_commit_and_push,
    create_release_tag_and_push,
    get_next_semantic_version,
    get_workspace_member_paths,
    is_valid_version,
    pin_all_service_dependencies,
    publish_all_packages,
    restore_workspace_state,
    set_version_in_tomls,
    setup_logging,
    store_workspace_state,
    tag_exists,
)

logger = logging.getLogger(__name__)


class ReleaseType(StrEnum):
    MANUAL = auto()
    AUTOMATIC = auto()


def main(
    path_to_pyproject: Path,
    version_str: str,
    *,
    skip_commit: bool = False,
    skip_pin: bool = False,
    skip_build: bool = False,
    skip_tag: bool = False,
    skip_publish: bool = False,
) -> None:
    """Release a custom CUJU AI VERSION."""
    release_type = ReleaseType.MANUAL
    if any(version_str.upper() == v for v in ["DEV", "ALPHA", "RC", "FINAL"]):
        release_type = ReleaseType.AUTOMATIC
        # Determine the next semantic version based on the commits in the git history
        version_str, _ = get_next_semantic_version(
            path_to_pyproject, version_str.upper()
        )
    logger.info("Preparing %s Release: v%s.", release_type, version_str)
    logger.debug("Scanning for UV workspace members ...")
    plugin_paths = get_workspace_member_paths(path_to_pyproject)
    logger.debug(
        "Workspacemembers to process in %s/pyproject.toml:\n%s",
        path_to_pyproject.resolve(),
        "\n".join(map(str, plugin_paths)),
    )

    if not is_valid_version(version_str):
        logger.warning(
            "The provided version '%s' does not comply with PEP440.", version_str
        )
        if input("Would you like to continue anyway? [yes/no]\n").lower() != "yes":
            logger.info("Release cancelled.")
    if not skip_tag and tag_exists("v" + version_str, include_remote=True):
        logger.error("Tag 'v%s' already exists. Release aborted.", version_str)
        sys.exit(1)
    store_workspace_state(path_to_pyproject)
    try:
        set_version_in_tomls(plugin_paths, version_str)
        if not release_type == ReleaseType.MANUAL:
            create_changelog(path_to_pyproject)
        else:
            logger.info("Manual release. Skipping Changelog generation.")
        if not skip_commit:
            create_release_commit_and_push(plugin_paths, version_str, path_to_pyproject)
        if not skip_tag:
            create_release_tag_and_push(version_str)
        if not skip_pin:
            pin_all_service_dependencies(workspace_root=path_to_pyproject)
        if not skip_build:
            build_all_packages(path_to_pyproject)
        if not skip_publish:
            publish_all_packages(path_to_pyproject, plugin_paths)
        logger.info("All opearions successful.")
    except CalledProcessError as e:
        logger.exception(
            "Command '%s' failed with return code %s, output:\n%s\n%s\n",
            e.cmd,
            e.returncode,
            e.output,
            e.stderr,
        )
        sys.exit(1)
    except Exception:
        logger.exception("Releasing failed. Starting recovery.")
        sys.exit(1)
        # TODO: delete tags if we have pushed any and the release failed
    finally:
        restore_workspace_state()


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Release all plugins of CUJU AI Workbench. Every package will be "
        "updated to the provided version, built and published to AWS "
        "CodeArtifacts. No Changelog is generated for manual publication."
    )
    parser.add_argument(
        "version",
        help="Version that should be set for all the pyproject.toml projects in this "
        "UV workspace. Can include pre-or-post-releases. To automatically determine the "
        "next version based on git history, pass in the Release Variant: DEV, ALPHA, "
        "RC or FINAL.\n"
        "Examples for manual version bump: 1.2.3, 1.2.3.a5, 1.2.3.rc5\n"
        "Examples for automatic version bump: DEV, ALPHA, RC, FINAL",
    )
    parser.add_argument(
        "--path_to_pyproject",
        default=".",
        help="Path to the folder containing the main pyproject.toml",
    )
    parser.add_argument(
        "--no-commit",
        action="store_true",
        help="Skip creating a commit that contains a version bump to all pyproject.toml "
        "files",
    )
    parser.add_argument(
        "--no-tag",
        action="store_true",
        help="Skip tagging & pushing the release in git. Without this option, a new "
        "commit will be created for the release containing the version update and all "
        "release tags attached.",
    )
    parser.add_argument(
        "--no-build",
        action="store_true",
        help="Skip build of all packages. Implies --no-publish",
    )
    parser.add_argument(
        "--no-pin-service-deps",
        action="store_true",
        help="Skip pinning dependencies of services on Publication. If this flag is not"
        " set, then all dependencies of packages inside the 'services' folder will be"
        " pinned to the version present in the uv.lock file at build time. Those fixed "
        "dependencies will be published to CodeArtifacts, ensureing reliable "
        "deployments of the service.",
    )
    parser.add_argument(
        "--no-publish",
        action="store_true",
        help="Does not publish any built packages.",
    )
    parser.add_argument(
        "-v",
        "--verbose",
        action="store_true",
        help="Verbose (debugging) output.",
    )
    args = parser.parse_args()
    args.version = str(args.version).strip().lstrip("v")

    setup_logging(log_level=logging.DEBUG if args.verbose else logging.INFO)

    if args.no_build:
        # If we are not building, we should not publish
        args.no_publish = True

    if not args.no_publish:
        check_for_publication_credentials()

    main(
        path_to_pyproject=Path(args.path_to_pyproject),
        version_str=args.version,
        skip_commit=args.no_commit,
        skip_tag=args.no_tag,
        skip_pin=args.no_pin_service_deps,
        skip_build=args.no_build,
        skip_publish=args.no_publish,
    )

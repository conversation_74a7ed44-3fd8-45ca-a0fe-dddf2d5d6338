from hatchling.builders.hooks.plugin.interface import BuildHookInterface
from hatchling.metadata.plugin.interface import MetadataHookInterface

authors = [
    {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"},
    {"name": "<PERSON><PERSON>", "email": "<EMAIL>"},
    {"name": "<PERSON>", "email": "<PERSON>@mail.schwarz"},
    {"name": "<PERSON><PERSON>", "email": "<EMAIL>"},
    {"name": "<PERSON><PERSON>", "email": "<PERSON><EMAIL>"},
    {"name": "<PERSON><PERSON>", "email": "<EMAIL>"},
    {"name": "<PERSON>", "email": "<PERSON>.<PERSON>@inovex.de"},
    {"name": "<PERSON>", "email": "<PERSON>.Z<PERSON><EMAIL>"},
    {"name": "Constantin <PERSON>", "email": "<EMAIL>"},
    {"name": "<PERSON>", "email": "<EMAIL>"},
    {"name": "<PERSON> <PERSON>madony", "email": "<EMAIL>"},
]


class SpecialBuildHook(BuildHookInterface):
    PLUGIN_NAME = "imgeta"

    # def initialize(self, version, build_data):
    #     build_data["build_hooks"] = (*build_data, JSONMetaDataHook(self.root, self.config))
    #     return super().initialize(version, build_data)


class JSONMetaDataHook(MetadataHookInterface):
    def update(self, metadata):
        # src_file = os.path.join(self.root, "gnumeric", ".constants.json")
        # with open(src_file) as src:
        # constants = json.load(src)
        # metadata["version"] = constants["__version__"]
        # metadata["license"] = constants["__license__"]
        metadata["authors"] = authors

#!/bin/bash

poetry self add poetry-multiproject-plugin

SUBMODULES_TO_PUBLISH=()

LAST_MAIN_COMMIT="HEAD^"

release_submodule() {
    local submodule_path=$1
    if [[ ! -f "$submodule_path/pyproject.toml" ]]; then
        echo "No pyproject.toml found in $submodule_path, skipping..."
        return
    fi
    local module_name=$(awk '/name/ {print $3;exit;}' "$submodule/pyproject.toml" | sed 's/"//g')
    local version=$(poetry version -s --directory "$submodule_path")
    # We can assume that there is always a tag in the past because the publish will create it, if it doesn't exist
    if git diff --name-only "$module_name-$version" HEAD -- "$submodule_path" | grep "$submodule_path" > /dev/null; then
        echo "Plugin $module_name has changed since last release ($version). Preparing new release commit:"
        version_bump "$submodule_path"
        generate_changelog "$submodule_path"
        generate_documentation "$submodule_path"
    fi
}

version_bump() {
    local submodule_path=$1
    local module_name=$(awk '/name/ {print $3;exit;}' "$submodule_path/pyproject.toml" | sed 's/"//g')
    # bump local pyproject.toml
    poetry version patch --directory $submodule_path
    local new_version=$(poetry version -s --directory $submodule_path)
    SUBMODULES_TO_PUBLISH+=("$module_name-$new_version")

    local build_path="$submodule_path/build"
    if [[ ! -f "$build_path/pyproject.toml" ]]; then
        echo "No pyproject.toml found in $build_path, skipping..."
        return
    fi

    poetry version patch --directory $build_path
    local new_version=$(poetry version -s --directory $build_path)
    SUBMODULES_TO_PUBLISH+=("$module_name-$new_version")
}

generate_changelog() {
    local submodule_path=$1
    local module_name=$(awk '/name/ {print $3;exit;}' "$submodule_path/pyproject.toml" | sed 's/"//g')
    echo "Generating CHANGELOG.md for $module_name"
    conventional-changelog --commit-path "$submodule_path" -t "$module_name-*" --skip-unstable -i "$submodule_path/CHANGELOG.md" -s -p conventionalcommits
    git add "$submodule_path/CHANGELOG.md"
}

generate_documentation() {
    local submodule_path=$1
    # Enhance this with automatic doc generation for sub-module
}


git fetch origin main

echo "Preparing release by changing local plugin files ..."
if [ -d "imgeta" ]; then
    release_submodule "imgeta"
fi

for submodule in bases/runners/*; do
    if [ -d "$submodule/build" ]; then
       release_submodule "$submodule"
    fi
done

for submodule in components/cuju/*; do
    if [ -d "$submodule/build" ]; then
       release_submodule "$submodule"
    fi
done
for submodule in components/zylo/*; do
    if [ -d "$submodule/build" ]; then
       release_submodule "$submodule"
    fi
done
echo "Done. Committing & Pushing to origin"
git config user.name "Release Bot"
git config user.email "<EMAIL>"
git commit -am "release: generic development release"
git push origin

# Tag all released plugins
UNIQ_TAGS=($(printf "%s\n" "${SUBMODULES_TO_PUBLISH[@]}" | sort -u))
if [[ ${#UNIQ_TAGS[@]} -gt 0 ]]; then
    echo "Tagging all released sub-modules"
    for submodule in "${UNIQ_TAGS[@]}"; do
        version=$(echo "$submodule"| rev | cut -d- -f1)
        module_name=$(echo "$submodule"| rev | cut -d- -f2- | rev)
        git tag -a "$submodule" -m "Bump version of $module to $version"
    done
else
    echo "No submodules to release."
fi
git push --tags origin

echo "Release changes committed and pushed."

"""Library that provides functions to release Cuju AI Modules."""

from dataclasses import dataclass, asdict
import itertools
import json
import logging
import os
import re
import shutil
import sys
from collections.abc import Iterable
from pathlib import Path
from subprocess import PIPE, CalledProcessError, run  # nosec
from tempfile import NamedTemporaryFile
from typing import Literal, overload

import boto3
import requests
import tomlkit
from prune_codeartifacts_packages import (
    AwsRepo,
    PruneConfig,
)
from prune_codeartifacts_packages import main as prune_codeartifacts
from tomlkit.toml_file import TOMLFile
from env_vars import *

# ruff: noqa: S603, S607

logger = logging.getLogger(__name__)

TOML_NAME = "pyproject.toml"
MAIN_BRANCH = "main"
REMOTE_NAME = "origin"
PYPROJECT_TOML = Path(TOML_NAME)
remember_workdir = None


def is_valid_version(version_str: str) -> bool:
    """Check if the provided version str conforms to PEP440."""
    return (
        re.match(
            r"^v?([1-9][0-9]*!)?(0|[1-9][0-9]*)(\.(0|[1-9][0-9]*))*((a|b|rc)(0|[1-9][0-9]*))?(\.post(0|[1-9][0-9]*))?(\.dev(0|[1-9][0-9]*))?(?:\+([a-z0-9]+(?:[-_\.][a-z0-9]+)*))?$",
            version_str,
        )
        is not None
    )


def is_commit_on_main(commit_hash: str) -> bool:
    """Check if a commit hash is an ancestor of the main branch."""
    if not commit_hash:
        return False
    remote_main_ref = f"{REMOTE_NAME}/{MAIN_BRANCH}"
    fetch_main()  # Ensure remote main ref is up-to-date

    # First, verify the commit exists
    verify_cmd = ["git", "rev-parse", "--verify", f"{commit_hash}^{{commit}}"]
    verify_result = run(verify_cmd, check=False)  # nosec
    if verify_result.returncode != 0:
        logger.error("Commit hash '%s' does not seem to exist.", commit_hash)
        return False

    # Check if the commit is an ancestor of remote main
    is_ancestor_cmd = [
        "git",
        "merge-base",
        "--is-ancestor",
        commit_hash,
        remote_main_ref,
    ]
    result = run(is_ancestor_cmd, check=False)  # nosec
    # If it's an ancestor, git returns 0. If not, it returns 1. Other errors return > 1.
    return result.returncode == 0


def check_command_exists(command_name: str) -> None:
    """Check if a command exists in the system's PATH."""
    if not shutil.which(command_name):
        logger.error(
            "Required command '%s' not found in PATH. Please install it.", command_name
        )
        sys.exit(1)


def parse_version_str(version_str: str) -> re.Match:
    """Parse a version string and return the parts of the version (PEP440).

    Parameters
    ----------
    version_str : str
        Formatted version string (e.g. 1.2.3.dev12)

    Returns
    -------
    re.Match
        Regex Match with the individual parts of the version string:
        epoch, release, pre (pre_l, pre_n), post (post_n1, post_l, post_n2),
        dev (dev_l, dev_n), local
    """
    # Pattern used: https://peps.python.org/pep-0440/#appendix-b-parsing-version-strings-with-regular-expressions
    version_pattern = r"""
        v?
        (?:
            (?:(?P<epoch>[0-9]+)!)?                           # epoch
            (?P<release>[0-9]+(?:\.[0-9]+)*)                  # release segment
            (?P<pre>                                          # pre-release
                [-_\.]?
                (?P<pre_l>alpha|a|beta|b|preview|pre|c|rc)
                [-_\.]?
                (?P<pre_n>[0-9]+)?
            )?
            (?P<post>                                         # post release
                (?:-(?P<post_n1>[0-9]+))
                |
                (?:
                    [-_\.]?
                    (?P<post_l>post|rev|r)
                    [-_\.]?
                    (?P<post_n2>[0-9]+)?
                )
            )?
            (?P<dev>                                          # dev release
                [-_\.]?
                (?P<dev_l>dev)
                [-_\.]?
                (?P<dev_n>[0-9]+)?
            )?
        )
        (?:\+(?P<local>[a-z0-9]+(?:[-_\.][a-z0-9]+)*))?       # local version
    """

    _regex = re.compile(
        r"^\s*" + version_pattern + r"\s*$",
        re.VERBOSE | re.IGNORECASE,
    )

    return _regex.fullmatch(version_str)


def store_workspace_state(path_to_pyproject: Path) -> None:
    """Store changes in the current workdir and switch to the project dir."""
    logger.debug("Changing working directory from %s to %s", Path(), path_to_pyproject)
    global remember_workdir
    remember_workdir = Path.cwd()
    os.chdir(path_to_pyproject)
    logger.info("Stashing all local changes before altering files.")
    run(["git", "stash", "--include-untracked"], check=False)  # nosec


def restore_workspace_state() -> None:
    """Restore the workspace state to the state before the script ran."""
    global remember_workdir
    logger.info("Cleaning up. Discarding temp changes and restoring workdir ...")
    run(["git", "checkout", "-f"], check=False)  # nosec
    run(["git", "stash", "pop"], check=False)  # nosec
    os.chdir(remember_workdir)  # return to previous directory


def get_codeartifact_token(
    key_id: str,
    secret: str,
    domain: str,
    domain_owner: str,
    region: str = "eu-central-1",
) -> str:
    """Get an AWS CodeArtifact token."""
    client = boto3.client(
        "codeartifact",
        aws_access_key_id=key_id,
        aws_secret_access_key=secret,
        region_name=region,
    )
    return client.get_authorization_token(
        domain=domain,
        domainOwner=domain_owner,
        durationSeconds=1800,  # token valid for 30 minutes
    ).get("authorizationToken")


def check_for_publication_credentials() -> None:
    """Check if we have all credentials to publish to AWS Codeartifacts."""
    logger.debug("Checking for AWS CodeArtifacts publication credentials ...")
    if not UV_PUBLISH_TOKEN:
        logger.debug("UV_PUBLISH_TOKEN not found. Checking for AWS credentials.")
        required_env_vars = [
            CODEARTIFACT_DOMAIN,
            AWS_ACCOUNT_ID,
            AWS_DEFAULT_REGION,
            AWS_ACCESS_KEY_ID,
            AWS_SECRET_ACCESS_KEY,
        ]
        if not all(required_env_vars):
            err_msg = (
                "AWS credentials not found. Please set the environment variables."
                " Alternatively you can set UV_PUBLISH_TOKEN yourself."
            )
            raise ValueError(err_msg)
        domain, domain_owner, region, key_id, secret = required_env_vars
        token = get_codeartifact_token(key_id, secret, domain, domain_owner, region)
        token = token.strip()
        logger.debug("Acquired Publication token from AWS-CLI.")
        os.environ["UV_PUBLISH_TOKEN"] = token


def get_project_version(path_to_pyproject: Path) -> list[Path]:
    """Get the version of of the pyproject.

    Parameters
    ----------
    path_to_pyproject : str | Path
        Path to the folder containing the pyproject.toml file of the project.
    """
    path_to_toml = path_to_pyproject.joinpath(TOML_NAME)
    if not path_to_toml.exists():
        msg = f"pyproject.toml not found in {path_to_pyproject}"
        raise FileNotFoundError(msg)
    with path_to_toml.open("rb") as file:
        pyproject_toml = tomlkit.load(file)
    return pyproject_toml["project"]["version"]


def get_workspace_member_paths(path_to_pyproject: Path) -> list[Path]:
    """Get a list of paths to all workspace members of the project.

    Parameters
    ----------
    path_to_pyproject : str | Path
        Path to the folder containing the pyproject.toml file of the project.
    """
    if path_to_pyproject.is_file() and path_to_pyproject.name == TOML_NAME:
        path_to_toml = path_to_pyproject
    else:
        path_to_toml = path_to_pyproject.joinpath(TOML_NAME)
    if not path_to_toml.exists():
        msg = f"pyproject.toml not found in {path_to_pyproject}"
        raise FileNotFoundError(msg)
    with path_to_toml.open("rb") as file:
        pyproject_toml = tomlkit.load(file)
    workspace_member_globs = pyproject_toml["tool"]["uv"]["workspace"]["members"]
    workspace_members = [
        path_to_pyproject.glob(member) for member in workspace_member_globs
    ]
    workspace_members.append([path_to_pyproject])  # add main project
    return sorted(itertools.chain(*workspace_members))


def prepare_individual_build(
    paths: list[Path],
    main_toml: Path,
    release_variant: Literal["DEV", "ALPHA", "RC", "FINAL"],
) -> None:
    """Prepare the build for individual versioning."""
    msg = "Individual versioning is not supported yet."
    raise NotImplementedError(msg)


def get_dev_meta(last_version: str = "*") -> tuple[str, str]:
    """Create a unique string to attach to each dev version.

    Returns commit_hash and num_commits since last tag
    """
    # Try to find last tag with version from pyproject.toml
    d_str = run(  # nosec
        ["git", "describe", "--match", f"v[0-9]*", "--exclude", "*rc[0-9]*"],
        check=False,
        capture_output=True,
        text=True,
    )
    if d_str.returncode != 0:
        # if that didn't work, just get me any describe str
        d_str = run(  # nosec
            ["git", "describe"],
            check=False,
            capture_output=True,
            text=True,
        )
    if d_str.returncode == 0:
        d_str = d_str.stdout.strip()
        m = re.match(r"^(?P<tag>.*)-(?P<num>\d+)-g(?P<hash>[a-f0-9]*)$", d_str)
        if m is None:
            msg = "Could not parse git describe output. Unable to determine dev version"
            raise ValueError(msg)
        commit_hash, dev_num = m.group("hash", "num")
    else:
        # The last command can still error out if there are no tags at all. In that case
        # we take the current commit hash and assume dev0
        d_str = run(  # nosec
            ["git", "rev-parse", "--short", "HEAD"],
            check=True,
            capture_output=True,
            text=True,
        )
        commit_hash, dev_num = d_str.stdout.strip(), "0"

    return commit_hash, dev_num


def bump_lockstep_dev_release(main_pyproject_toml: Path, paths: list[Path]) -> None:
    """Bump versions of all projects in paths to their next dev version."""
    dev_version, _ = get_dev_version(main_pyproject_toml)
    set_version_in_tomls(paths, dev_version)
    logger.info("Successfully bumped versions to %s", dev_version)


def bump_lockstep_alpha_release(main_pyproject_toml: Path, paths: list[Path]) -> None:
    """Bump versions of all projects in this UV Workspace to their next dev version."""
    alpha_version, _ = get_prerelease_version(main_pyproject_toml, "alpha")
    set_version_in_tomls(paths, alpha_version)
    logger.info("Successfully bumped versions to %s", alpha_version)


def bump_lockstep_rc_release(main_pyproject_toml: Path, paths: list[Path]) -> None:
    """Bump versions of all projects in this UV Workspace to their next version."""
    rc_version, _ = get_prerelease_version(main_pyproject_toml, "rc")
    set_version_in_tomls(paths, rc_version)
    logger.info("Successfully bumped versions to %s", rc_version)


def bump_lockstep_final_release(main_pyproject_toml: Path, paths: list[Path]) -> None:
    """Bump versions of all projects in this UV Workspace to their next version."""
    version, _ = get_final_version(main_pyproject_toml)
    set_version_in_tomls(paths, version)
    logger.info("Successfully bumped versions to %s", version)


def adjust_semantic_versioning_config(
    main_pyproject_toml: Path, paths: list[Path]
) -> None:
    """Adjust the configuration of semantic-version to include UV workspace members."""
    suffix = ":project.version:nf"
    parent = main_pyproject_toml.parent
    members = [str(m.joinpath(TOML_NAME).relative_to(parent)) + suffix for m in paths]
    toml = TOMLFile(main_pyproject_toml)
    pyproject_toml = toml.read()
    pyproject_toml["tool"]["semantic_release"]["version_toml"] = members
    toml.write(pyproject_toml)


def prepare_lockstep_build(
    paths: list[Path],
    main_proj_dir: Path,
    release_variant: Literal["DEV", "ALPHA", "RC", "FINAL"],
) -> None:
    """Prepare the build for individual versioning."""
    main_pyproject_toml = main_proj_dir.joinpath(TOML_NAME)
    logger.info("Preparing %s build. Adjusting verion files ...", release_variant)

    match release_variant:
        case "DEV":
            # currently we need to handle this separately from the semantic-release
            # because we are not tagging dev-releases (since they are automatically
            # pruned. Therefore semantic-release would never increase the dev number
            bump_lockstep_dev_release(main_pyproject_toml, paths)
        case "ALPHA":  # tagged and pushed
            bump_lockstep_alpha_release(main_pyproject_toml, paths)
        case "RC":  # tagged and pushed
            bump_lockstep_rc_release(main_pyproject_toml, paths)
        case "FINAL":  # tagged and pushed
            bump_lockstep_final_release(main_pyproject_toml, paths)
        case _:
            msg = "Wrong Release Variant supplied to lockstep build preparation"
            raise RuntimeError(msg)


def get_module_name(toml_path: str | Path) -> str:
    """Get the module name from a pyproject.toml."""
    toml = TOMLFile(toml_path)
    pyproject_toml = toml.read()
    return pyproject_toml["project"]["name"]


def get_module_version(toml_path: str | Path) -> str:
    """Get the module name from a pyproject.toml."""
    toml = TOMLFile(toml_path)
    pyproject_toml = toml.read()
    return pyproject_toml["project"]["version"]


def check_branch_exists(branch_name: str, include_remote: bool = False) -> bool:
    """Check if a git branch exists locally or remotely."""
    err_msg = f"Branch '{branch_name}' already exists."
    if branch_exists_locally(branch_name) or (
        include_remote and branch_exists_on_remote(branch_name)
    ):
        raise ValueError(err_msg)


def branch_exists_locally(branch_name: str) -> bool:
    """Check if a git branch exists locally."""
    result = run(["git", "rev-parse", "--verify", branch_name], check=False)  # nosec
    return result.returncode == 0


def branch_exists_on_remote(branch_name: str) -> bool:
    """Check if a git branch exists on remote."""
    cmd = f"git ls-remote --exit-code --heads origin {branch_name}"
    result = run(cmd.split(), check=False)  # nosec
    return result.returncode == 0


def get_commit_hash(ref: str) -> str | None:
    """Get the commit hash for a given git reference."""
    try:
        result = run(["git", "rev-parse", ref], capture_output=True, check=True)  # nosec
        return result.stdout.strip().decode()
    except CalledProcessError:
        logger.warning("Could not resolve git reference: %s", ref)
        return None


def get_current_branch() -> str:
    """Get the current git branch."""
    result = run(["git", "branch", "--show-current"], capture_output=True, check=True)  # nosec
    return result.stdout.strip().decode()


def fetch_main() -> None:
    """Fetch the main branch from the remote."""
    logger.info("Fetching %s/%s...", REMOTE_NAME, MAIN_BRANCH)
    run(["git", "fetch", REMOTE_NAME, MAIN_BRANCH], check=True)  # nosec


def is_main_branch_up_to_date() -> bool:
    """Check if the local main branch is up-to-date with the remote."""
    local_main = MAIN_BRANCH
    remote_main = f"{REMOTE_NAME}/{MAIN_BRANCH}"
    try:
        # If diff is empty, the command returns successfully with no output.
        # If there are differences, it returns successfully with output.
        result = run(  # nosec
            ["git", "diff", local_main, remote_main], capture_output=True, check=False
        )
        # Check return code in case branches don't exist or other git errors
        if result.returncode != 0:
            logger.error(
                "Failed to compare %s and %s. Error:\n%s",
                local_main,
                remote_main,
                result.stderr,
            )
            return False  # Treat comparison failure as not up-to-date for safety
        is_synced = not bool(result.stdout.strip())  # No output means they are the same
        if not is_synced:
            logger.warning(
                "Local %s is not up-to-date with %s.", local_main, remote_main
            )
        # Return the actual sync status
        return is_synced
    except CalledProcessError:
        # This shouldn't happen with check=False unless git itself fails badly
        logger.exception("Error running git diff command.")
        return False


def check_main_branch_up_to_date() -> bool:
    """Check if the local main branch is up-to-date with the remote."""
    current_branch = get_current_branch()
    if current_branch != MAIN_BRANCH:
        logger.error(
            "To keep a consistent state, this script must be executed on the '%s' "
            "branch (currently on '%s').",
            MAIN_BRANCH,
            current_branch,
        )
        sys.exit(1)

    fetch_main()
    if not is_main_branch_up_to_date():
        logger.error(
            "Your local '%s' branch is not up-to-date with '%s/%s'. "
            "Please sync it first (e.g., git push/pull).",
            MAIN_BRANCH,
            REMOTE_NAME,
            MAIN_BRANCH,
        )
        sys.exit(1)
    logger.info("Local '%s' is up-to-date with remote.", MAIN_BRANCH)


def get_dev_version(project_root: Path = Path()) -> tuple[str, str]:
    """Get the next development version based on the last release tag.

    Returns
    -------
    tuple[str, str]: The next development version (dev versions are not tagged).
    """
    check_command_exists("git")
    pattern = r"^v?(?P<base_version>(\d+)\.(\d+)\.(\d+)).*?(:?-(?P<dev_num>\d+)-g(?P<git_hash>[a-f0-9]+))?$"
    try:
        git_desc_str = run(
            [
                "git",
                "describe",
                "--match",
                "v[0-9]*",
                "--exclude",
                "*.[0-9]a[0-9]*",  # exclude alpha versions
                "--exclude",
                "*.[0-9]b[0-9]*",  # exclude beta versions
                "--exclude",
                "*.[0-9]rc[0-9]*",  # exclude rc versions
                "--exclude",
                "*.[0-9]post[0-9]*",  # exclude post versions
            ],
            capture_output=True,
            text=True,
            check=True,
            cwd=Path(),
        ).stdout.strip()  # nosec
        match = re.match(pattern, git_desc_str)
        if match is None:
            logger.error("Could not parse git describe output: %s", git_desc_str)
            sys.exit(1)

        base, dev, g_hash = (
            match.group("base_version"),
            match.group("dev_num"),
            match.group("git_hash"),
        )
        next_version = f"{base}.dev{dev}+{g_hash}" if all([base, dev, g_hash]) else base
        return next_version, f"v{next_version}"
    except CalledProcessError as e:
        logger.error("Error finding last tag. Maybe no tag exists? Error: %s", e.stderr)
        sys.exit(1)


def get_prerelease_version(
    project_root: Path = Path(), pr_type: Literal["alpha", "rc"] = "rc"
) -> tuple[str, str]:
    """Get the next pre-release version based on the last release tag.

    Returns
    -------
    tuple[str, str]: The next pre-release version and corresponding tag.
    """
    check_command_exists("cz")
    cmd = f"cz bump --get-next --yes --allow-no-commit --prerelease {pr_type} --prerelease-offset 1"
    try:
        next_version = run(
            cmd.split(),
            capture_output=True,
            text=True,
            check=True,
            cwd=project_root,
        ).stdout.strip()  # nosec
        return next_version, f"v{next_version}"
    except CalledProcessError as e:
        logger.error("Error running commitizen-cli: %s", e.stderr)
        sys.exit(1)


def get_final_version(project_root: Path = Path()) -> tuple[str, str]:
    """Get the next final version based on the last release tag.

    Returns
    -------
    tuple[str, str]: The next final version and corresponding tag.
    """
    check_command_exists("cz")
    try:
        next_version = run(
            ["cz", "bump", "--yes", "--get-next", "--allow-no-commit"],
            capture_output=True,
            text=True,
            check=True,
            cwd=project_root,
        ).stdout.strip()  # nosec
        tag = f"v{next_version}"
        return next_version, tag
    except CalledProcessError as e:
        logger.error("Error running commitizen-cli: %s", e.stderr)
        sys.exit(1)


def bump_version_from_tag(
    increment: Literal["PATCH", "MINOR", "MAJOR"] = "PATCH",
) -> tuple[str, str]:
    """Bump the version based on the last release tag given the increment."""
    check_command_exists("cz")
    if increment not in ["PATCH", "MINOR", "MAJOR"]:
        raise ValueError(
            "Invalid increment type. Must be one of ['PATCH', 'MINOR', 'MAJOR']"
        )
    new_version = run(
        [
            "cz",
            "bump",
            "--get-next",
            "--yes",
            "--allow-no-commit",
            "--increment",
            increment,
        ],
        check=True,
        text=True,
        capture_output=True,
    ).stdout.strip()
    return new_version, f"v{new_version}"


def get_next_semantic_version(
    project_root: Path = Path(),
    release_variant: Literal["DEV", "ALPHA", "RC", "FINAL"] = "FINAL",
) -> tuple[str, str]:
    """
    Get the next semantic version based on the last release and commit history.

    Returns
    -------
        str: The next semantic version based on the commit history and the corresponding tag.
    """
    check_command_exists("cz")  # check if commitizen-cli exists
    match release_variant:
        case "DEV":
            # currently we need to handle this separately from the semantic-release
            # because we are not tagging dev-releases (since they are automatically
            # pruned. Therefore semantic-release would never increase the dev number
            version, tag = get_dev_version(project_root)
        case "ALPHA":  # tagged and pushed
            version, tag = get_prerelease_version(project_root, "alpha")
        case "RC":  # tagged and pushed
            version, tag = get_prerelease_version(project_root, "rc")
        case "FINAL":  # tagged and pushed
            version, tag = get_final_version(project_root)
        case _:
            msg = "Wrong Release Variant supplied to lockstep build preparation"
            raise RuntimeError(msg)
    return version, tag


def create_changelog(project_root: Path = Path()) -> None:
    """Creates a new Changelog for the project.

    Parameters
    ----------
    project_root : Path, optional
        Workspace root, by default Path()
    """
    logger.info("Amending Changelog...")
    logger.warning("Not implemented yet.")


def check_tag_exists(tag_name: str, include_remote: bool = False) -> bool:
    """Raise RuntimeError if a given Git tag exists in the repository."""
    if tag_exists(tag_name, include_remote):
        raise RuntimeError(f"Tag '{tag_name}' already exists.")


def tag_exists(tag_name: str, include_remote: bool = False) -> bool:
    """
    Checks if a specific Git tag exists in the repository.

    Uses 'git rev-parse --verify --quiet <tag_name>' and checks the exit code.

    Args:
        tag_name (str): The exact name of the tag to check.
        include_remote (bool): Check also if the tag exists remotely.

    Returns
    -------
        bool: True if the tag exists, False otherwise (including errors).
    """
    if not tag_name:
        print("Error: tag_name cannot be empty.", file=sys.stderr)
        return False
    try:
        # Use 'git rev-parse --verify --quiet' which exits with 0 if the ref exists,
        # and non-zero otherwise. --quiet suppresses output on success/failure.
        git_command = ["git", "rev-parse", "--verify", "--quiet", tag_name]

        # Execute the command. We only care about the return code.
        # stdout and stderr are suppressed by --quiet, but capture anyway for safety.
        local_exists = (
            run(  # nosec
                git_command,
                stdout=PIPE,
                stderr=PIPE,
                check=False,  # Do not raise error on non-zero exit, we check returncode manually
            ).returncode
            == 0
        )

        remote_exists = False
        if include_remote:
            # If the tag exists locally, check if it also exists remotely
            git_command = f"git ls-remote --exit-code --tags {REMOTE_NAME} {tag_name}"
            remote = run(git_command.split(), stdout=PIPE, stderr=PIPE, check=False)  # nosec
            remote_exists = remote.returncode == 0

        # Return True if the command succeeded (exit code 0), False otherwise.
        return local_exists or remote_exists

    except FileNotFoundError:
        # Handle case where 'git' command is not found
        print(
            "Error: 'git' command not found. Please ensure Git is installed and in your PATH.",
            file=sys.stderr,
        )
        return False
    except Exception as e:
        # Catch any other unexpected errors during subprocess execution
        print(
            f"An unexpected error occurred during git rev-parse: {e}", file=sys.stderr
        )
        return False


def create_release_commit_and_push(
    paths: str | Path, version_str: str, workspace_root: Path = Path()
) -> None:
    """Create new commit with version changes and push to remote."""
    logger.debug("Adding changed toml files to staging area ...")
    paths = list(map(Path, paths))  # ensure all paths are of type path
    paths = [p if p.name == TOML_NAME else p.joinpath(TOML_NAME) for p in paths]
    changelog = workspace_root / "CHANGELOG.md"
    if changelog.exists() and changelog.is_file():
        paths.append(changelog)
    for toml_path in filter(lambda p: p.exists() and p.is_file(), paths):
        try:
            run(  # nosec
                ["git", "add", str(toml_path.resolve())],
                check=True,
                stderr=sys.stderr,
                stdout=sys.stdout,
            )
        except CalledProcessError as e:
            logger.warning("Error adding %s. Skipping. %s", toml_path, e)
            continue
    logger.debug("Committing changes to pyproject.toml files ...")
    module_name = get_module_name(Path(TOML_NAME))
    run(  # nosec
        [
            "git",
            "commit",
            "-m",
            f"release: {module_name} v{version_str}",
            "--no-verify",
        ],
        check=True,
        stderr=sys.stderr,
        stdout=sys.stdout,
    )
    run(["git", "push"], check=True, stderr=sys.stderr, stdout=sys.stdout)  # nosec


def create_release_tag_and_push(
    version_str: str, commit_hash: str | None = None
) -> None:
    """Create a tag with the given version string and push to remote.

    Parameters
    ----------
    version_str : str
        Version string (e.g. v1.2.3)
    commit_hash : Optional[str]
        Use the commit hash to create an annotated tag. Defaults to latest commit.
    """
    check_tag_exists(version_str)
    logger.debug("Creating annotated git tag(s) ...")
    tag_cmd = f"git tag -a v{version_str} -m v{version_str}"
    tag_cmd += f" {commit_hash}" if commit_hash else ""
    run(  # nosec
        tag_cmd.split(" "),
        check=True,
        stderr=sys.stderr,
        stdout=sys.stdout,
    )
    logger.debug("Pushing changes to remote ...")
    run(  # nosec
        ["git", "push", "origin", "tag", f"v{version_str}"],
        check=True,
        stderr=sys.stderr,
        stdout=sys.stdout,
    )


def get_most_recent_tag(
    pattern: str | None = None, exclude: str | None = None
) -> str | None:
    """
    Retrieves the most recent Git tag on the current branch, optionally matching a pattern.

    Uses the 'git describe --tags --abbrev=0' command, potentially with '--match <pattern>'
      and/or '--exclude <pattern>'.

    Args:
        pattern (str | None, optional): A glob pattern to filter tags.
            If None, the most recent tag regardless of name is returned.
            Defaults to None.

    Returns
    -------
        str: The name of the most recent matching tag if found.
        None: If no matching tags are found, not in a Git repository,
              Git is not installed, or any other error occurs.
    """
    try:
        git_command = ["git", "describe", "--tags", "--abbrev=0"]
        if pattern:
            git_command.extend(["--match", pattern])
        if exclude:
            git_command.extend(["--exclude", pattern])
        # Execute the git command to get the most recent tag
        # --tags: Ensures any kind of tag is considered (lightweight or annotated)
        # --abbrev=0: Removes the commit hash suffix, giving just the tag name
        result = run(  # nosec
            git_command,
            stdout=PIPE,  # Capture standard output
            stderr=PIPE,  # Capture standard error
            text=True,  # Decode output/error as text (Python 3.7+)
            check=True,  # Raise CalledProcessError if command returns non-zero exit code
        )
        # Strip any leading/trailing whitespace (like newline characters)
        tag = result.stdout.strip()
        return tag if tag else None  # Return tag if not empty, otherwise None

    except FileNotFoundError:
        print(
            "Error: 'git' command not found. Please ensure Git is installed and in your PATH.",
            file=sys.stderr,
        )
        return None
    except CalledProcessError as e:
        error_message = e.stderr.strip()
        if "No names found" in error_message:
            print(
                "Info: No tags found in the repository reachable from the current commit.",
                file=sys.stderr,
            )
        elif "not a git repository" in error_message:
            print("Error: Not currently in a Git repository.", file=sys.stderr)
        else:
            print(f"Error executing git command: {error_message}", file=sys.stderr)
        return None
    except Exception as e:
        print(f"An unexpected error occurred: {e}", file=sys.stderr)
        return None


def set_version_in_tomls(paths: list[str | Path], version_str: str) -> None:
    """Set {version} in all provided pyproject.toml paths."""
    paths = list(map(Path, paths))  # ensure all paths are of type path
    paths = [p if p.name == TOML_NAME else p.joinpath(TOML_NAME) for p in paths]
    for toml_path in filter(lambda p: p.exists() and p.is_file(), paths):
        try:
            toml = TOMLFile(toml_path)
            pyproject_toml = toml.read()
            pyproject_toml["project"]["version"] = version_str
            toml.write(pyproject_toml)
        except Exception as e:  # noqa: BLE001
            logger.warning("Error writing version to %s. Skipping. %s", toml_path, e)
            continue


def get_deps_csv_from_codeartifacts(package_name: str, version: str) -> str:
    import tempfile

    AWS_USER = "aws"
    get_token_cmd = (
        f"aws codeartifact get-authorization-token "
        f"--domain {CODEARTIFACT_DOMAIN} --domain-owner {AWS_ACCOUNT_ID} "
        f"--region {AWS_DEFAULT_REGION} --query authorizationToken --output text"
    )
    aws_token = run(  # nosec
        get_token_cmd.split(), check=True, text=True, capture_output=True
    ).stdout.strip()
    index_str = f"https://{AWS_USER}:{aws_token}@{CODEARTIFACT_DOMAIN}-{AWS_ACCOUNT_ID}.d.codeartifact.{AWS_DEFAULT_REGION}.amazonaws.com/pypi/{CODEARTIFACT_REPO}/simple/"
    with tempfile.NamedTemporaryFile(delete_on_close=False) as fp:
        requirement = f"{package_name}=={version}"
        fp.write(requirement.encode())
        fp.close()
        get_deps_cmd = f"uv pip compile -q --universal --no-annotate --no-header --extra-index-url {index_str} {fp.name}"
        dep_str = run(  # nosec
            get_deps_cmd.split(), check=True, capture_output=True, text=True
        ).stdout.strip()
        deps = [d for d in dep_str.split("\n")]
        deps_csv = ",".join(deps)
        return deps_csv


@dataclass
class BitbucketVariable:
    key: str
    value: str
    secured: bool = False  # Optional: set to True if the variable is secured


def trigger_bitbucket_pipeline(
    repo_owner: str,
    repo_slug: str,
    branch_name: str,
    pipeline_pattern: str,
    variables: list[BitbucketVariable],
) -> None:
    """Triggers the Bitbucket deployment pipeline via the API.

    Requires the environment variable BB_REMOTE_ACCESS_TOKEN to be set.
    """
    # --- Configuration from Environment Variables ---
    if not BB_REMOTE_ACCESS_TOKEN:
        logger.error(
            f"Error: Missing required environment variable BB_REMOTE_ACCESS_TOKEN. "
            "This variable is required to authenticate with the remote Bitbucket server."
        )
        sys.exit(1)  # Exit with an error code

    # --- API Request Details ---
    api_url = f"https://api.bitbucket.org/2.0/repositories/{repo_owner}/{repo_slug}/pipelines/"
    headers = {
        "Authorization": f"Bearer {BB_REMOTE_ACCESS_TOKEN}",
        "Content-Type": "application/json",
        "Accept": "application/json",
    }
    payload = {
        "target": {
            "type": "pipeline_ref_target",
            "ref_type": "branch",
            "ref_name": branch_name,
            "selector": {"type": "custom", "pattern": pipeline_pattern},
        },
        "variables": [asdict(v) for v in variables],
    }

    # --- Make the API Request ---
    try:
        logger.info(
            f"Attempting to trigger pipeline '{pipeline_pattern}' on branch '{branch_name}' in repo '{repo_owner}/{repo_slug}'..."
        )
        response = requests.post(
            api_url, headers=headers, json=payload, timeout=15
        )  # Use json=payload to automatically serialize and set content-type

        # --- Handle Response ---
        logger.info(f"API Response Status Code: {response.status_code}")
        try:
            # Try to print JSON response if available
            logger.info("API Response Body:")
            logger.info(json.dumps(response.json(), indent=2))  # Pretty print JSON
        except json.JSONDecodeError:
            # If response is not JSON, print raw text
            logger.info("API Response Body (non-JSON):")
            logger.info(response.text)

        # Raise an exception for bad status codes (4xx or 5xx)
        response.raise_for_status()
        print("Pipeline triggered successfully!")

    except requests.exceptions.RequestException as e:
        print(f"\nError making API request: {e}", file=sys.stderr)
        sys.exit(1)  # Exit with an error code
    except Exception as e:
        print(f"\nAn unexpected error occurred: {e}", file=sys.stderr)
        sys.exit(1)  # Exit with an error code


def trigger_deploy_pipeline(
    version: str,
    environment: Literal[
        "CUJU-AI-Integration", "CUJU-AI-Test", "CUJU-AI-Production"
    ] = "CUJU-AI-Integration",
) -> None:
    """Triggers the CUJU-AI-Inference Repo deployment pipeline via the API.

    Requires the environment variable BB_REMOTE_ACCESS_TOKEN to be set.
    """
    branch_name = "main"
    pipeline_pattern = "update_project_dependencies"
    repo_owner = "rogontechnologies"
    repo_slug = "cuju-ai-inference-server"
    variables = [
        BitbucketVariable("ENVIRONMENT", environment),
        BitbucketVariable("VERSION", version),
    ]
    trigger_bitbucket_pipeline(
        repo_owner, repo_slug, branch_name, pipeline_pattern, variables
    )


def prepare_build(
    release_variant: Literal["DEV", "ALPHA", "RC", "FINAL"],
    versioning: Literal["lockstep", "individual"],
    paths: list[str],
    main_toml: Path,
) -> None:
    """Prepare a build of all cuju plugins.

    Parameters
    ----------
    versioning : Literal['lockstep', 'individual']
        The type of release.
          - lockstep: the versions of all plugins will will be set to the same value
                determined by the commits of the whole repository
          - individual: every plugin will receive its own version number, determined
                by the commit messages that changed the plugin folder
    paths : list[str]
        List of paths to the cuju plugin folders (must contain pyproject.toml).
    """
    # stash all local changes before touching any files
    prep_stategies = {
        "lockstep": prepare_lockstep_build,
        "individual": prepare_individual_build,
    }
    prep_strategy = prep_stategies.get(versioning)
    if not prep_strategy:
        msg = f"Invalid release variant: {versioning}"
        raise ValueError(msg)

    prep_strategy(paths, main_toml, release_variant)


def build_all_packages(main_project_path: Path) -> None:
    """Build all packages of the UV Workspace."""
    dist_path = main_project_path.joinpath("dist")
    whls = list(dist_path.rglob("*.whl"))
    srcs = list(dist_path.rglob("*.tar.gz"))
    logger.debug("Checking for existing distribution files in dist/ folder ...")
    dirty_dist_folder = dist_path.exists() and (whls or srcs)
    if dirty_dist_folder:
        logger.warning(
            "Existing distribution files found in the dist/ folder. "
            "This directory has to be empty before building. Do you want to delete the"
            "*.whl and *.tar.gz files in dist/ and continue?"
        )
        if input("yes/no: ").lower() != "yes":
            msg = "Aborting build because of dirty dist folder."
            raise InterruptedError(msg)
        logger.info("Deleting existing wheels and sources")
        for file in [*whls, *srcs]:
            file.unlink()

    logger.debug("The dist/ folder is clean.")
    logger.info("Building all packages ...")
    run(  # nosec
        ["uv", "build", "--all-packages"],
        check=True,
        stdout=sys.stdout,
        stderr=sys.stderr,
    )
    logger.info("Done Building all packages.")


def pin_package_requirements(requirements: list[str], package_name: str) -> None:
    """Add pinned requirements to the given package.

    Requires UV to be installed.

    Parameters
    ----------
    requirements : list[str]
        List of package requirements to add.
    package_name : str
        Name of the package for which to generate pinned requirements.
    """
    check_command_exists("uv")
    with NamedTemporaryFile() as f:
        f.write("\n".join(requirements).encode())
        add_cmd = f"uv add --no-sync --package {package_name} -r {f.name}"
        run(add_cmd.split(), check=True, text=True)  # nosec


def get_pinned_package_requirements(
    package_name: str, project_root: Path = Path()
) -> list[str]:
    """Generate list of pinned package requirements for the given package from uv.lock.

    Requires UV to be installed.

    Parameters
    ----------
    package_name : str
        Name of the package for which to generate pinned requirements.
    project_root : Path, optional
        Root of the Project containing the workspace-pyproject.toml, by default Path()

    Returns
    -------
    list[str]
        List of required package versions in requirements.txt format.
    """
    check_command_exists("uv")
    cmd = f"uv export --package {package_name} --frozen --no-dev --no-hashes --no-annotate --no-header"
    req_str = run(cmd.split(), check=True, capture_output=True, text=True).stdout
    requirements = list(map(str.strip, req_str.split("\n")))

    req_out = []
    for requirement in requirements:
        if not requirement.startswith("-e"):
            # for non-editable requirements, simply add them to the output list
            req_out.append(requirement)
            continue

        # for editable requirements, extract the name and version and add it to the output list
        toml_path = project_root / Path(requirement.lstrip("-e ")) / "pyproject.toml"
        name, version = get_module_name(toml_path), get_module_version(toml_path)
        # No self-reference allowed
        if name == package_name:
            continue

        req_out.append(f"{name}=={version}")
    return req_out


def get_all_services(workspace_root: Path = Path()) -> list[str]:
    """Get module names of services (inside the 'services'-folder) in the workspace."""
    members = get_workspace_member_paths(workspace_root)
    service_paths = (
        m for m in members if m.is_relative_to(workspace_root / "services")
    )
    service_toml_paths = (m / "pyproject.toml" for m in service_paths)
    service_names = [get_module_name(sp) for sp in service_toml_paths]
    return service_names


@overload
def pin_deps_of_package_in_toml(
    package_name: list[str] = ["cuju-e2e-service"], workspace_root: Path = Path()
) -> None: ...


@overload
def pin_deps_of_package_in_toml(
    package_name: str = "cuju-e2e-service", workspace_root: Path = Path()
) -> None: ...


def pin_deps_of_package_in_toml(
    package_name: str | list[str] = ["cuju-e2e-service"], workspace_root: Path = Path()
) -> None:
    """Pin all dependencies of {package} in it's pyproject.toml."""
    if not isinstance(package_name, Iterable):
        package_name = [package_name]

    for package in package_name:
        requirements = get_pinned_package_requirements(package, workspace_root)
        pin_package_requirements(requirements, package)


def pin_all_service_dependencies(workspace_root: Path = Path()) -> None:
    """Pin all dependencies of all services in the workspace."""
    logger.info("Pinning all service dependencies ...")
    services = get_all_services(workspace_root)
    logger.debug("Found services: %s", ", ".join(services))
    pin_deps_of_package_in_toml(services, workspace_root)
    logger.info("Successfully pinned dependencies for %s", ", ".join(services))


def prune_codeartifacts_package(pkg_name: str, version: str) -> None:
    """Prune a version of a CodeArtifacts Package."""
    config = PruneConfig(
        prune_packages=[pkg_name],
        prune_versions=[version],
        dev=True,  # we should not do automatic pruning on final versions
        interactive_mode=False,
    )
    repo = AwsRepo("cuju-ai-workbench", "cuju-ai")
    prune_codeartifacts(repo, config)


def publish_all_packages(main_toml: str | Path, plugin_paths: list[Path]) -> None:
    """Publish all packages using UV."""
    logger.info("Publishing all packages ...")
    try:
        run(  # nosec
            ["uv", "publish", "--index", "cuju-ai-workbench"],
            check=True,
            stdout=sys.stdout,
            stderr=sys.stderr,
        )
    except Exception as exc:
        logger.exception("Failed to publish packages: %s")
        logger.info("Rolling back already published packages ...")
        for plugin_path in plugin_paths:
            plugin_name = get_module_name(plugin_path)
            plugin_version_str = get_module_version(plugin_path)
            version_match = parse_version_str(plugin_version_str)
            if version_match is None:
                msg = (
                    f"Version '{plugin_version_str}' of plugin {plugin_name} is "
                    "not PEP440 compliant."
                )
                raise ValueError(msg) from exc
            if not any(version_match.group(g) for g in ["dev", "post", "local", "pre"]):
                import inspect

                script_path = Path(
                    inspect.getsourcefile(prune_codeartifacts)
                ).relative_to(Path())
                msg = (
                    "No automatic pruning for non-pre-release versions. Please run"
                    f" the '{script_path}' script locally in "
                    "--interactive mode to ensure safe removal of prod packages."
                )
                raise RuntimeError(msg) from exc

            logger.info("Rolling back: %s", plugin_path)
            try:
                prune_codeartifacts_package(plugin_name, plugin_version_str)
            except Exception:
                logger.exception("Failed to rollback plugin at '%s'", plugin_path)
    logger.info("Done Publishing all packages.")


def setup_logging(log_level: int = logging.INFO) -> None:
    """Set up default logger."""
    blue = "\x1b[1;34m"
    reset = "\x1b[0m"
    logging.basicConfig(
        format=blue
        + "%(asctime)s | %(levelname)-8s | %(filename)s:%(lineno)d "
        + reset
        + "%(message)s",
        datefmt="%m/%d/%Y %I:%M:%S",
        level=log_level,
    )

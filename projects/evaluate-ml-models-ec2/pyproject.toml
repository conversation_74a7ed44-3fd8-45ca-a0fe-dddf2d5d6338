[project]
name = "evaluate-ml-models-ec2"
version = "0.0.1"
description = "Evaluation of machine learning models on ec2."
authors = [
    { name = "Ayn<PERSON>", email = "<EMAIL>" },
    { name = "<PERSON><PERSON><PERSON>", email = "<EMAIL>" },
    { name = "<PERSON><PERSON>", email = "<EMAIL>" },
    { name = "<PERSON>", email = "Felix.Saw<PERSON>@mail.schwarz" },
    { name = "He<PERSON> Aladdin", email = "<EMAIL>" },
    { name = "<PERSON>", email = "<EMAIL>" },
    { name = "<PERSON>", email = "<EMAIL>" },
    { name = "<PERSON>", email = "<EMAIL>" },
    { name = "<PERSON><PERSON>", email = "<PERSON><PERSON><PERSON>@inovex.de" },
    { name = "<PERSON>", email = "<PERSON><PERSON>@inovex.de" },
    { name = "<PERSON><PERSON>", email = "<EMAIL>" },
    { name = "<PERSON>n", email = "<PERSON>.<PERSON><PERSON><EMAIL>" },
    { name = "Sundaresh <PERSON>krishnaiah", email = "<EMAIL>" },
]
requires-python = ">=3.12,<3.13"
readme = "README.md"
license = "tbd"
dependencies = [
    "imgeta",
    "botocore==1.34.161",
    "torch",
    "cuju-block-image-overlayer",
    "cuju-block-image-transformer",
    "cuju-con-localstorage",
    "cuju-con-s3storage",
    "cuju-data-detection",
    "cuju-data-ops",
    "cuju-data-pose",
    "cuju-in-batcher",
    "cuju-in-input",
    "cuju-inference-onnx",
    "cuju-metrics",
    "cuju-out-logger",
    "cuju-out-sink",
    "cuju-prov-provider",
    "cuju-srv-web",
    "cuju-con-dataloop",
    "cuju-inference-detectron",
    "cuju-inference-masa",
    "dtlpy",
]

[tool.uv]
package = false

[tool.uv.sources]
imgeta = { path = "../../imgeta", editable = true }
cuju-block-image-overlayer = { path = "../../components/cuju/cuju_block_image_overlayer", editable = true }
cuju-block-image-transformer = { path = "../../components/cuju/cuju_block_image_transformer", editable = true }
cuju-con-localstorage = { path = "../../components/cuju/cuju_con_localstorage", editable = true }
cuju-con-s3storage = { path = "../../components/cuju/cuju_con_s3storage", editable = true }
cuju-data-detection = { path = "../../components/cuju/cuju_data_detection", editable = true }
cuju-data-ops = { path = "../../components/cuju/cuju_data_ops", editable = true }
cuju-data-pose = { path = "../../components/cuju/cuju_data_pose", editable = true }
cuju-in-batcher = { path = "../../components/cuju/cuju_in_batcher", editable = true }
cuju-in-input = { path = "../../components/cuju/cuju_in_input", editable = true }
cuju-inference-onnx = { path = "../../components/cuju/cuju_inference_onnx", editable = true }
cuju-metrics = { path = "../../components/cuju/cuju_metrics", editable = true }
cuju-out-logger = { path = "../../components/cuju/cuju_out_logger", editable = true }
cuju-out-sink = { path = "../../components/cuju/cuju_out_sink", editable = true }
cuju-prov-provider = { path = "../../components/cuju/cuju_prov_provider", editable = true }
cuju-srv-web = { path = "../../components/cuju/cuju_srv_web", editable = true }
cuju-inference-detectron = { path = "../../components/cuju/cuju_inference_detectron", editable = true }
cuju-inference-masa = { path = "../../components/cuju/cuju_inference_masa", editable = true }

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

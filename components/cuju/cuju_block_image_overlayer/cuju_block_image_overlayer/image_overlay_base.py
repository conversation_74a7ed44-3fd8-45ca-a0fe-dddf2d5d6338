"""Base class for image overlay objects.

This plugin contains:
- ImageOverlayBase: base class for image overlay;
"""

from __future__ import annotations

from abc import abstractmethod
from typing import TYPE_CHECKING

from imgeta.plugin_base.bases.imgeta_object_base import ImgetaObjectBase

if TYPE_CHECKING:
    import numpy as np
    from imgeta.plugin_base.imgeta_tree.imgeta_node import ImgetaNodeBase


class ImageOverlayBase(ImgetaObjectBase):
    """Base class for image overlay objects.

    Attributes
    ----------
    ids : str
        ID string of the object type created.

    Raises
    ------
    NotImplementedError
        if the `draw` method is not implemented by the derived class.

    """

    def __init__(self, ids: str | None = None) -> None:
        """Initialize the image overlay base object."""
        super().__init__(ids=ids)

    @abstractmethod
    def draw(self, img: np.ndarray, imgeta_node: ImgetaNodeBase) -> None:
        """Abstract method that draws the overlay on the image.

        Attributes
        ----------
            img : np.ndarray
                image the overlay is applied to (note: is changed by the function)
            imgeta_node : ImgetaNodeBase
                node containing the overlay result

        Notes
        -----
        This method needs to be implemented by the derived class.

        """
        raise NotImplementedError

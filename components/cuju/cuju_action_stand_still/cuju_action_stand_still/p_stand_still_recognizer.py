"""Stand Still Action Recognizer."""

from __future__ import annotations

from typing import TYPE_CHECKING

from cuju_data_scene.actions.stand_still_action import StandStillAction
from cuju_data_scene.constants import EntityRole
from cuju_data_scene.entities.person import Person
from cuju_data_scene.entities.tracked_entity import ToPointMethod
from cuju_data_scene.p_scene_registry import MatchingCriteria, SceneRegistry
from cuju_data_scene.scene_exceptions import NoStartPositionError
from cuju_data_scene.scene_node import SceneNode
from imgeta.pipeline.hook_markers import hook_impl
from imgeta.plugin_base.bases.config_base import ConfigBase
from imgeta.plugin_base.builder.object_builder import ObjectBuilder
from loguru import logger as logging

if TYPE_CHECKING:
    import pandas as pd


class StandStillActionRecognizerConfig(ConfigBase):
    """Configuration for StandStillActionRecognizer.

    Attributes.
    ----------
    key_start_frame: str
        The key for the start frame as received from backend.
    min_frames_for_standstill: int
        The minimum number of frames required for standstill.
    only_until_start_frame: bool
        If True, only consider frames until the start frame.
    px_movement_threshold: float
        The threshold for movement in pixels.
    """

    key_start_frame: str = "start_frame"
    min_frames_for_standstill: int = 10
    only_until_start_frame: bool = True
    px_movement_threshold: float = 5.0


class StandStillActionRecognizer(SceneNode):
    """Stand Still Action.

    Attributes
    ----------
    _cfg : StandStillActionConfig
        The Configuration object.
    """

    def __init__(
        self,
        config: StandStillActionRecognizerConfig,
        **kwargs: dict,
    ) -> None:
        """Initialize RunStartAction."""
        super().__init__(
            ids=config.ids,
            state_storage=config.state_storage,
            on_control_flag=config.on_control_flag,
            **kwargs,
        )
        self._cfg = config

        self._frame_until = None

    def _understand_scene(
        self,
        scene_registry: SceneRegistry,
    ) -> list[None]:
        """Apply the StandStillAction recognizer."""
        if self._state_storage is None and self._frame_until is None:
            msg = "Invalid or no start frame provided to StandStillActionRecognizer."
            raise RuntimeError(msg)
        if self._state_storage is not None:
            self._frame_until = self._state_storage.get_state(self._cfg.key_start_frame)
        mp = scene_registry.retrieve_entity(
            criteria=EntityRole.MAIN_PERSON,
            matching_criteria=MatchingCriteria.ROLE,
            entity_type=Person,
        )
        if mp is None:
            msg = "No main person found in scene registry. skipping stand-still."
            logging.warning(msg)
            return [None]

        action = self._create_stand_still_action(mp)
        if action is not None:
            scene_registry.upsert_action(
                action,
            )
        else:
            msg = "No Stand still action was found."
            logging.error(msg)
            raise NoStartPositionError

        return [None]

    def _create_stand_still_action(
        self,
        mp: Person,
    ) -> StandStillAction | None:
        """Create a StandStillAction."""
        if self._cfg.only_until_start_frame and self._frame_until is not None:
            start_frame = self._frame_until
        else:
            start_frame = mp.max_frame_number

        traj = mp.get_points_as_df(
            keypoints=["left_ankle", "right_ankle"],
            frame_range=(0, start_frame),
            conversion_method=ToPointMethod.KPT_CENTER,
        )["x"]
        # difference between each frame
        traj_diff = traj.diff().abs()
        movement_mask: pd.DataFrame = (
            traj_diff < self._cfg.px_movement_threshold
        ).astype(int)
        frame_wise_standstill_df = movement_mask.rolling(
            window=self._cfg.min_frames_for_standstill
        ).sum()
        frames_still = frame_wise_standstill_df == self._cfg.min_frames_for_standstill
        if frames_still.sum() == 0:
            return None
        still_start_frame = (
            frame_wise_standstill_df[frames_still].reset_index().iloc[0]["frame"]
        )
        return StandStillAction(
            time=mp.frame_time[mp.get_frame_index(still_start_frame)],
            start_frame=still_start_frame,
            position=mp.to_point(still_start_frame),
            entity_id=mp.id,
        )

    @staticmethod
    @hook_impl
    def node_register() -> None:
        """Register a StandStillActionRecognizer."""
        ObjectBuilder.direct_register(
            id_str="StandStillActionRecognizer",
            object_type=(
                "StandStillActionRecognizer",
                "cuju_action_stand_still.p_stand_still_recognizer",
            ),
            config_type=(
                "StandStillActionRecognizerConfig",
                "cuju_action_stand_still.p_stand_still_recognizer",
            ),
        )

"""Unit Tests for Jump Action Recognizer."""

from copy import deepcopy
from unittest.mock import MagicMock, patch

import numpy as np
import pandas as pd
import pytest
from cuju_action_jump.p_jump_recognizer import (
    JumpRecognizer,
    JumpRecognizerConfig,
)
from cuju_data_scene.actions.filter import SceneActionFilter
from cuju_data_scene.actions.jump_action import JumpEndAction, JumpStartAction
from cuju_data_scene.actions.setup_checked_action import SetupCheckedAction
from cuju_data_scene.constants import EntityRole
from cuju_data_scene.entities.direction import Direction
from cuju_data_scene.entities.person import Person
from cuju_data_scene.entities.tracked_entity import ToPointMethod
from cuju_data_scene.p_scene_registry import MatchingCriteria, SceneRegistry
from imgeta.pipeline.hook_markers import PLUGIN_NAMESPACE
from imgeta.pipeline.hook_specs import HookSpecs
from imgeta.plugin_base.builder.object_builder import ObjectBuilder
from pluggy import PluginManager


@pytest.mark.unittest
class TestJumpRecognizer:
    """Test the logic of Jump Action Recognizer."""

    def test_extract_positions_from_person(
        self,
        jumping_person: tuple[
            Person, tuple[float, float], tuple[float, float], int, int, bool, float
        ],
    ) -> None:
        """Test the data extraction logic of Jump Action Recognizer."""
        person, _, _, _, end_frame, _, _ = jumping_person
        df = JumpRecognizer.extract_positions_from_person(person, start_frame=-1)

        if df.shape[0] != end_frame + 8:
            pytest.fail(
                f"Not all data was extracted properly. Expected {end_frame + 9} "
                f"records, Got: {df.shape[0]}"
            )
        if not np.allclose(
            df["x"],
            person.to_points(ToPointMethod.KPT_NAME, keypoints=["left_ankle"])[:, 0],
        ):
            pytest.fail("x Position not extracted correctly.")

        if not np.allclose(
            df["y"],
            person.to_points(ToPointMethod.KPT_NAME, keypoints=["left_ankle"])[:, 1],
        ):
            pytest.fail("x Position not extracted correctly.")

        if not all(df["frame"] == person.frame_numbers):
            pytest.fail("Frames not extracted correctly.")

    @pytest.mark.parametrize("direction", [Direction.LTR, Direction.RTL])
    def test_recognize_jump(
        self,
        jump_recognizer_config: JumpRecognizerConfig,
        jumping_person: tuple[
            Person, tuple[float, float], tuple[float, float], int, int, bool, float
        ],
        direction: Direction,
    ) -> None:
        """Test the jump detection logic for both LTR and RTL directions."""
        person, exp_start_pos, exp_end_pos, exp_start, exp_end, _, _ = jumping_person
        jump_recognizer_config.window_offset = 1
        scene_registry = SceneRegistry()
        scene_registry.upsert_entity(person, matching_criteria=MatchingCriteria.ROLE)
        state = MagicMock()
        state.get_state.return_value = 0
        jump_start, jump_end = JumpRecognizer.recognize_jump(
            scene_registry=scene_registry,
            config=jump_recognizer_config,
            direction=direction,
            state=state,
        )

        if not isinstance(jump_start, JumpStartAction):
            pytest.fail("JumpStartAction not recognized.")
        if not isinstance(jump_end, JumpEndAction):
            pytest.fail("JumpEndAction not recognized.")
        if jump_start.position != exp_start_pos:
            pytest.fail("JumpStartAction position is incorrect.")
        if jump_start.time.frame_number != exp_start:
            pytest.fail(
                f"JumpStartAction time wrong. Expected {exp_start}, "
                f"Got: {jump_start.time.frame_number}"
            )
        if jump_end.position != exp_end_pos:
            pytest.fail("JumpEndAction position is incorrect.")
        if jump_end.time.frame_number != exp_end:
            pytest.fail(
                f"JumpEndAction time wrong. Expected {exp_end}, "
                f"Got: {jump_end.time.frame_number}"
            )

    @pytest.mark.parametrize(
        ("direction", "is_jump_start", "expected_position"),
        [
            (Direction.LTR, True, (20.0, 30.0)),  # Jump start: always midpoint
            (Direction.RTL, True, (20.0, 30.0)),  # Jump start: always midpoint
            (Direction.LTR, False, (10.0, 20.0)),  # Jump end LTR: left ankle (rear)
            (Direction.RTL, False, (30.0, 40.0)),  # Jump end RTL: right ankle (rear)
        ],
    )
    def test_get_ankle_position_for_action(
        self,
        direction: Direction,
        *,
        is_jump_start: bool,
        expected_position: tuple[float, float],
    ) -> None:
        """Test ankle position selection for different directions and action types."""
        df_row = pd.Series(
            {
                "x_left_ankle": 10.0,
                "y_left_ankle": 20.0,
                "x_right_ankle": 30.0,
                "y_right_ankle": 40.0,
            }
        )

        actual_position = JumpRecognizer._get_ankle_position_for_action(  # noqa: SLF001
            df_row, direction, is_jump_start=is_jump_start
        )

        if actual_position != expected_position:
            msg = (
                f"Position incorrect for {direction} "
                f"{'start' if is_jump_start else 'end'}: "
                f"{actual_position} != {expected_position}"
            )
            pytest.fail(msg)

    def test_get_ankle_position_for_action_jump_end(self) -> None:
        """Test ankle position selection for jump end action (should be rear ankle)."""
        df_row = pd.Series(
            {
                "x_left_ankle": 10.0,
                "y_left_ankle": 20.0,
                "x_right_ankle": 30.0,
                "y_right_ankle": 40.0,
            }
        )

        # For LTR jump end, should return left ankle (rear ankle - smaller x)
        pos_ltr = JumpRecognizer._get_ankle_position_for_action(  # noqa: SLF001
            df_row, Direction.LTR, is_jump_start=False
        )
        expected_ltr = (10.0, 20.0)

        # For RTL jump end, should return right ankle (rear ankle - larger x)
        pos_rtl = JumpRecognizer._get_ankle_position_for_action(  # noqa: SLF001
            df_row, Direction.RTL, is_jump_start=False
        )
        expected_rtl = (30.0, 40.0)

        if pos_ltr != expected_ltr:
            pytest.fail(f"LTR jump end position incorrect: {pos_ltr} != {expected_ltr}")
        if pos_rtl != expected_rtl:
            pytest.fail(f"RTL jump end position incorrect: {pos_rtl} != {expected_rtl}")

    def test_understand_scene_no_mainperson(
        self, jump_recognizer_config: JumpRecognizerConfig, no_main_person: Person
    ) -> None:
        """Test recognize_jump returns None, None if no main_person is found."""
        scene_registry = SceneRegistry()
        scene_registry.upsert_entity(
            no_main_person, matching_criteria=MatchingCriteria.ID
        )
        scene_registry.upsert_action(
            SetupCheckedAction(
                entity_id=no_main_person.id, time=no_main_person.frame_time[0]
            )
        )
        jump_recognizer = JumpRecognizer(jump_recognizer_config)

        jump_recognizer._understand_scene(  # noqa: SLF001
            scene_registry=scene_registry,
        )
        jump_start_action = scene_registry.retrieve_actions(
            SceneActionFilter(action_type=JumpStartAction)
        )
        jump_end_action = scene_registry.retrieve_actions(
            SceneActionFilter(action_type=JumpEndAction)
        )
        # no actions should be detected
        assert not jump_start_action
        assert not jump_end_action

    def test_understand_scene_no_setup(
        self, jump_recognizer_config: JumpRecognizerConfig, no_main_person: Person
    ) -> None:
        """Test recognize_jump returns None, None if no main_person is found."""
        scene_registry = SceneRegistry()
        scene_registry.upsert_entity(
            no_main_person, matching_criteria=MatchingCriteria.ID
        )
        jump_recognizer = JumpRecognizer(jump_recognizer_config)

        jump_recognizer._understand_scene(  # noqa: SLF001
            scene_registry=scene_registry,
        )
        jump_start_action = scene_registry.retrieve_actions(
            SceneActionFilter(action_type=JumpStartAction)
        )
        jump_end_action = scene_registry.retrieve_actions(
            SceneActionFilter(action_type=JumpEndAction)
        )
        # no actions should be detected
        assert not jump_start_action
        assert not jump_end_action

    def test_understand_scene_success(
        self, jump_recognizer_config: JumpRecognizerConfig
    ) -> None:
        """Test recognize_jump returns None, None if no main_person is found."""
        scene_registry = SceneRegistry()
        main_person = MagicMock(spec=Person)
        main_person.role = EntityRole.MAIN_PERSON
        main_person.id = "MP"
        scene_registry.upsert_entity(main_person, matching_criteria=MatchingCriteria.ID)
        scene_registry.upsert_action(
            MagicMock(
                spec=SetupCheckedAction,
                entity_id=main_person.id,
                id="SA",
                setup_direction=Direction.LTR,
            )
        )
        jump_recognizer = JumpRecognizer(jump_recognizer_config)

        with patch.object(jump_recognizer, "recognize_jump"):
            jump_recognizer._understand_scene(  # noqa: SLF001
                scene_registry=scene_registry,
            )
        jump_start_action = scene_registry.retrieve_actions(
            SceneActionFilter(action_type=JumpStartAction)
        )
        jump_end_action = scene_registry.retrieve_actions(
            SceneActionFilter(action_type=JumpEndAction)
        )
        # no actions should be detected
        assert not jump_start_action
        assert not jump_end_action

    @pytest.mark.skip(reason="randomly fails, needs to be debugged post go-live.")
    def test_detect_movement_before_jump(
        self,
        jump_recognizer_config: JumpRecognizerConfig,
        jumping_person: tuple[
            Person, tuple[float, float], tuple[float, float], int, int, bool, float
        ],
    ) -> None:
        """Test the detect_and_set_movement_before_jump method for various scenarios."""
        person, _, _, start_frame, _, add_noise, noise_variance = jumping_person
        length_of_df = 100

        custom_config = deepcopy(jump_recognizer_config)
        custom_config.window_size = start_frame
        custom_config.window_offset = 1

        x_base = np.ones(length_of_df) * 10
        y_base = np.ones(length_of_df) * 20

        if add_noise:
            x_data = np.concatenate(
                [
                    np.linspace(10, 50, 5)
                    + np.random.uniform(-noise_variance, noise_variance, 5),
                    np.linspace(50, 100, length_of_df - 5),
                ]
            )
            y_data = np.concatenate(
                [
                    np.abs(np.random.normal(0, noise_variance, 5)),
                    40
                    + np.abs(np.sin(np.linspace(0, 4 * np.pi, length_of_df - 5)) * 30),
                ]
            )
        else:
            x_data = x_base
            y_data = y_base

        df = pd.DataFrame(
            {
                "x": x_data,
                "y": y_data,
                "frame": range(length_of_df),
            }
        )

        jump_start_action = JumpStartAction(
            position=(0, 0),
            time=person.frame_time[start_frame],
            entity_id=person.id,
        )

        JumpRecognizer.detect_and_set_movement_before_jump(
            df, jump_start_action, custom_config
        )

        expected_result = add_noise and noise_variance >= 20
        if jump_start_action.movement_before_jump is not expected_result:
            pytest.fail(
                f"Movement detection failed for add_noise={add_noise}, "
                f"noise_variance={noise_variance}"
            )


@pytest.mark.unittest
class TestNodeRegister:
    """A test class for the 'JumpRecognizer' registration.

    This class tests the node registration for 'JumpRecognizer', ensuring that
    it is correctly registered within the plugin manager and available in the
    'ObjectBuilder' registry.
    """

    def test_node_register(self) -> None:
        """Test the registration of the 'JumpRecognizer' node.

        This test verifies that the 'JumpRecognizer' node is properly
        registered in the plugin manager and added to the 'ObjectBuilder' registry.
        """
        # --- create plugin manager from scratch ---
        registry = deepcopy(ObjectBuilder._registry)  # noqa: SLF001
        ObjectBuilder._registry = {}  # noqa: SLF001
        pm = PluginManager(PLUGIN_NAMESPACE)
        pm.add_hookspecs(HookSpecs)
        if (
            pm.is_registered(JumpRecognizer)
            or "JumpRecognizer" in ObjectBuilder._registry  # noqa: SLF001
        ):
            pytest.fail("'JumpRecognizer' already registered")

        # --- register node to plugin manager ---
        if not pm.is_registered(JumpRecognizer):
            pm.register(JumpRecognizer)
        pm.hook.node_register()

        # --- check whether registration has been successfully ---
        if (
            not pm.is_registered(JumpRecognizer)
            or "JumpRecognizer" not in ObjectBuilder._registry  # noqa: SLF001
        ):
            pytest.fail("'JumpRecognizer' could not have been registered")

        # --- check whether registered object can be actually build ---
        pipe_config = {
            "id": "app_recognize_jump",
            "type": "JumpRecognizer",
            "param": {},
        }
        (builder_class, _) = ObjectBuilder.get_builder_class(
            "JumpRecognizer",
        )
        pipe_obj = builder_class()(pipe_config, resolve_references=False)
        if not isinstance(pipe_obj, JumpRecognizer):
            pytest.fail(
                "'JumpRecognizer' object could have been successfully built "
                "through the registered classes.",
            )
        ObjectBuilder._registry = registry  # noqa: SLF001

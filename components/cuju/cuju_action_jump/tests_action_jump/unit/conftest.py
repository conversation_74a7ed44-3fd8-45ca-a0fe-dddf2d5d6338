"""Fixtures for testing the jump action recognition."""

import uuid
from datetime import timedelta
from unittest.mock import MagicMock

import numpy as np
import pytest
from cuju_action_jump.p_jump_recognizer import (
    JumpRecognizerConfig,
)
from cuju_data_pose.pose2d.pose_base import Po<PERSON>
from cuju_data_scene.constants import <PERSON><PERSON><PERSON><PERSON><PERSON>
from cuju_data_scene.entities.person import Person
from cuju_data_scene.entities.tracked_entity import (
    ToPointMethod,
)
from cuju_prov_provider.p_memory_obj_provider import MemoryObjProvider
from imgeta.plugin_base.imgeta_tree.frame_time import FrameTime


@pytest.fixture
def jump_recognizer_config() -> JumpRecognizerConfig:
    """Create a valid JumpRecognizerConfig."""
    return JumpRecognizerConfig(
        smooth_window_size=3,
        smooth_trajectories=False,
        min_peak_height=1,
        min_peak_width=1,
        min_jump_frames=2,
        state_storage=MagicMock(spec=MemoryObjProvider),
    )


def create_frame_times(num_frames: int, fps: float = 30.0) -> list[FrameTime]:
    """Create a list of FrameTime objects."""
    frame_times = []

    for i in range(num_frames):
        frame_times.append(
            FrameTime(
                frame_number=i,
                frame_time=timedelta(seconds=1 / fps),
                frame_rate=fps,
                time_since_start=timedelta(seconds=i / fps),
            )
        )
    return frame_times


def simulate_jump_trajectories(
    jump_start_frame: int,
    jump_end_frame: int,
    jump_len_px: int,
    jump_height_px: int,
) -> tuple[np.ndarray, np.ndarray]:
    """Simulate a jump trajectory for the midpoint between both ankle keypoints.

    The movements here are very simplified. The y trajectory follows a triangle shape
    (liner ascent until the midpoint, then linear descent.), the x trajectory is linear.

    Parameters
    ----------
    jump_start_frame : int
        The starting frame number for the jump trajectory.
    jump_end_frame : int
        The landing frame number for the jump trajectory.
    jump_len_px : int
        The length of the jump in pixel.
    jump_height_px : int
        The height of the jump in pixel.

    Returns
    -------
    The x and y trajectories.
    """
    x_coords = []
    y_coords = []

    jump_duration = jump_end_frame - jump_start_frame

    for frame in range(jump_end_frame + 10):
        if frame < jump_start_frame:
            # Before the jump starts
            x_coords.append(0)
            y_coords.append(0)
        elif jump_start_frame <= frame < jump_end_frame:
            # During the jump
            # Calculate the progress of the jump
            progress = (frame - jump_start_frame) / jump_duration
            x_coords.append(int(jump_len_px * progress))  # Linearly rise to jump_len_px
            if progress <= 0.5:
                # Ascending phase
                # In image coordinates, "up" movement is actually along the
                # negative y-axis.
                y_coords.append(-int(jump_height_px * (2 * progress)))
            else:
                # Descending phase
                y_coords.append(
                    -int(jump_height_px * (2 * (1 - progress)))
                )  # Fall back down
        else:
            # After the jump ends
            x_coords.append(jump_len_px)  # Stay at jump_len_px
            y_coords.append(0)
        # Convert lists to numpy arrays
    x_coords = np.array(x_coords)
    y_coords = np.array(y_coords)

    return x_coords, y_coords


def mock_person(
    jump_start_frame: int, jump_end_frame: int, jump_len_px: int, jump_height_px: int
) -> Person:
    """Create a mock Person entity with simulated jump trajectories.

    Parameters
    ----------
    jump_start_frame : int
        The starting frame number for the jump trajectory.
    jump_end_frame : int
        The landing frame number for the jump trajectory.
    jump_len_px : int
        The length of the jump in pixel.
    jump_height_px : int
        The height of the jump in pixel.

    Returns
    -------
    A mocked Person entity configured to return the simulated jump trajectories.
    """
    # Use the parameters from the fixture
    x_positions, y_positions = simulate_jump_trajectories(
        jump_start_frame, jump_end_frame, jump_len_px, jump_height_px
    )

    class MockPerson(Person):
        def __init__(self) -> None:
            super().__init__(
                id="mocked_main_person",
                frame_time=create_frame_times(len(x_positions)),
                role=EntityRole.MAIN_PERSON,
            )
            self._frame_numbers = np.array(list(range(len(x_positions))))

            self.poses = [Pose(coords=np.random.randn(17, 2))] * len(x_positions)
            self.boxes = [None] * len(x_positions)

        def to_points(self, _method: ToPointMethod, **_kwargs: dict) -> np.ndarray:
            return np.column_stack((x_positions, y_positions))

        def get_frame_index(self, frame: int) -> int:
            return int(frame)  # Simple mapping for testing

    return MockPerson()


@pytest.fixture(
    params=[
        {
            "jump_start_frame": 10,
            "jump_end_frame": 20,
            "jump_len_px": 600,
            "jump_height_px": 100,
            "add_noise": True,
            "noise_variance": 30.0,
        },
        {
            "jump_start_frame": 5,
            "jump_end_frame": 13,
            "jump_len_px": 300,
            "jump_height_px": 40,
            "add_noise": True,
            "noise_variance": 20.0,
        },
        {
            "jump_start_frame": 18,
            "jump_end_frame": 30,
            "jump_len_px": 800,
            "jump_height_px": 130,
            "add_noise": False,
            "noise_variance": 0.1,
        },
    ]
)
def jumping_person(
    request: pytest.FixtureRequest,
) -> tuple[Person, tuple[float, float], tuple[float, float], int, int, bool, float]:
    """Return a mocked person that jumps.

    Parameters
    ----------
    request : pytest.FixtureRequest with parameters defined as:
        jump_start_frame : int
            The starting frame number for the jump trajectory.
        jump_end_frame : int
            The landing frame number for the jump trajectory.
        jump_len_px : int
            The length of the jump in pixel.
        jump_height_px : int
            The height of the jump in pixel.
        add_noise : bool
            Whether to add noise to the trajectory.
        noise_variance : float
            Variance level of noise to add (only if add_noise is True).

    Returns
    -------
    A mocked person, the expected starting position, the expected landing position,
    the expected start frame number, the expected landing frame number,
    whether to add noise, noise variance level.
    """
    start_frame = request.param["jump_start_frame"]
    end_frame = request.param["jump_end_frame"]
    jump_len = request.param["jump_len_px"]
    jump_height = request.param["jump_height_px"]
    add_noise = request.param["add_noise"]
    noise_variance = request.param["noise_variance"]

    return (
        mock_person(
            jump_start_frame=start_frame,
            jump_end_frame=end_frame,
            jump_len_px=jump_len,
            jump_height_px=jump_height,
        ),
        (
            0.0,
            0.0,
        ),
        (jump_len, 0.0),
        start_frame - 1,  # the -1 is done on purpose for test sanity.
        end_frame + 2,  # smoothing changes this, need to investigate after go-live
        add_noise,
        noise_variance,
    )


@pytest.fixture
def no_main_person() -> Person:
    """Return a Person without a main person role."""
    return Person(
        id=str(uuid.uuid4()),
        frame_time=create_frame_times(10),
    )

"""A module that provides a Detector for line crossings over a full video."""

from functools import partial

import numpy as np
import pandas as pd
from cuju_data_scene.actions.line_crossed_action import LineCrossedAction
from cuju_data_scene.entities.line import Line, LineEntity
from cuju_data_scene.entities.tracked_entity import ToPointMethod, TrackedEntity
from imgeta.plugin_base.imgeta_tree.frame_time import FrameTime

DIRECTION_ROW_COLUMNS = [
    "frame_from",
    "frame_to",
    "line",
    "trajectory",
    "entity_id",
    "conv_method",
]


def get_direction(row: pd.DataFrame, movement_df: pd.DataFrame) -> str | None:
    """Get the direction of the crossing defined by the row.

    Parameters
    ----------
    row : pd.DataFrame
        Row containing the information about the crossing.
    movement_df : pd.DataFrame
        DataFrame containing the movement trajectories of the entities.

    Returns
    -------
    str
        The direction of the crossing.
    """
    # get the starting point of the trajectory from frame f-1
    # and the endpoint from f+1
    movement_df_entity = movement_df[
        (movement_df["id"] == row["entity_id"])
        & (movement_df["conv_method"] == row["conv_method"])
    ]
    start_traj = movement_df_entity[
        movement_df_entity["frame_from"] == row["frame_from"]
    ][["x1", "y1"]]
    end_traj = movement_df_entity[
        (movement_df_entity["frame_from"] == row["frame_from"])
        & (movement_df_entity["frame_to"] == row["frame_to"])
    ][["x2", "y2"]]
    if start_traj.empty or end_traj.empty:
        return None
    traj = Line(
        p1=np.squeeze(start_traj.to_numpy()).tolist(),
        p2=np.squeeze(end_traj.to_numpy()).tolist(),
    )
    line = Line(
        p1=row["line"][0:2],
        p2=row["line"][2:4],
    )
    return line.get_crossing_direction(traj)


def get_frame_time(row: pd.DataFrame, entity: TrackedEntity) -> FrameTime | None:
    """Get the frame time of the crossing defined by the row.

    Parameters
    ----------
    row : pd.DataFrame
        Row containing the information about the crossing.
    entity : TrackedEntity
        List of entities.

    Returns
    -------
    FrameTime
        The frame time of the crossing.
    """
    frame_from_idx = entity.get_frame_index(int(row["frame_from"]))
    i = 1
    while frame_from_idx is None and row["frame_from"] + i < row["frame_to"]:
        frame_from_idx = entity.get_frame_index(int(row["frame_from"]) + i)
        i += 1
    frame_to_idx = entity.get_frame_index(int(row["frame_to"]))
    if frame_from_idx is None:
        return entity.frame_time[frame_to_idx]
    if frame_to_idx is not None:
        ft = entity.frame_time
        return ft[frame_from_idx].interpolate_linear(ft[frame_to_idx], at=row["frac"])
    return None


class LineCrossingDetector:
    """A virtual light barrier that detects crossings of pre-defined lines.

    The detector emits an observation when virtual lines are crossed.

    Attributes
    ----------
        config : LineCrossDetectorConfig
            configuration of the walk through counter.
    """

    def __init__(
        self,
    ) -> None:
        """Create a new instance of a LineCrossDetector."""

    def postprocess_intersections(
        self,
        intersections: pd.DataFrame,
        line_df: pd.DataFrame,
        entity_df: pd.DataFrame,
        line: list[Line],
        entity: list[TrackedEntity],
    ) -> pd.DataFrame:
        """Postprocess the intersections DataFrame.

        Parameters
        ----------
        intersections : pd.DataFrame
            DataFrame containing intersections of entities with lines.
        entity_df : pd.DataFrame
            DataFrame containing the movement trajectories of the entity.
        line_df : pd.DataFrame
            DataFrame containing the line.
        line: list[Line]
            Line Entiy object
        entity: list[TrackedEntity]
            Entiy object

        Returns
        -------
        pd.DataFrame
            Postprocessed intersections DataFrame.
        """
        if intersections.empty:
            return intersections
        # # store additional information on intersections for easier processing
        filtered_ents = entity_df.iloc[intersections["entity_idx"]]
        filtered_lines = line_df.iloc[intersections["line_idx"]]
        intersections["line"] = filtered_lines.loc[
            :, ["x1", "y1", "x2", "y2"]
        ].values.tolist()
        intersections["trajectory"] = filtered_ents.loc[
            :, ["x1", "y1", "x2", "y2"]
        ].values.tolist()
        intersections["line_id"] = line.id
        intersections["line_name"] = line.name
        intersections["anchor_role"] = line.anchor_role

        intersections["entity_id"] = entity.id
        intersections["frame_from"] = filtered_ents["frame_from"]
        intersections["frame_to"] = filtered_ents["frame_to"]
        intersections["conv_method"] = filtered_ents["conv_method"]
        if "project" in filtered_ents.columns:
            intersections["project"] = filtered_ents["project"]
        if "project_mid" in filtered_ents.columns:
            intersections["project_mid"] = filtered_ents["project_mid"]
        intersections["frac"] = np.clip(
            Line.fraction_at_point(
                filtered_ents[["x1", "y1", "x2", "y2"]],
                intersections[["x", "y"]],
            ),
            0,
            1,
        ).tolist()

        # remove duplicate cross-product detections
        intersections.drop_duplicates(
            subset=["frame_to", "line_id", "entity_id"],
            keep="first",
            ignore_index=True,
            inplace=True,
        )
        # if an entity (e) is detected exactly on the line in a frame (f), then two
        # intersections will be found. When e moves from f-1 to f and from f to f+1
        #                     f-1        f        f+1
        #                      |-------->|-------->|
        # In such a case, we keep only the first observation
        intersections.drop_duplicates(
            subset=["x", "y", "line_id", "entity_id"],
            keep="first",
            ignore_index=True,
            inplace=True,
        )
        if len(intersections) == 0:
            return intersections

        get_direction_callable = partial(get_direction, movement_df=filtered_ents)

        intersections["direction"] = intersections.agg(
            get_direction_callable,
            axis="columns",
        )
        no_direction = intersections["direction"].isnull()
        if no_direction.any():
            intersections.loc[no_direction, "direction"] = self._handle_null_directions(
                intersections.copy(), no_direction, filtered_ents, line_df
            )

        # Add frame time to intersections
        intersections["frame_time"] = intersections.apply(
            get_frame_time, entity=entity, axis=1
        )
        return intersections

    def __call__(
        self,
        entities: list[TrackedEntity],
        lines: list[LineEntity],
        entity_conversion_method: ToPointMethod | None = None,
        **kwargs: dict,
    ) -> list[LineCrossedAction]:
        """Find crossing observations of entities over lines.

        Parameters
        ----------
        - entities (list[TrackedEntity]):
            Entities whose movement trajectories will be checked for line crossings.
        - lines (list[LineEntity])
            Lines that are checked for crossing by each entity.
        - entity_conversion_method (ToPointMethod | None):
            Conversion method for the entities. If None, default conversion methods
            will be used.
        - **kwargs (dict):
            Additional keyword arguments to pass to the entity's get_trajectories_as_df
            method, such as `smooth_method`, `smooth_window`, and `smooth_polyorder`.

        Returns
        -------
        - LineCrossedAction
            Returns a list of Line-Crossing-Actions
        """
        int_list = []
        for line in lines:
            line_df = line.get_lines_as_df(smooth_line=False)
            for entity in entities:
                ent_df = entity.get_trajectories_as_df(
                    conversion_method=entity_conversion_method,
                    **kwargs,
                )
                conv_groups = ent_df.groupby("conv_method")
                for conv_method in conv_groups.groups:
                    conv_df = conv_groups.get_group(conv_method).reset_index(drop=True)
                    line_df_local = line_df[
                        line_df["frame_no"].isin(conv_df["frame_to"])
                    ].reset_index(drop=True)
                    conv_int = Line.get_intersections(line_df_local, conv_df)
                    group_int = self.postprocess_intersections(
                        conv_int,
                        line_df_local,
                        conv_df,
                        line,
                        entity,
                    )
                    int_list.append(group_int)

        intersections = pd.concat(int_list, ignore_index=True)
        return self.get_actions_from_intersections(intersections)

    def construct_movement_and_lines_df(
        self,
        entities: list[TrackedEntity],
        lines: list[LineEntity],
        entity_conversion_methods: list[ToPointMethod] | None = None,
        **kwargs: dict,
    ) -> tuple[pd.DataFrame, pd.DataFrame]:
        """Construct the movement and lines DataFrames.

        Parameters.
        ----------
        - entities (list[TrackedEntity]):
            Entities whose movement trajectories will be checked for line crossings.
        - lines (list[LineEntity])
            Lines that are checked for crossing by each entity.
        - entity_conversion_methods (list[ToPointMethod] | None):
            List of conversion methods for the entities. If None, default conversion
            methods will be used.

        Returns
        -------
        - lines_df (pd.DataFrame):
            DataFrame containing the lines.
        - movement_df (pd.DataFrame):
            DataFrame containing the movement trajectories of the entities.
        """
        lines_df = pd.concat(line.get_lines_as_df() for line in lines)

        entity_conversion_methods = entity_conversion_methods or [None]
        movement_df = pd.concat(
            entity.get_trajectories_as_df(conversion_method=method, **kwargs)
            for entity in entities
            for method in entity_conversion_methods
        )

        return lines_df.reset_index(drop=True), movement_df.reset_index(drop=True)

    def _handle_null_directions(
        self,
        intersections: pd.DataFrame,
        no_direction: pd.Series,
        movement_df: pd.DataFrame,
        lines_df: pd.DataFrame,
    ) -> pd.DataFrame:
        """Handle intersections where the direction is not defined.

        if direction is still None, the starting point of the trajectory is on the line
        and the direction is not defined, we go back one frame to get the direction
        this is a special case when the entity is detected on the line in the first
        frame and the direction is not defined

        Parameters
        ----------
        intersections : pd.DataFrame
            DataFrame containing intersections of entities with lines.
        no_direction : pd.Series
            Series indicating which rows have no direction.
        movement_df : pd.DataFrame
            DataFrame containing the movement trajectories of the entities.
        lines_df : pd.DataFrame
            DataFrame containing the lines for each frame.

        Returns
        -------
        pd.DataFrame
            DataFrame with only direction filled in.
        """
        get_direction_callable = partial(get_direction, movement_df=movement_df)
        frame_numbers = intersections.loc[no_direction, "frame_to"]
        next_frames = np.clip(frame_numbers + 1, 0, lines_df.index.max())
        intersections.loc[no_direction, "line"] = pd.Series(
            lines_df.loc[next_frames, ["x1", "y1", "x2", "y2"]].values.tolist(),
            index=intersections.loc[no_direction].index,
        )

        return intersections.loc[no_direction, :].agg(
            get_direction_callable,
            axis="columns",
        )

    def get_actions_from_intersections(
        self,
        intersections: pd.DataFrame,
    ) -> list[LineCrossedAction]:
        """Create actions from the intersection df.

        Parameters
        ----------
        intersections : pd.DataFrame
            DataFrame containing intersections of entities with lines.
        """
        actions = []
        for _, inters in intersections.iterrows():
            if inters["direction"] is not None:
                action = LineCrossedAction(
                    entity_id=inters["entity_id"],
                    line_name=inters["line_name"],
                    line_id=inters["line_id"],
                    time=inters["frame_time"],
                    detected_frame=inters["frame_to"],
                    position=(inters["x"], inters["y"]),
                    crossing_direction=inters["direction"],
                    crossing_trajectory=inters["trajectory"],
                    observed_point=str(inters["conv_method"]),
                    anchor_role=inters["anchor_role"],
                    project=inters.get("project", False),
                    project_mid=inters.get("project_mid", False),
                )
                actions.append(action)
        actions.sort(key=lambda a: a.time.precise_frame)
        return actions

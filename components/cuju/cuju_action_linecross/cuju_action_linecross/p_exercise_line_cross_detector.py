"""A virtual light barrier that detects crossings of exercise specific lines."""

from __future__ import annotations

from cuju_data_scene.actions.line_crossed_action import LineCrossedAction
from cuju_data_scene.entities.tracked_entity import SmoothMethod
from cuju_data_scene.p_scene_registry import SceneRegistry  # noqa: TC002
from cuju_data_scene.scene_node import SceneNode
from imgeta.pipeline.hook_markers import hook_impl
from imgeta.plugin_base.bases.config_base import ConfigBase
from imgeta.plugin_base.builder.object_builder import ObjectBuilder

from cuju_action_linecross.line_crossing_detector import LineCrossingDetector

from .exercise_strategies import STRATEGY_MAP


class ExerciseLineCrossDetectorConfig(ConfigBase):
    """Configuration for walkthrough counter.

    Attributes
    ----------
    smooth_method : SmoothMethod (default: KALMAN)
        The smoothing method to use for smoothing keypoint trajectories.
    smooth_window : int (default: 5)
        The window size of the smoothing function.
    smooth_polyorder: int (default: 2)
        The order of the polynomial used for smoothing.
    frame_buffer : int (default: 2)
        The size of the frame buffer used to filter duplicates.
    """

    smooth_line: bool = False
    smooth_method: SmoothMethod = SmoothMethod.NONE
    smooth_window: int = 5
    smooth_polyorder: int = 2
    frame_buffer: int = 2


class ExerciseLineCrossDetector(SceneNode):
    """A virtual light barrier that detects crossings of pre-defined lines.

    The detector emits an observation when virtual lines of the exercise are crossed.

    Attributes
    ----------
        config : ExerciseLineCrossDetectorConfig
            configuration of the walk through counter.
    """

    def __init__(
        self,
        config: ExerciseLineCrossDetectorConfig,
    ) -> None:
        """Create a new instance of a ExerciseLineCrossDetector."""
        super().__init__(
            ids=config.ids,
            on_control_flag=config.on_control_flag,
            state_storage=config.state_storage,
        )
        self._cfg = config

    def _understand_scene(self, scene_registry: SceneRegistry) -> None:
        """Apply line crossing detection according to an exercise strategy.

        Parameters
        ----------
        scene_registry: SceneRegistry
            The SceneRegistry.

        Returns
        -------
        - modified imgeta views
        """
        # fetch exercise ID and frame size
        if self._state_storage is not None:
            exercise_id = self._state_storage.get_state("exercise_id")
        else:
            msg = "Invalid or no Exercise ID provided to Line Cross Detector."
            raise RuntimeError(msg)

        retrieval_strategy = STRATEGY_MAP.get(exercise_id)
        if retrieval_strategy is None:
            msg = f"No retrieval strategy defined for exercise {exercise_id}"
            raise RuntimeError(msg)

        exercise_entities, exercise_lines, smoothing_params = retrieval_strategy(
            scene_registry
        )
        actions = LineCrossingDetector()(
            entities=exercise_entities,
            lines=exercise_lines,
            **smoothing_params.model_dump(exclude_none=True),
        )
        actions = LineCrossedAction.deduplicate_and_order_actions(
            actions, self._cfg.frame_buffer
        )
        for action in actions:
            scene_registry.upsert_action(action)

    @staticmethod
    @hook_impl
    def node_register() -> None:
        """Register the ExerciseLineCrossDetector node."""
        ObjectBuilder.direct_register(
            id_str="ExerciseLineCrossDetector",
            object_type=(
                "ExerciseLineCrossDetector",
                "cuju_action_linecross.p_exercise_line_cross_detector",
            ),
            config_type=(
                "ExerciseLineCrossDetectorConfig",
                "cuju_action_linecross.p_exercise_line_cross_detector",
            ),
        )

"""Unit tests for LineCrossDetector class in cuju_action_linecross."""

import uuid
from collections.abc import Callable
from datetime import timed<PERSON><PERSON>
from math import ceil, floor

import numpy as np
import pytest
from cuju_action_linecross.line_crossing_detector import LineCrossingDetector
from cuju_data_detection.bounding_box.box_base import BoundingBox
from cuju_data_detection.bounding_box.utils import BoxType
from cuju_data_scene.entities.ball import Ball
from cuju_data_scene.entities.direction import Direction as Dir
from cuju_data_scene.entities.line import Line, LineEntity
from cuju_data_scene.entities.scene_entity import EntityRole
from imgeta.plugin_base.imgeta_tree.frame_time import FrameTime
from imgeta.plugin_base.imgeta_tree.imgeta_views import ImgetaViews
from numpy.testing import assert_almost_equal

from tests_action_linecross.unit.bb_track_sim import BBTrackSimulation
from tests_action_linecross.unit.conftest import get_path_track_simulation_for_img

pytest.skip("Skipping test", allow_module_level=True)
# Constants
img_w, img_h = 600, 600  # image height and width of the sample images
observations_key = "lc_observ"
entity_key = "ent_ball"
lines_ent_key = "ent_lines"


@pytest.mark.unittest
class TestLineCrossingDetector:
    """Unittest Class for LineCrossingDetector."""

    @pytest.fixture
    def detector(self) -> LineCrossingDetector:
        """Line Crossing Detector."""
        return LineCrossingDetector()

    @pytest.fixture
    def get_line(self) -> Callable:
        """Get Line Entity from coordinates."""

        def _get_line_ent(
            coords: list[int], size: int = 1, name: str = "default-line"
        ) -> LineEntity:
            return LineEntity(
                name=name,
                lines=[Line(p1=np.array(coords[:2]), p2=np.array(coords[2:]))] * size,
                frame_time=[
                    FrameTime(
                        frame_number=i,
                        time_since_start=timedelta(seconds=i),
                        frame_time=timedelta(seconds=i),
                    )
                    for i in range(size)
                ],
            )

        return _get_line_ent

    @pytest.fixture
    def get_frame_times(self) -> Callable:
        """Get frame time."""

        def _get_frame_time(num_frames: int = 1, fps: int = 30) -> FrameTime:
            seconds_per_frame = 1 / fps
            return [
                FrameTime(
                    frame_number=i,
                    frame_time=timedelta(seconds=seconds_per_frame),
                    time_since_start=timedelta(seconds=i * seconds_per_frame),
                )
                for i in range(num_frames)
            ]

        return _get_frame_time

    @pytest.fixture
    def get_scene_sim(
        self,
        get_frame_times: Callable,
    ) -> Callable[[list[tuple[int, int]], int, int], tuple[ImgetaViews, list[Ball]]]:
        """Create a simulation of a scene with one entity (a Ball).

        Given a path for the ball, its speed, and bb_size, returns imgeta_vws
        containing nodes for each frame as well the entities for each frame.
        """

        def _get_scene_sim(
            ball_path: list[tuple[int, int]],
            speed: int = 15,
            bb_size: int = 50,
        ) -> tuple[ImgetaViews, list[Ball]]:
            bb_sim = BBTrackSimulation(ball_path, speed=speed, bb_size=bb_size)
            uid, ball_uid = uuid.uuid4(), uuid.uuid4()
            bb_per_frame = [
                BoundingBox(
                    box_id=str(uid),
                    coords=np.array(coords),
                    box_type=BoxType.XYXY,
                )
                for coords, _ in bb_sim
            ]
            nodes = get_path_track_simulation_for_img(
                path=ball_path,
                speed=speed,
                bb_size=bb_size,
            )
            ball = Ball(
                id=str(ball_uid),
                name="Main Ball",
                track_ids=["main_ball"] * len(bb_per_frame),
                frame_time=get_frame_times(len(bb_per_frame)),
                boxes=bb_per_frame,
                role=EntityRole.MAIN_BALL,
            )
            ball_ents = [ball]
            nodes[-1].results[entity_key] = ball_ents
            imgeta_vws = ImgetaViews(imgeta_nodes=nodes)
            return imgeta_vws, ball_ents

        return _get_scene_sim

    @pytest.mark.parametrize("speed", [7, 30, 45])
    @pytest.mark.parametrize("line_x", [100, 300, 550])
    def test_single_static_straight_line(
        self,
        get_scene_sim: Callable,
        get_line: Callable,
        speed: int,
        line_x: int,
        detector: LineCrossingDetector,
    ) -> None:
        """Test simple line crossing of static line."""
        # Arrange
        crss_y = img_h // 2  # height of the expected crossing point
        ball_path = [[0, crss_y], [img_w, crss_y]]  # straight line (LTR) 0 to 600
        _, ball_entities = get_scene_sim(ball_path=ball_path, speed=speed)
        line = get_line((line_x, 0, line_x, img_h), size=len(ball_entities))

        # Act
        actions = detector(entities=ball_entities, lines=[line])

        # Assert
        if len(actions) != 1:
            msg = f"Expected 1 frame with 1 action, got {len(actions)}"
            raise AssertionError(msg)
        action = actions[0]
        if (pos := action.position) != (line_x, crss_y):
            msg = f"Expected crossing at ({line_x}, {crss_y}), got ({pos[0]}, {pos[1]})"
            raise AssertionError(msg)
        if action.line_id != line.id:
            msg = f"Expected line_id {line.id}, got {action.line_id}"
            raise AssertionError(msg)
        if action.entity_id != ball_entities[0].id:
            msg = f"Expected entityid {ball_entities[0].id},got {action.entity_id}"
            raise AssertionError(msg)
        expected_crss_time = line_x / speed
        # CHeck if frame/time interpolation worked
        assert_almost_equal(action.time.frame_number, expected_crss_time)

    @pytest.mark.parametrize("speed", [7, 30, 45])
    @pytest.mark.skip("Currently fails for multiple entities.")
    def test_multiple_lines_and_entities(
        self,
        get_scene_sim: Callable,
        speed: int,
        get_line: Callable,
        detector: LineCrossingDetector,
    ) -> None:
        """Test simple line crossing of static line."""
        # Arrange
        margin = 50
        lx1, lx2 = img_w / 2, (img_w / 2) + margin
        crss_y = img_h // 2  # height of the expected crossing point
        ball_path = [[0, crss_y], [img_w, crss_y]]  # straight line (LTR) 0 to 600
        _, ball_entities = get_scene_sim(ball_path=ball_path, speed=speed)
        ball_path2 = [[0 + 100, crss_y + margin], [img_w + 100, crss_y + margin]]
        _, ball_entities2 = get_scene_sim(ball_path=ball_path2, speed=speed)
        lines = [
            get_line((lx1, 0, lx1, img_h), size=len(ball_entities), name="V Middle"),
            get_line((lx2, 0, lx2, img_h), size=len(ball_entities), name="V M+Margin"),
        ]

        # Act
        actions = detector(entities=[*ball_entities, *ball_entities2], lines=lines)

        # Assert
        if len(actions) != 4:
            msg = f"Expected 4 frames with 1 observation, got {len(actions)}"
            raise AssertionError(msg)
        a1, a2, a3, a4 = actions
        assert all((a.entity_id == ball_entities2[0].id) for a in [a1, a2]), (
            "first two actions should be from the ball_path2"
        )
        assert all((a.entity_id == ball_entities[0].id) for a in [a3, a4]), (
            "the last two actions should be from ball_path"
        )
        expected_crss_time1 = (lx1 - 100) / speed
        assert_almost_equal(a1.time.frame_number, expected_crss_time1)
        expected_crss_time2 = (lx2 - 100) / speed
        assert_almost_equal(a2.time.frame_number, expected_crss_time2)
        expected_crss_time3 = (lx1) / speed
        assert_almost_equal(a3.time.frame_number, expected_crss_time3)
        expected_crss_time4 = (lx2) / speed
        assert_almost_equal(a4.time.frame_number, expected_crss_time4)
        exp_x, exp_y = [lx1, lx2], crss_y + margin
        assert all(a.position[0] in exp_x for a in [a1, a2, a3, a4]), (
            f"Expected all x positions to be in {exp_x}"
        )
        assert all(a.position[1] == exp_y for a in [a1, a2]), (
            f"Expected first two y positions to be {exp_y}"
        )
        assert all(a.position[1] == crss_y for a in [a3, a4]), (
            f"Expected last two y positions to be {crss_y}"
        )

    @pytest.mark.parametrize("speed", [7, 30, 45])
    @pytest.mark.parametrize("tracking_margin", [1, 5, 6])
    def test_lost_tracking_interpolation(
        self,
        get_scene_sim: Callable,
        speed: int,
        tracking_margin: int,
        get_line: Callable,
        detector: LineCrossingDetector,
    ) -> None:
        """Test simple line crossing with a lost tracking of the entity."""
        # Arrange
        crss_x, crss_y = img_w // 2, img_h // 2  # expected crossing point
        ball_path = [[0, crss_y], [img_w, crss_y]]  # straight line (LTR) 0 to 600
        _, ball_entities = get_scene_sim(ball_path=ball_path, speed=speed)
        # Remove all but the start and end frame to simulate lost entity tracking
        for i in range(tracking_margin, len(ball_entities) - tracking_margin):
            ball_entities[0].boxes[i] = None

        lines = [
            get_line((crss_x, 0, crss_x, img_h), size=len(ball_entities), name="Vert")
        ]

        # Act
        actions = detector(entities=ball_entities, lines=lines)

        # Assert
        if len(actions) != 1:
            msg = f"Expected 1 frame with 1 observation, got {len(actions)}"
            raise AssertionError(msg)
        action = actions[0]
        if (pos := action.position) != (crss_x, crss_y):
            msg = f"Expected crossing at ({crss_x}, {crss_y}), got ({pos[0]}, {pos[1]})"
            raise AssertionError(msg)
        if action.line_id != lines[0].id:
            msg = f"Expected line_id {lines[0].id}, got {action.line_id}"
            raise AssertionError(msg)
        if action.entity_id != ball_entities[0].id:
            msg = f"Expected entityid {ball_entities[0].id},got {action.entity_id}"
            raise AssertionError(msg)
        expected_crss_time = crss_x / speed
        diff = abs(action.time.frame_number - expected_crss_time)
        eps = 0.4
        if diff >= eps:
            pytest.fail(
                f"Expected observation at frame {expected_crss_time:.3f} ."
                f" Actual: {action.time.frame_number:.3f} exceeds eps: {eps}",
            )

    @pytest.mark.parametrize("speed", [7, 30, 45])
    def test_directional_lines(
        self,
        get_scene_sim: Callable,
        speed: int,
        get_line: Callable,
        detector: LineCrossingDetector,
    ) -> None:
        """Test multiple line crossing observations with dynamic & static lines."""
        # Arrange
        crss_x, crss_y = img_w // 2, img_h // 2  # expected crossing points
        ball_path = [[0, 0], [img_w, img_h]]  # straight diagonal line (LTR)
        _, ball_entities = get_scene_sim(ball_path=ball_path, speed=speed)

        lines = [
            get_line((0, img_h, img_w, 0), size=1, name="Static Diag revert"),
            get_line((crss_x, 0, crss_x, img_h), size=1, name="Static Vertical"),
            get_line((0, img_h, img_w, 0), size=1, name="Static Diagonal"),
            # should not be found
            get_line((0, 0, img_w, img_h), size=1, name="Static Duplicate"),
        ]

        # Act
        actions = detector(entities=ball_entities, lines=lines)

        # Assert
        if len(actions) != 3:
            msg = f"Expected 3 actions, got {len(actions)}"
            raise AssertionError(msg)
        if not all((a.position == (crss_x, crss_y)) for a in actions):
            msg = f"Expected crossing at ({crss_x}, {crss_y}) for all 3 actions."
            raise AssertionError(msg)
        # We don't expect any intersections with the perfectly parallel line -> (0:3)
        expected_ids = {line_l.id for line_l in lines[0:3]}
        observed_ids = {a.line_id for a in actions}
        if observed_ids != expected_ids:
            msg = f"Expected line_ids: {expected_ids!s}, got {observed_ids!s}"
            raise AssertionError(msg)
        line_len = Line(p1=ball_path[0], p2=ball_path[1]).length()
        expected_crss_time = (line_len / 2) / speed
        times = {a.time.frame_number for a in actions}
        if len(times) != 1:
            msg = "Expected to observe all crossings at the same time."
            raise AssertionError(msg)
        # CHeck if frame/time interpolation worked
        assert_almost_equal(times.pop(), expected_crss_time, 1)

    @pytest.mark.parametrize("speed", [7, 30, 45])
    def test_moving_crossing_line(
        self,
        get_scene_sim: Callable,
        speed: int,
        detector: LineCrossingDetector,
    ) -> None:
        """Test single line actions with a moving line."""
        # Arrange
        crss_y = img_h // 2  # height of the expected crossing point
        ball_path = [[0, crss_y], [600, crss_y]]  # straight line (LTR) 0 to 600
        _, ball_entities = get_scene_sim(ball_path=ball_path, speed=speed)

        ex_id = str(uuid.uuid4())  # expected id
        # The line moves from the left side of the image in the first half to the
        # right side of the image in the second half. That way we should capture two
        # crossings on the same line with the entity moving in a straight line.
        ex_x1, ex_x2 = 50, img_w - 50
        line_halfs = [
            Line(id=ex_id, name="1. Half", p1=(ex_x1, 0), p2=(ex_x1, img_h)),
            Line(id=ex_id, name="2. Half", p1=(ex_x2, 0), p2=(ex_x2, img_h)),
        ]
        llist = [
            *[line_halfs[0]] * floor(len(ball_entities[0]) / 2),  # First Half of frames
            *[line_halfs[1]] * ceil(len(ball_entities[0]) / 2),  # Second Half of frames
        ]
        lines = [LineEntity(id=ex_id, name="Test_line", lines=llist)]

        # Act
        actions = detector(entities=ball_entities, lines=lines)

        # Assert
        if len(actions) != 2:
            msg = f"Expected 2 actions, got {len(actions)}"
            raise AssertionError(msg)
        act1, act2 = actions[0], actions[1]
        pos1, pos2 = act1.position, act2.position
        if pos1 != (ex_x1, crss_y) or pos2 != (ex_x2, crss_y):
            msg = f"Expected x crossing at {ex_x1} & {ex_x2}, got {pos1[0]}, {pos2[0]}"
            raise AssertionError(msg)
        if act1.line_id != ex_id or act2.line_id != ex_id:
            msg = f"Expected both actions to have the same id {ex_id}"
            raise AssertionError(msg)
        expected_crss_time_1 = ex_x1 / speed
        expected_crss_time_2 = ex_x2 / speed
        # CHeck if frame/time interpolation worked
        assert_almost_equal(act1.time.frame_number, expected_crss_time_1)
        assert_almost_equal(act2.time.frame_number, expected_crss_time_2)

    @pytest.mark.parametrize("tracking_loss_percent", [0, 20, 50, 90])
    @pytest.mark.parametrize("speed", [7, 30])
    def test_sequential_processing(
        self,
        get_scene_sim: Callable,
        get_line: Callable,
        speed: int,
        tracking_loss_percent: int,
        detector: LineCrossingDetector,
    ) -> None:
        """Test frame-by-frame / sequential processing of lines."""
        # Arrange
        crss_x, crss_y = img_w // 2, img_h // 2  # expected crossing points
        ball_path = [[0, crss_y], [img_w, crss_y]]  # straight line (LTR) 0 to 600
        _, ball_entities = get_scene_sim(ball_path=ball_path, speed=speed)
        # Remove frames to simulate lost entity tracking
        tracking_loss = round(len(ball_entities) * (tracking_loss_percent / 100))
        start_index = (len(ball_entities) - tracking_loss) // 2
        end_index = start_index + tracking_loss
        for i in range(start_index, end_index):
            ball_entities[0].boxes[i] = None
        lines = [
            get_line(
                (crss_x, 0, crss_x, img_h), size=len(ball_entities[0]), name="Vrt"
            ),
        ]

        # Act
        actions = detector(entities=ball_entities, lines=lines)

        # Assert: we should have 1 frame with 1 observations
        if len(actions) != 1:
            pytest.fail(f"Expected 1 frame with 1 observation, got: {len(actions)}")
        # both lines should be crossed at the same time
        expected_crss_time_1 = crss_x / speed
        # CHeck if frame/time interpolation worked
        for act in actions:
            assert_almost_equal(act.time.frame_number, expected_crss_time_1)

    @pytest.mark.parametrize("speed", [7, 30, 45])
    def test_square_route(
        self,
        get_scene_sim: Callable,
        get_line: Callable,
        speed: int,
        detector: LineCrossingDetector,
    ) -> None:
        """Test a route with a bit more complexity: square."""
        # Arrange
        # Create a route along a square path with a fixed margin to the img dimensions
        margin = 75
        x1, x2, x3, x4 = margin, img_w - margin, img_w - margin, margin
        y1, y2, y3, y4 = margin, margin, img_h - margin, img_h - margin
        ball_path = [(x1, y1), (x2, y2), (x3, y3), (x4, y4), (x1, y1)]
        _, ball_entities = get_scene_sim(ball_path=ball_path, speed=speed)
        lines = [
            get_line((0, img_h // 2, img_w, img_h // 2), size=len(ball_entities[0])),
            get_line((img_w // 2, 0, img_w // 2, img_h), size=len(ball_entities[0])),
        ]

        # Act
        actions = detector(entities=ball_entities, lines=lines)

        # Assert
        if len(actions) != 4:
            msg = f"Expected 4 actions, got {len(actions)}"
            raise AssertionError(msg)
        o1, o2, o3, o4 = actions
        sl_horz, sl_vert = lines[0], lines[1]
        if o1.position != (sl_vert.lines[0].p1[0], y1):
            msg = "Expected 1. observation of crossing the vertical line at y1"
            raise AssertionError(msg)
        if o2.position != (x2, sl_horz.lines[0].p1[1]):
            msg = "Expected 2. observation of crossing the horizontal line at x2"
            raise AssertionError(msg)
        if o3.position != (sl_vert.lines[0].p2[0], y3):
            msg = "Expected 3. observation of crossing the vertical line at y3"
            raise AssertionError(msg)
        if o4.position != (x4, sl_horz.lines[0].p2[1]):
            msg = "Expected 4. observation of crossing the horizontal line at x4"
            raise AssertionError(msg)
        expected_dirs = [
            Dir.RTL,
            Dir.LTR,
            Dir.LTR,
            Dir.RTL,
        ]
        actual_dirs = [a.crossing_direction for a in actions]
        if expected_dirs != actual_dirs:
            pytest.fail(
                f"Expected the following crossing directions: \n"
                f"{', '.join(expected_dirs)} \n"
                "Got: \n"
                f"{', '.join(actual_dirs)}",
            )

"""Conftest for cuju_embedding_fastreid tests."""

from __future__ import annotations

from collections.abc import Callable
from datetime import timedelta
from typing import TYPE_CHECKING, Any

if TYPE_CHECKING:
    from collections.abc import Callable
from unittest import mock
from unittest.mock import MagicMock, create_autospec

import cv2 as cv
import numpy as np
import pytest
from cuju_action_linecross.p_exercise_line_cross_detector import (
    ExerciseLineCrossDetectorConfig,
)
from cuju_data_detection.bounding_box.box_base import BoundingBox
from cuju_data_detection.bounding_box.utils import BoxType
from cuju_data_pose.pose2d.pose_base import Pose
from cuju_data_scene.actions.line_crossed_action import LineCrossedAction
from cuju_data_scene.actions.setup_checked_action import SetupCheckedAction
from cuju_data_scene.constants import EntityRole, ExerciseId
from cuju_data_scene.entities.ball import Ball
from cuju_data_scene.entities.cone import Cone
from cuju_data_scene.entities.direction import Direction
from cuju_data_scene.entities.line import LineEntity
from cuju_data_scene.entities.person import Person
from cuju_data_scene.p_scene_registry import MatchingCrite<PERSON>, SceneRegistry
from cuju_data_tracking.tracking_result import TrackingResult
from cuju_filter_position.object_position_result import (
    ObjectPositionResult,
)
from cuju_line_perspective.exercise_strategies import wallball_strategy
from imgeta.plugin_base.imgeta_tree.frame_time import FrameTime
from imgeta.plugin_base.imgeta_tree.imgeta_node import ImgetaNode
from imgeta.plugin_base.imgeta_tree.imgeta_views import ImgetaViews

from .bb_track_sim import BBTrackSimulation


@pytest.fixture
def square_run_imgeta_views(request: pytest.FixtureRequest) -> ImgetaViews:
    """Fixture for ImgetaViews from Square Run."""
    exercise_rounds = request.param if hasattr(request, "param") else 5
    square_path = [(75, 75), (525, 75), (525, 525), (75, 525)]
    n_square_rounds = square_path * exercise_rounds
    nodes = get_path_track_simulation_for_img(path=n_square_rounds, speed=15)
    return ImgetaViews(imgeta_nodes=nodes)


@pytest.fixture(params=["LTR", "RTL", "TTB", "BTT"])
def center_line_trajectories(
    empty_sample_img: np.ndarray,
    request: pytest.FixtureRequest,
) -> tuple[ImgetaViews, str]:
    """Fixture for different center line trajectories."""
    c_x, c_y = empty_sample_img.shape[0] // 2, empty_sample_img.shape[1] // 2
    path_choice = {
        "LTR": [(0, c_y), (empty_sample_img.shape[0], c_y)],
        "RTL": [(empty_sample_img.shape[0], c_y), (0, c_y)],
        "TTB": [(c_x, 0), (c_x, empty_sample_img.shape[1])],
        "BTT": [(c_x, empty_sample_img.shape[1]), (c_x, 0)],
    }
    path = path_choice.get(request.param, "LTR")
    nodes = get_path_track_simulation_for_img(path=path)
    return ImgetaViews(imgeta_nodes=nodes), request.param


@pytest.fixture
def empty_sample_img() -> None:
    """Create a sample image for unit tests."""
    img_height, img_width = 600, 600
    return _get_empty_sample_image(img_width, img_height)


def get_path_track_simulation_for_img(
    path: list[tuple[int, int]] | None,
    speed: int = 15,
    bb_size: int = 50,
    bg_img: np.ndarray | None = None,
) -> list[ImgetaNode]:
    """Generate a bounding box trajectory simulation ontop of a given image.

    Parameters
    ----------
    path : Optional[list[tuple[int, int]]]
        Path that should be traced by the bounding box.
    speed : int
        Speed in pixels per frame that the path is traced by the box, by default 15
    bb_size : int
        Size of the Bounding box (defaults to 50 pixels)
    bg_img : Optional[np.ndarray]
        Background image that the bb object should be drawn upon.

    Returns
    -------
    Sequence[ImgetaNode]
        Returns a Sequence of Imgeta Nodes with bounding boxes drawn and traces added
    """
    bb_sim = BBTrackSimulation(path, speed=speed, bb_size=bb_size)
    if bg_img is None:
        bg_img = _get_empty_sample_image()
    return add_bb_track_to_bg_img(bb_sim=bb_sim, bg_img=bg_img)


def _get_empty_sample_image(width: int = 600, height: int = 600) -> np.ndarray:
    return np.zeros((width, height, 3), dtype=np.uint8)


def add_bb_track_to_bg_img(
    bb_sim: BBTrackSimulation,
    bg_img: np.ndarray,
) -> list[ImgetaNode]:
    """Add BoundingBoxTrack to a background image. Return imgeta_node for each frame."""
    nodes = []
    for i, (track_pos, _) in enumerate(bb_sim):
        p1_x, p1_y, p2_x, p2_y = track_pos
        cv.rectangle(
            bg_img,
            (p1_x, p1_y),
            (p2_x, p2_y),
            (0, 255, 0),
            3,
        )
        node = mock.MagicMock(spec=ImgetaNode)
        node.traces = []
        box = BoundingBox(coords=np.array(track_pos))
        track_result = TrackingResult(
            boxes=[box],
            track_ids=["0"],
            labels=["track-0"],
            scores=[np.array(0.9)],
        )
        pos_result = ObjectPositionResult(
            box_ids=[box.box_id],
            positions=[box.to(BoxType.CXCYWH).coords[:2].tolist()],
            labels=["person"],
        )
        node.col_results_in_node.return_value = {
            "detections": track_result,
            "positions": pos_result,
        }
        node.img = bg_img
        node.results = {
            "detections": track_result,
            "positions": pos_result,
        }
        node.frame_time = FrameTime(
            frame_number=i,
            number_of_frames=len(bb_sim._bb_path),  # noqa: SLF001
            frame_rate=30.0,
            frame_time=timedelta(seconds=1 / 30),
            time_since_start=timedelta(seconds=i / 30),
        )
        nodes.append(node)
    return nodes


def get_frame_time(frame_number: int, fps: float = 10.0) -> FrameTime:
    """Get the frame time for a given frame number."""
    frame_time = 1 / fps
    return FrameTime(
        frame_number=frame_number,
        fps=fps,
        frame_time=frame_time,
        time_since_start=frame_number * frame_time,
    )


@pytest.fixture
def person() -> Person:
    """Create a Person entity for testing."""
    boxes = []
    poses = []
    frame_times = []
    for i in range(5):
        boxes.append(BoundingBox(coords=np.array([10, 10, 20, 20])))
        poses.append(Pose(coords=np.random.rand(17, 2), visibility=np.random.rand(17)))
        frame_times.append(get_frame_time(i))

    return Person(
        role=EntityRole.MAIN_PERSON,
        name="test_person",
        boxes=boxes,
        poses=poses,
        frame_time=frame_times,
    )


@pytest.fixture
def ball() -> Ball:
    """Create a Ball entity for testing."""
    boxes = []
    frame_times = []
    for i in range(5):
        boxes.append(BoundingBox(coords=np.array([10, 10, 20, 20])))
        frame_times.append(get_frame_time(i))

    return Ball(
        role=EntityRole.MAIN_BALL,
        name="test_ball",
        boxes=boxes,
        frame_time=frame_times,
    )


@pytest.fixture
def cone_factory() -> Callable[..., Cone]:
    """Create cones with different roles."""

    def _create_cone(
        role: EntityRole, name: str, position: tuple[int, int], n_frames: int = 5
    ) -> Cone:
        boxes, frame_times = [], []
        for i in range(n_frames):
            boxes.append(
                BoundingBox(
                    coords=np.array(
                        [
                            position[0] - 10,
                            position[1] - 10,
                            position[0] + 10,
                            position[1] + 10,
                        ]
                    )
                )
            )
            frame_times.append(get_frame_time(i))
        return Cone(
            initial_position=BoundingBox(
                coords=np.array(
                    [
                        position[0] - 10,
                        position[1] - 10,
                        position[0] + 10,
                        position[1] + 10,
                    ]
                )
            ),
            role=role,
            name=name,
            frame_time=frame_times,
            boxes=boxes,
        )

    return _create_cone


@pytest.fixture
def sprint_cones(cone_factory: Callable[..., Cone]) -> list[Cone]:
    """Create cones for sprint strategy testing."""
    return [
        cone_factory(EntityRole.CONE_1, "track-0", (50, 400)),
        cone_factory(EntityRole.CONE_2, "track-1", (150, 400)),
        cone_factory(EntityRole.CONE_3, "track-2", (250, 400)),
        cone_factory(EntityRole.CONE_4, "track-3", (350, 400)),
    ]


@pytest.fixture
def wallball_cones(cone_factory: Callable[..., Cone]) -> list[Cone]:
    """Create cones for wallball strategy testing."""
    return [
        cone_factory(EntityRole.CONE_1, "track-0", (150, 400)),
        cone_factory(EntityRole.CONE_2, "track-1", (250, 400)),
        cone_factory(EntityRole.CONE_3, "track-2", (250, 300)),
        cone_factory(EntityRole.CONE_4, "track-3", (150, 300)),
    ]


@pytest.fixture
def ttest_cones(cone_factory: Callable[..., Cone]) -> dict[str, Cone]:
    """Create cones for T-test strategy testing."""
    return {
        "cone1": cone_factory(EntityRole.CONE_1, "cone1", (200, 200)),
        "cone2": cone_factory(EntityRole.CONE_2, "cone2", (200, 300)),
    }


@pytest.fixture
def setup_action_factory() -> Callable[..., SetupCheckedAction]:
    """Create setup checked actions with different directions."""

    def _create_action(ex_id: ExerciseId, direction: Direction) -> SetupCheckedAction:
        action = SetupCheckedAction(
            exercise_id=ex_id,
            time=get_frame_time(0),
        )
        action.setup_direction = direction
        return action

    return _create_action


@pytest.fixture
def registry_factory(  # noqa: PLR0913
    person: Person,
    ball: Ball,
    sprint_cones: list,
    wallball_cones: list,
    ttest_cones: dict[str, Any],
    setup_action_factory: Callable[..., SetupCheckedAction],
) -> Callable[..., SceneRegistry]:
    """Create a factory fixture to create scene registries."""

    def _create_registry(
        exercise_id: int, direction: Direction = Direction.LTR
    ) -> SceneRegistry:
        registry = SceneRegistry()

        # Add person
        registry.upsert_entity(person, MatchingCriteria.ID)
        # Add ball
        registry.upsert_entity(ball, MatchingCriteria.ID)
        # Add cones based on exercise type
        if exercise_id == ExerciseId.SPRINT:
            for cone in sprint_cones:
                registry.upsert_entity(cone, MatchingCriteria.ID)
        elif exercise_id == ExerciseId.NAVETTE:
            registry.upsert_entity(ttest_cones["cone1"], MatchingCriteria.ID)
        elif exercise_id == ExerciseId.T_TEST:
            registry.upsert_entity(ttest_cones["cone1"], MatchingCriteria.ID)
            registry.upsert_entity(ttest_cones["cone2"], MatchingCriteria.ID)
            registry.upsert_action(setup_action_factory(exercise_id, direction))
        elif exercise_id == ExerciseId.SQUARE_WB:
            # only need one cone here.
            registry.upsert_entity(sprint_cones[0], MatchingCriteria.ID)
            registry.upsert_entity(sprint_cones[2], MatchingCriteria.ID)
            registry.upsert_action(setup_action_factory(exercise_id, direction))
            registry.upsert_entity(
                LineEntity(
                    name="perspective-line-timer", anchor_role=EntityRole.CONE_1
                ),
                MatchingCriteria.ID,
            )
        elif exercise_id == ExerciseId.WALL_BALL_WB:
            registry.upsert_entity(wallball_cones[0], MatchingCriteria.ID)
            registry.upsert_entity(wallball_cones[3], MatchingCriteria.ID)
            registry.upsert_action(setup_action_factory(exercise_id, direction))
            lines = wallball_strategy(registry)
            for line in lines:
                registry.upsert_entity(line, MatchingCriteria.ID)
        return registry

    return _create_registry


@pytest.fixture
def mock_registry() -> SceneRegistry:
    """Fixture to create a mock SceneRegistry object."""
    return create_autospec(SceneRegistry)


@pytest.fixture
def mock_state_storage() -> MagicMock:
    """Fixture to create a mock object."""
    return MagicMock()


@pytest.fixture
def line_config(
    mock_registry: SceneRegistry, mock_state_storage: MagicMock
) -> ExerciseLineCrossDetectorConfig:
    """Fixture to create a mock configuration for the ExerciseLineCrossDetector."""
    return ExerciseLineCrossDetectorConfig(
        registry=mock_registry, state_storage=mock_state_storage
    )


@pytest.fixture
def mock_objects() -> tuple[ExerciseId, MagicMock, MagicMock, LineCrossedAction]:
    """Fixture to create mock objects for testing."""
    dummy_entity = MagicMock()
    dummy_line = MagicMock()
    dummy_action = MagicMock(spec=LineCrossedAction)
    dummy_action.time = MagicMock()
    dummy_action.time.precise_frame = 10
    dummy_action.is_duplicate_keypoint.return_value = False
    return ExerciseId.SPRINT, dummy_entity, dummy_line, dummy_action

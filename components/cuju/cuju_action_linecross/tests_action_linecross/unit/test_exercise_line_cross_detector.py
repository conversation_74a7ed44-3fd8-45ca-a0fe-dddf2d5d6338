"""Tests for ExerciseLineCrossDetector."""

from typing import Any
from unittest.mock import Magic<PERSON>ock

import pytest
from cuju_action_linecross.exercise_strategies import STRATEGY_MAP, SmoothingParams
from cuju_action_linecross.p_exercise_line_cross_detector import (
    ExerciseLineCrossDetector,
    ExerciseLineCrossDetectorConfig,
)
from cuju_data_scene.actions.line_crossed_action import Line<PERSON><PERSON>edAction
from cuju_data_scene.constants import ExerciseId
from cuju_data_scene.p_scene_registry import SceneRegistry
from imgeta.plugin_base.bases.control_flag import ControlFlag


@pytest.mark.unittest
class TestExerciseLineCrossDetector:
    """Unit tests for ExerciseLineCrossDetector."""

    @pytest.mark.parametrize(
        ("exercise_id", "expected_error", "expected_msg"),
        [
            (
                "INVALID",
                RuntimeError,
                "No retrieval strategy defined for exercise INVALID",
            ),
        ],
    )
    def test_raises_error_for_invalid_or_missing_strategy(
        self,
        line_config: ExerciseLineCrossDetectorConfig,
        exercise_id: str | ExerciseId,
        expected_error: type[BaseException],
        expected_msg: str,
    ) -> None:
        """Should raise errors for invalid or unmapped exercise IDs."""
        detector = ExerciseLineCrossDetector(line_config)
        detector._node_control = ControlFlag.END_OF_STREAM  # noqa: SLF001
        detector._cfg.state_storage.get_state = lambda _: exercise_id  # noqa: SLF001

        with pytest.raises(expected_error) as excinfo:
            detector._understand_scene(MagicMock())  # noqa: SLF001
        if expected_msg not in str(excinfo.value):
            pytest.fail(
                f"Expected error message '{expected_msg}' in exception, "
                f"but got '{excinfo.value}'"
            )

    def test_correct_strategy_and_upserts_actions(
        self,
        line_config: ExerciseLineCrossDetectorConfig,
        mock_registry: SceneRegistry,
        mock_objects: tuple[ExerciseId, MagicMock, MagicMock, LineCrossedAction],
        monkeypatch: Any,
    ) -> None:
        """Should use correct strategy and call upsert_action for all actions."""
        detector = ExerciseLineCrossDetector(line_config)
        detector._node_control = ControlFlag.END_OF_STREAM  # noqa: SLF001
        exercise_id, dummy_entity, dummy_line, dummy_action = mock_objects
        detector._cfg.state_storage.get_state = lambda _: exercise_id  # noqa: SLF001

        valid_id = ExerciseId.SPRINT

        monkeypatch.setitem(
            STRATEGY_MAP,
            valid_id,
            lambda _: ([dummy_entity], [dummy_line], SmoothingParams()),
        )

        monkeypatch.setattr(
            "cuju_action_linecross.p_exercise_line_cross_detector.LineCrossingDetector",
            lambda: (
                lambda entities,  # noqa: ARG005
                smooth_method,  # noqa: ARG005
                smooth_window,  # noqa: ARG005
                smooth_polyorder,  # noqa: ARG005
                lines: [  # noqa: ARG005
                    dummy_action
                ]
            ),
        )

        detector._understand_scene(mock_registry)  # noqa: SLF001

        if not mock_registry.upsert_action.called:
            pytest.fail("Expected upsert_action to be called on registry")

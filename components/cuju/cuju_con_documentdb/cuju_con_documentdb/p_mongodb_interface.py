"""Interact with MongoDB using PyMongo.

Provides a wrapper class to interact with MongoDB using PyMongo.

Example:

**Initialize the MongoDB wrapper**:
```python
docdb_resource = MongoDBInterface(
    connection_string="mongodb://localhost:27017",
    database="mydb",
    collection="mycollection
)
```
**Create a new document**:
```python
docdb_resource.create_item({"name": "Alice"})
```
**Retrieve a document by ID**:
```python
docdb_resource.get_item("123e4567-e89b-12d3-a456-426614174000")
```
**Update a document by ID**:
```python
docdb_resource.update_item("123e4567-e89b-12d3-a456-426614174000", {"name": "Bob"})
```
**Delete a document by ID**:
```python
docdb_resource.delete_item("123e4567-e89b-12d3-a456-426614174000")
```
**Find documents matching query criteria**:
```python
docdb_resource.find_items({"name": "<PERSON>"})
```
**Perform aggregation operations**:
```python
docdb_resource.aggregate([{"$group": {"_id": "$name", "count": {"$sum": 1}}}])
```
"""

from __future__ import annotations

import datetime
from typing import TYPE_CHECKING, Any

import pymongo
from bson.errors import InvalidId
from bson.objectid import ObjectId
from imgeta.pipeline.hook_markers import hook_impl
from imgeta.plugin_base.bases.config_base import ConfigBase
from imgeta.plugin_base.builder.object_builder import ObjectBuilder
from loguru import logger as logging
from pydantic import BaseModel
from pymongo import ASCENDING, MongoClient
from pymongo.errors import PyMongoError

if TYPE_CHECKING:
    from collections.abc import Generator


class MongoDBResponse(BaseModel):
    """Type definition for response dictionary."""

    success: bool
    error: str | None
    item: dict[str, Any] | list[dict[str, Any]] | None


class MongoDBInterfaceConfig(ConfigBase):
    """Configuration for MongoDBInterface.

    Attributes
    ----------
        connection_string (str):
            MongoDB connection string
        database (str):
            Database name
        collection (str):
            Collection name
    """

    connection_string: str
    database: str
    collection: str


class MongoDBInterface:
    """A wrapper class for MongoDB operations."""

    def __init__(
        self,
        config: MongoDBInterfaceConfig,
    ) -> None:
        """
        Initialize the MongoDB wrapper.

        Parameters
        ----------
            config (dict): Configuration parameters
        """
        self._init_connection(
            config.connection_string,
            config.database,
            config.collection,
        )

    def _init_connection(
        self,
        connection_string: str,
        database: str,
        collection: str,
        *,
        retry_writes: bool = False,
    ) -> None:
        """
        Initialize the MongoDB connection.

        Parameters
        ----------
            connection_string (str):
                MongoDB connection string
            database (str):
                Database name
            collection (str):
                Collection name
            retry_writes (bool, default=False):
                Whether to retry writes. AWS DocumentDB doesn't support retryWrites.
        """
        params = {
            "retryWrites": retry_writes,
        }
        try:
            # Initialize connection
            self._client = MongoClient(connection_string, **params)
            self._db = self._client[database]
            self._collection = self._db[collection]
            logging.info("Connected to MongoDB")

        except PyMongoError as e:
            msg = f"Failed to initialize MongoDB connection: {e!s}"
            raise ConnectionError(msg) from None

    def _convert_id(self, id_value: str | ObjectId) -> ObjectId:
        """Convert string ID to ObjectId."""
        if isinstance(id_value, ObjectId):
            return id_value
        try:
            return ObjectId(id_value)
        except InvalidId:
            msg = f"Invalid ObjectId format: {id_value}"
            raise ValueError(msg) from None

    @property
    def collection(self) -> pymongo.collection.Collection:
        """Get the collection object."""
        return self._collection

    def __del__(self) -> None:
        """Close the MongoDB client."""
        self._client.close()

    @property
    def client(self) -> MongoClient:
        """Get the MongoDB client object."""
        return self._client

    def create_item(
        self,
        item: dict[str, Any],
    ) -> MongoDBResponse:
        """
        Create a new document in the collection.

        Parameters
        ----------
            item (dict): Document to create

        Returns
        -------
            dict: Response with created document
        """
        # Add timestamps
        item["created_at"] = datetime.datetime.now(datetime.UTC)
        item["updated_at"] = item["created_at"]

        try:
            result = self._collection.insert_one(item)
        except PyMongoError as e:
            return MongoDBResponse(success=False, error=str(e), item=None)
        else:
            logging.debug(f"Created item with ID: {result.inserted_id}")

            if result.inserted_id:
                return MongoDBResponse(success=True, error=None, item=item)
            return MongoDBResponse(
                success=False,
                error="Failed to create item",
                item=None,
            )

    def get_item(
        self,
        document_id: str | ObjectId,
        projection: dict[str, int] | None = None,
    ) -> MongoDBResponse:
        """
        Retrieve a document by ID.

        Parameters
        ----------
            document_id: Document ID (string or ObjectId)
            projection: Optional fields to include/exclude

        Returns
        -------
            dict: Document if found
        """
        obj_id = self._convert_id(document_id)
        try:
            item = self._collection.find_one({"_id": obj_id}, projection)

        except (PyMongoError, ValueError) as e:
            return MongoDBResponse(success=False, error=str(e), item=None)
        else:
            return MongoDBResponse(success=True, error=None, item=item)

    def update_item(
        self,
        document_id: str | ObjectId,
        updates: dict[str, Any],
        *,
        upsert: bool = False,
    ) -> MongoDBResponse:
        """
        Update a document by ID.

        Parameters
        ----------
            document_id: Document ID (string or ObjectId)
            updates (dict): Fields to update
            upsert (bool): Whether to create if document doesn't exist

        Returns
        -------
            dict: Updated document
        """
        obj_id = self._convert_id(document_id)

        # Prepare update document
        update_doc = {
            "$set": {
                **updates,
                "updated_at": datetime.datetime.now(datetime.UTC),
            },
        }
        try:
            result = self._collection.update_one(
                {"_id": obj_id},
                update_doc,
                upsert=upsert,
            )

        except (PyMongoError, ValueError) as e:
            return MongoDBResponse(success=False, error=str(e), item=None)
        else:
            if result.modified_count > 0 or (upsert and result.upserted_id):
                updated_item = self._collection.find_one({"_id": obj_id})
                return MongoDBResponse(success=True, error=None, item=updated_item)
            return MongoDBResponse(success=False, error="Document not found", item=None)

    def delete_item(self, document_id: str | ObjectId) -> MongoDBResponse:
        """
        Delete a document by ID.

        Parameters
        ----------
            document_id: Document ID (string or ObjectId)

        Returns
        -------
            dict: Deletion result
        """
        try:
            obj_id = self._convert_id(document_id)
            result = self._collection.delete_one({"_id": obj_id})

        except (PyMongoError, ValueError) as e:
            return MongoDBResponse(success=False, error=str(e), item=None)
        else:
            if result.deleted_count > 0:
                return MongoDBResponse(success=True, error=None, item=None)
            return MongoDBResponse(success=False, error="Document not found", item=None)

    def find_items(
        self,
        query: dict[str, Any],
        projection: dict[str, int] | None = None,
        sort: list[tuple] | None = None,
        limit: int | None = None,
        skip: int | None = None,
    ) -> MongoDBResponse:
        """
        Find documents matching query criteria.

        Parameters
        ----------
            query (dict):
                Query criteria
            projection (dict, optional):
                Fields to include/exclude
            sort (list, optional): list of (field, direction) tuples
            limit (int, optional): Maximum number of documents to return
            skip (int, optional): Number of documents to skip

        Returns
        -------
            dict: Matching documents
        """
        try:
            cursor = self._collection.find(query, projection)

        except PyMongoError as e:
            return MongoDBResponse(success=False, error=str(e), item=None)
        else:
            if sort:
                cursor = cursor.sort(sort)
            if skip:
                cursor = cursor.skip(skip)
            if limit:
                cursor = cursor.limit(limit)

            items = list(cursor)
            return MongoDBResponse(success=True, error=None, item=items)

    def aggregate(self, pipeline: list[dict[str, Any]]) -> MongoDBResponse:
        """
        Perform aggregation operations.

        Parameters
        ----------
            pipeline (list): Aggregation pipeline stages

        Returns
        -------
            dict: Aggregation results
        """
        try:
            results = list(self._collection.aggregate(pipeline))

        except PyMongoError as e:
            return MongoDBResponse(success=False, error=str(e), item=None)
        else:
            return MongoDBResponse(success=True, error=None, item=results)

    def create_index(
        self,
        keys: str | list[tuple],
        *,
        unique: bool = False,
        sparse: bool = False,
    ) -> MongoDBResponse:
        """
        Create an index on the collection.

        Parameters
        ----------
            keys: Either a single key name or list of (key, direction) tuples
            unique (bool): Whether the index should be unique
            sparse (bool): Whether the index should be sparse

        Returns
        -------
            dict: Index creation result
        """
        try:
            if isinstance(keys, str):
                keys = [(keys, ASCENDING)]

            index_name = self._collection.create_index(
                keys,
                unique=unique,
                sparse=sparse,
            )

        except PyMongoError as e:
            return MongoDBResponse(success=False, error=str(e), item=None)
        else:
            return MongoDBResponse(
                success=True,
                error=None,
                item={
                    "index_name": index_name,
                },
            )

    def drop_index(self, index_name: str) -> MongoDBResponse:
        """
        Drop an index from the collection.

        Parameters
        ----------
            index_name (str): Name of the index to drop

        Returns
        -------
            dict: Drop index result
        """
        try:
            self._collection.drop_index(index_name)

        except PyMongoError as e:
            return MongoDBResponse(success=False, error=str(e), item=None)
        else:
            return MongoDBResponse(success=True, error=None, item=None)

    def create_collection(self, name: str) -> MongoDBResponse:
        """
        Create a new collection in the database.

        Parameters
        ----------
            name (str): Collection name

        Returns
        -------
            dict: Collection creation result
        """
        try:
            self._db.create_collection(name)

        except PyMongoError as e:
            return MongoDBResponse(success=False, error=str(e), item=None)
        else:
            return MongoDBResponse(success=True, error=None, item=None)

    def delete_collection(self) -> MongoDBResponse:
        """
        Delete the collection.

        Returns
        -------
            dict: Deletion result
        """
        try:
            self._collection.drop()

        except PyMongoError as e:
            return MongoDBResponse(success=False, error=str(e), item=None)
        else:
            return MongoDBResponse(success=True, error=None, item=None)

    def delete_database(self) -> MongoDBResponse:
        """
        Delete the database.

        Returns
        -------
            dict: Deletion result
        """
        try:
            self._db.drop()
        except PyMongoError as e:
            return MongoDBResponse(success=False, error=str(e), item=None)
        else:
            return MongoDBResponse(success=True, error=None, item=None)

    @staticmethod
    def _date_to_objectid(date: datetime) -> str:
        """Convert datetime to ObjectId string.

        Parameters
        ----------
            date: datetime
                Datetime object to convert

        Returns
        -------
            str
                ObjectId string
        """
        return f"{int(date.timestamp()):024x}"

    def get_ids(
        self,
        batch_size: int = 1000,
        query: dict[str, Any] | None = None,
        sort: dict[str, int] | None = None,
        date_range: tuple | None = None,
    ) -> Generator[str, None, None]:
        """
        Generate ObjectIDs from collection with optional filtering.

        Parameters
        ----------
            batch_size: int, default 1000
                Number of documents to fetch per batch
            query: dict, optional
                Additional query filters
            sort: dict, optional
                Sort specification
            date_range: tuple, optional
                Tuple of (start_date, end_date) for filtering by ObjectID timestamp

        Yields
        ------
            ObjectID strings
        """
        try:
            # Build query
            final_query = query or {}

            # Add date range filter if provided
            if date_range:
                start_date, end_date = date_range
                final_query.update(
                    {
                        "_id": {
                            "$gte": self._date_to_objectid(start_date),
                            "$lte": self._date_to_objectid(end_date),
                        },
                    },
                )

            # Create cursor with batch size
            cursor = self._collection.find(
                final_query,
                {"_id": 1},  # Only fetch _id field
                batch_size=batch_size,
            )

            # Apply sorting if specified
            if sort:
                cursor = cursor.sort(list(sort.items()))

            # Yield IDs
            for doc in cursor:
                yield str(doc["_id"])

        except (PyMongoError, ValueError) as e:
            msg = f"Error generating ObjectIDs: {e}"
            raise RuntimeError(msg) from e

    @staticmethod
    @hook_impl
    def node_register() -> None:
        """Register a MongoDB interface."""
        ObjectBuilder.direct_register(
            id_str="MongoDBInterface",
            object_type=(
                "MongoDBInterface",
                "cuju_con_documentdb.p_mongodb_interface",
            ),
            config_type=(
                "MongoDBInterfaceConfig",
                "cuju_con_documentdb.p_mongodb_interface",
            ),
        )

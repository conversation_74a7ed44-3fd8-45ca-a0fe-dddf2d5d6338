"""
Test suite for the MongoDBInterface class.

This test suite includes the following test cases:
- test_initialization: Tests the initialization of the MongoDBInterface.
- test_initialization_invalid: Tests the initialization error handling of the
    MongoDBInterface.
- test_create_item: Tests the creation of an item in the MongoDBInterface.
- test_create_item_invalid: Tests the error handling during item creation in the
    MongoDBInterface.
- test_get_item: Tests retrieving an item by ID from the MongoDBInterface.
- test_update_item: Tests updating an item in the MongoDBInterface.
- test_delete_item: Tests deleting an item by ID from the MongoDBInterface.
- test_find_items: Tests finding items based on a query in the MongoDBInterface.
- test_get_ids: Tests retrieving a list of IDs from the MongoDBInterface.
Each test case uses mock objects to simulate the MongoDB client and collection
    interactions.
"""

from copy import deepcopy
from typing import Any
from unittest.mock import MagicMock, patch

import pytest
from bson.objectid import ObjectId
from cuju_con_documentdb.p_mongodb_interface import (
    MongoDBInterface,
    MongoDBInterfaceConfig,
)
from imgeta.pipeline.hook_markers import PLUGIN_NAMESPACE
from imgeta.pipeline.hook_specs import HookSpecs
from imgeta.plugin_base.builder.object_builder import ObjectBuilder
from pluggy import PluginManager
from pymongo.errors import PyMongoError


@pytest.mark.unittest
class TestMongoDBInterface:
    """Test cases for the MongoDBInterface class."""

    def test_initialization_invalid(self, mock_config: MongoDBInterfaceConfig) -> None:
        """Test the initialization error handling of the MongoDBInterface."""
        with (
            patch(
                "cuju_con_documentdb.p_mongodb_interface.MongoClient",
                side_effect=PyMongoError("Connection error"),
            ),
            pytest.raises(ConnectionError),
        ):
            MongoDBInterface(mock_config)

    def test_create_item(
        self,
        mock_config: MongoDBInterfaceConfig,
        mock_mongodb: dict[str, Any],
        test_item: dict[str, Any],
    ) -> None:
        """Test the creation of an item in the MongoDBInterface."""
        # Setup
        mock_mongodb["collection"].insert_one.return_value.inserted_id = ObjectId()

        # Execute
        interface = MongoDBInterface(mock_config)
        response = interface.create_item(test_item)

        # Check
        if not response.success:
            pytest.fail(f"Error creating item: {response.error}")
        if response.error is not None:
            pytest.fail(f"Error creating item: {response.error}")
        if "created_at" not in response.item:
            pytest.fail("Item does not contain a created_at timestamp")
        if "updated_at" not in response.item:
            pytest.fail("Item does not contain an updated_at timestamp")
        if mock_mongodb["collection"].insert_one.call_count != 1:
            pytest.fail("Insert not called once")

    def test_create_item_invalid(
        self,
        mock_config: MongoDBInterfaceConfig,
        mock_mongodb: dict[str, Any],
        test_item: dict[str, Any],
    ) -> None:
        """Test the error handling during item creation in the MongoDBInterface.

        Parameters
        ----------
        mock_config : MongoDBInterfaceConfig
            Mock configuration object.
        mock_mongodb : dict[str, Any]
            Mock MongoDB client and collection.
        test_item : dict[str, Any]
            Test item to create.
        """
        # Setup
        mock_mongodb["collection"].insert_one.side_effect = PyMongoError("Insert error")

        # Execute
        interface = MongoDBInterface(mock_config)
        response = interface.create_item(test_item)

        # Check
        if response.success:
            pytest.fail("Item creation should have failed")
        if response.error is None:
            pytest.fail("Error message should have been set")
        if response.error != "Insert error":
            pytest.fail("Error message should have been 'Insert error'")
        if response.item is not None:
            pytest.fail("Item should not have been returned")

    def test_get_item(
        self,
        mock_config: MongoDBInterfaceConfig,
        mock_mongodb: dict[str, Any],
        test_id: str,
        expected_item: dict[str, Any],
    ) -> None:
        """Test retrieving an item by ID from the MongoDBInterface.

        Parameters
        ----------
        mock_config : MongoDBInterfaceConfig
            Mock configuration object.
        mock_mongodb : dict[str, Any]
            Mock MongoDB client and collection.
        test_id : str
            Test item ID.
        expected_item : dict[str, Any]
            Expected item to be retrieved.
        """
        mock_mongodb["collection"].find_one.return_value = expected_item

        # Execute
        interface = MongoDBInterface(mock_config)
        response = interface.get_item(test_id)

        # Assert
        if not response.success:
            pytest.fail(f"Error creating item: {response.error}")
        if response.error is not None:
            pytest.fail(f"Error creating item: {response.error}")
        if response.item != expected_item:
            pytest.fail("Retrieved item does not match expected item")
        if mock_mongodb["collection"].find_one.call_count != 1:
            pytest.fail("Find one not called once")

    def test_update_item(
        self,
        mock_config: MongoDBInterfaceConfig,
        mock_mongodb: dict[str, Any],
        test_id: str,
        updated_item: dict[str, Any],
    ) -> None:
        """Test updating an item in the MongoDBInterface.

        Parameters
        ----------
        mock_config : MongoDBInterfaceConfig
            Mock configuration object.
        mock_mongodb : dict[str, Any]
            Mock MongoDB client and collection.
        test_id : str
            Test item ID.
        updated_item : dict[str, Any]
            Updated item to be retrieved.
        """
        # Setup
        updates = {"name": "updated"}
        mock_mongodb["collection"].update_one.return_value.modified_count = 1
        mock_mongodb["collection"].find_one.return_value = updated_item

        # Execute
        interface = MongoDBInterface(mock_config)
        response = interface.update_item(test_id, updates)

        # Assert
        if not response.success:
            pytest.fail(f"Error creating item: {response.error}")
        if response.error is not None:
            pytest.fail(f"Error creating item: {response.error}")
        if response.item != updated_item:
            pytest.fail("Retrieved item does not match updated item")
        if mock_mongodb["collection"].update_one.call_count != 1:
            pytest.fail("Update not called once")

    def test_delete_item(
        self,
        mock_config: MongoDBInterfaceConfig,
        mock_mongodb: dict[str, Any],
        test_id: str,
    ) -> None:
        """Test deleting an item by ID from the MongoDBInterface.

        Parameters
        ----------
        mock_config : MongoDBInterfaceConfig
            Mock configuration object.
        mock_mongodb : dict[str, Any]
            Mock MongoDB client and collection.
        test_id : str
            Test item ID.
        """
        # Setup
        mock_mongodb["collection"].delete_one.return_value.deleted_count = 1

        # Execute
        interface = MongoDBInterface(mock_config)
        response = interface.delete_item(test_id)

        # Assert
        if not response.success:
            pytest.fail(f"Error creating item: {response.error}")
        if response.error is not None:
            pytest.fail(f"Error creating item: {response.error}")
        if response.item is not None:
            pytest.fail("Item should not have been returned")
        if mock_mongodb["collection"].delete_one.call_count != 1:
            pytest.fail("Insert not called once")

    def test_find_items(
        self,
        mock_config: MongoDBInterfaceConfig,
        mock_mongodb: dict[str, Any],
        test_items: list[dict[str, Any]],
        mock_cursor: MagicMock,
    ) -> None:
        """Test finding items based on a query in the MongoDBInterface.

        Parameters
        ----------
        mock_config : MongoDBInterfaceConfig
            Mock configuration object.
        mock_mongodb : dict[str, Any]
            Mock MongoDB client and collection.
        test_items : list[dict[str, Any]]
            Test items to retrieve.
        mock_cursor : MagicMock
            Mock cursor object.
        """
        # Setup
        mock_mongodb["collection"].find.return_value = mock_cursor

        # Execute
        interface = MongoDBInterface(mock_config)
        response = interface.find_items({"name": "test"})

        # Assert
        if not response.success:
            pytest.fail(f"Error creating item: {response.error}")
        if response.error is not None:
            pytest.fail(f"Error creating item: {response.error}")
        if response.item != test_items:
            pytest.fail("Retrieved item does not match expected item")
        if mock_mongodb["collection"].find.call_count != 1:
            pytest.fail("Insert not called once")

    def test_get_ids(
        self,
        mock_config: MongoDBInterfaceConfig,
        mock_mongodb: dict[str, Any],
        test_ids: list[dict[str, Any]],
    ) -> None:
        """Test retrieving a list of IDs from the MongoDBInterface.

        Parameters
        ----------
        mock_config : MongoDBInterfaceConfig
            Mock configuration object.
        mock_mongodb : dict[str, Any]
            Mock MongoDB client and collection.
        test_ids : list[dict[str, Any]]
            Test IDs to retrieve.
        """
        # Setup
        mock_cursor = MagicMock()
        mock_cursor.__iter__.return_value = iter(test_ids)
        mock_mongodb["collection"].find.return_value = mock_cursor

        # Execute
        interface = MongoDBInterface(mock_config)
        ids = list(interface.get_ids())

        # Check
        if len(ids) != len(test_ids):
            pytest.fail("Unexpected number of IDs")
        if any(not isinstance(id_, str) for id_ in ids):
            pytest.fail("All IDs should be strings")
        if mock_mongodb["collection"].find.call_count != 1:
            pytest.fail("Find not called once")


@pytest.mark.unittest
class TestNodeRegister:
    """A test class for the 'MongoDBInterface' registration.

    This class tests the node registration for 'MongoDBInterface', ensuring that
    it is correctly registered within the plugin manager and available in the
    'ObjectBuilder' registry.
    """

    def test_node_register(
        self,
        mock_mongodb: dict[str, Any],  # noqa: ARG002
    ) -> None:
        """Test the registration of the 'MongoDBInterface' node.

        This test verifies that the 'MongoDBInterface' node is properly registered
        in the plugin manager and added to the 'ObjectBuilder' registry.
        """
        # --- create plugin manager from scratch ---
        registry = deepcopy(ObjectBuilder._registry)  # noqa: SLF001
        ObjectBuilder._registry = {}  # noqa: SLF001
        pm = PluginManager(PLUGIN_NAMESPACE)
        pm.add_hookspecs(HookSpecs)
        if (
            pm.is_registered(MongoDBInterface)
            or "MongoDBInterface" in ObjectBuilder._registry  # noqa: SLF001
        ):
            pytest.fail("'MongoDBInterface' already registered")

        # --- register node to plugin manager ---
        if not pm.is_registered(MongoDBInterface):
            pm.register(MongoDBInterface)
        pm.hook.node_register()

        # --- check whether registration has been successfully ---
        if (
            not pm.is_registered(MongoDBInterface)
            or "MongoDBInterface" not in ObjectBuilder._registry  # noqa: SLF001
        ):
            pytest.fail("'MongoDBInterface' could not have been registered")

        # --- check whether registered object can be actually build ---
        pipe_config = {
            "id": "test",
            "type": "MongoDBInterface",
            "param": {
                "connection_string": "mongodb://localhost:27017",
                "database": "test_db",
                "collection": "test_collection",
            },
        }
        (builder_class, _) = ObjectBuilder.get_builder_class(
            "MongoDBInterface",
        )
        pipe_obj = builder_class()(pipe_config, resolve_references=False)
        if not isinstance(pipe_obj, MongoDBInterface):
            pytest.fail(
                "'MongoDBInterface' object could have been successfully built "
                "through the registered classes.",
            )
        ObjectBuilder._registry = registry  # noqa: SLF001

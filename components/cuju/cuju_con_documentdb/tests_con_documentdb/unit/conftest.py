"""Fixtures that are used throughout the tests."""

from collections.abc import Generator
from typing import Any
from unittest.mock import MagicMock, patch

import pytest
from bson.objectid import ObjectId
from cuju_con_documentdb.p_mongodb_interface import MongoDBInterfaceConfig


# Test configuration
@pytest.fixture
def mock_config() -> MongoDBInterfaceConfig:
    """Fixture for a MongoDB interface configuration."""
    return MongoDBInterfaceConfig(
        connection_string="mongodb://localhost:27017",
        database="test_db",
        collection="test_collection",
    )


@pytest.fixture
def mock_mongodb() -> Generator[dict[str, MagicMock], None, None]:
    """Fixture for a mock MongoDB client."""
    with patch("cuju_con_documentdb.p_mongodb_interface.MongoClient") as mock_client:
        # Setup mock collection
        mock_collection = MagicMock()
        mock_db = MagicMock()
        mock_db.__getitem__.return_value = mock_collection
        mock_client.return_value.__getitem__.return_value = mock_db

        yield {
            "client": mock_client,
            "db": mock_db,
            "collection": mock_collection,
        }


@pytest.fixture
def test_item() -> dict[str, Any]:
    """Fixture for a basic test item."""
    return {"name": "test"}


@pytest.fixture
def test_id() -> str:
    """Fixture for a test ObjectId string."""
    return str(ObjectId())


@pytest.fixture
def test_object_id(test_id: str) -> ObjectId:
    """Fixture for a test ObjectId object."""
    return ObjectId(test_id)


@pytest.fixture
def expected_item(
    test_object_id: ObjectId,
    test_item: dict[str, Any],
) -> dict[str, Any]:
    """Fixture for an expected item with ID."""
    return {"_id": test_object_id, **test_item}


@pytest.fixture
def updated_item(test_object_id: ObjectId) -> dict[str, Any]:
    """Fixture for an updated item."""
    return {"_id": test_object_id, "name": "updated"}


@pytest.fixture
def test_items() -> list[dict[str, Any]]:
    """Fixture for a list of test items."""
    return [{"name": "test1"}, {"name": "test2"}]


@pytest.fixture
def test_ids() -> list[dict[str, Any]]:
    """Fixture for a list of test IDs."""
    return [{"_id": ObjectId()} for _ in range(3)]


@pytest.fixture
def mock_cursor(test_items: list[dict[str, Any]]) -> MagicMock:
    """Fixture for a mock cursor."""
    mock = MagicMock()
    mock.__iter__.return_value = iter(test_items)
    return mock

"""
Balanced landing recognizer node.

This module contains the BalancedLandingRecognizer node, which is responsible for
recognizing a balanced landing after a jump.

"""

from __future__ import annotations

from typing import TYPE_CHECKING, Annotated

from cuju_data_scene.actions.filter import SceneActionFilter
from cuju_data_scene.actions.jump_action import JumpEndAction
from cuju_data_scene.entities.person import Person
from cuju_data_scene.entities.scene_entity import EntityRole
from cuju_data_scene.p_scene_registry import MatchingCriteria
from cuju_data_scene.scene_node import SceneNode
from cuju_prov_provider.p_state_aggregator import StateAggregator  # noqa: TC002
from imgeta.pipeline.hook_markers import hook_impl
from imgeta.plugin_base.bases.config_base import ConfigBase
from imgeta.plugin_base.builder.object_builder import ObjectBuilder
from loguru import logger as logging
from pydantic import InstanceOf  # noqa: TC002

from cuju_action_balanced_landing.src.balanced_jump_classifier import (
    BalancedJumpClassifier,
)

if TYPE_CHECKING:
    from cuju_data_scene.p_scene_registry import SceneRegistry


class BalancedLandingRecognizerConfig(ConfigBase):
    """Configuration class for the 'BalancedLandingRecognizer' node.

    Attributes
    ----------
    model_provider : StateAggregator | None, optional (default=None)
        Model provider object.
    key_model : str | list[str] | None, optional (default=None)
        Key of the model in the model provider.
    window_shift : int, optional (default=10)
        The window shift for the feature extraction.
    """

    model_provider: InstanceOf[StateAggregator] | None = None
    key_model: str | None = None
    window_shift: Annotated[int, "The window shift for the feature extraction."] = 10


class BalancedLandingRecognizer(SceneNode):
    """Node for Recognizing whether a person has a balanced landing after a jump.

    The BalancedLandingRecognizer node is responsible for recognizing a balanced
    landing after a jump. It takes the last frame of the jump and uses a classifier
    to predict whether the person has a balanced landing within a window of frames.

    """

    def __init__(
        self,
        config: BalancedLandingRecognizerConfig,
    ) -> None:
        """Create a new instance of 'BalancedLandingRecognizer' node.

        Parameters
        ----------
        config : BalancedLandingRecognizerConfig
            Configuration object for the video meta saver.
        """
        super().__init__(
            on_control_flag=config.on_control_flag,
            state_storage=config.state_storage,
            ids=config.ids,
        )
        self._cfg = config
        # --- pass input arguments ---
        self._classifier = BalancedJumpClassifier(
            model_provider=self._cfg.model_provider,
            key_model=self._cfg.key_model,
            window_shift=self._cfg.window_shift,
        )

    def _understand_scene(
        self,
        scene_registry: SceneRegistry,
    ) -> None:
        """Apply the 'BalancedLandingRecognizer' node.

        Check if the main person has a balanced landing after a jump.

        Parameters
        ----------
        scene_registry : SceneRegistry
            The scene registry containing all entities.
        """
        mp = scene_registry.retrieve_entity(
            criteria=EntityRole.MAIN_PERSON,
            matching_criteria=MatchingCriteria.ROLE,
            entity_type=Person,
        )
        if mp is None:
            msg = "No main person found in scene registry. skipping jump recognition."
            logging.error(msg)
            return

        jump_end_action: JumpEndAction = scene_registry.retrieve_actions(
            SceneActionFilter(action_type=JumpEndAction)
        )
        if len(jump_end_action) == 0:
            msg = (
                "No jump end action found in scene registry.\t"
                "skipping jump recognition."
            )
            logging.error(msg)
            return
        jump_end_action = jump_end_action[0]
        (
            jump_end_action.balanced_landing,
            jump_end_action.window_frame,
        ) = self._classifier.predict(mp, jump_end_action)

        scene_registry.upsert_action(jump_end_action, overwrite=True)

    @staticmethod
    @hook_impl
    def node_register() -> None:
        """Register 'BalancedLandingRecognizer' as a node for use in pipeline."""
        ObjectBuilder.direct_register(
            id_str="BalancedLandingRecognizer",
            object_type=(
                "BalancedLandingRecognizer",
                "cuju_action_balanced_landing.p_balanced_landing_recognizer",
            ),
            config_type=(
                "BalancedLandingRecognizerConfig",
                "cuju_action_balanced_landing.p_balanced_landing_recognizer",
            ),
        )

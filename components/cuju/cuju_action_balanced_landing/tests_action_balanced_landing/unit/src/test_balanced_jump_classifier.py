"""Unit tests for the BalancedJumpClassifier class."""

from __future__ import annotations

from collections.abc import Sequence
from typing import TYPE_CHECKING
from unittest.mock import MagicMock, patch

import numpy as np
import pytest
from cuju_action_balanced_landing.src.balanced_jump_classifier import (
    BalancedJumpClassifier,
)

if TYPE_CHECKING:
    from cuju_data_scene.actions.jump_action import JumpEndAction
    from cuju_data_scene.entities.person import Person
    from cuju_prov_provider.p_state_aggregator import StateAggregator


class TestBalancedJumpClassifier:
    """Test class for the BalancedJumpClassifier."""

    def test_initialization(self, mock_model_provider: StateAggregator) -> None:
        """Test proper initialization of the classifier."""
        # Mock the onnxruntime session creation
        with patch("onnxruntime.InferenceSession") as mock_session:
            # Create the classifier
            classifier = BalancedJumpClassifier(
                model_provider=mock_model_provider,
                key_model="test_model",
                fps=30,
                window_shift=15,
            )

            # Check if the initialization was correct
            if classifier._fps != 30:  # noqa: SLF001
                pytest.fail(f"Expected fps to be 30, got {classifier._fps}")  # noqa: SLF001
            if classifier._window_shift != 15:  # noqa: SLF001
                pytest.fail(
                    f"Expected window_shift to be 15, got {classifier._window_shift}"  # noqa: SLF001
                )
            if classifier._key_model != "test_model":  # noqa: SLF001
                pytest.fail(
                    f"Expected key_model to be 'test_model', "
                    f"got {classifier._key_model}"  # noqa: SLF001
                )

            # Verify onnxruntime.set_seed was called
            mock_model_provider.get_state.assert_called_once_with("test_model")

            # Verify that the session was created with the correct parameters
            mock_session.assert_called_once()
            _args, kwargs = mock_session.call_args
            if kwargs.get("providers") != ["CPUExecutionProvider"]:
                pytest.fail(
                    f"Expected CPUExecutionProvider, got {kwargs.get('providers')}"
                )

    @pytest.mark.parametrize(
        ("input_data", "expected_shape"),
        [
            (np.array([1, 2, 3]), (1, 3)),  # 1D array should be reshaped to 2D
            (np.array([[1, 2, 3]]), (1, 3)),  # Already 2D, should remain the same
            (np.array([[[1, 2, 3]]]), (1, 1, 3)),  # 3D array should remain the same
        ],
    )
    def test_inference_reshaping(
        self,
        mock_onnx_session: MagicMock,
        input_data: np.ndarray,
        expected_shape: tuple[int, ...],
    ) -> None:
        """Test that inference correctly reshapes input data."""
        with (
            patch("onnxruntime.InferenceSession", return_value=mock_onnx_session),
            patch(
                "cuju_action_balanced_landing.src.balanced_jump_classifier.fetch_model",
                return_value=b"mock_model",
            ),
        ):
            classifier = BalancedJumpClassifier()
            classifier._ort_session = mock_onnx_session  # noqa: SLF001

            # Perform inference
            classifier._inference(input_data, mock_onnx_session)  # noqa: SLF001

            args, _ = mock_onnx_session.run.call_args
            input_dict = args[1]
            input_data = next(iter(input_dict.values()))

            # For 1D arrays, they should be reshaped to have a batch dimension
            if input_data.shape != expected_shape:
                pytest.fail(
                    f"Expected 1D array to be reshaped to "
                    f"{expected_shape}, got {input_data.shape}"
                )

    def test_predict(
        self,
        mock_person: Person,
        mock_jump_end_action: JumpEndAction,
        mock_onnx_session: MagicMock,
    ) -> None:
        """Test the predict method."""
        # Patch extract_features to return our mock features
        with (
            patch("onnxruntime.InferenceSession", return_value=mock_onnx_session),
            patch(
                "cuju_action_balanced_landing.src.balanced_jump_classifier.fetch_model",
                return_value=b"mock_model",
            ),
        ):
            classifier = BalancedJumpClassifier()
            classifier._ort_session = mock_onnx_session  # noqa: SLF001

            # Call predict
            result = classifier.predict(mock_person, mock_jump_end_action)

            # Verify result type
            if not isinstance(result, tuple):
                pytest.fail(f"Expected a tuple result, got {type(result)}")

            if not isinstance(result[0], bool):
                pytest.fail(f"Expected a boolean result, got {type(result[0])}")

            if not isinstance(result[1], Sequence):
                pytest.fail(f"Expected a sequence result, got {type(result[1])}")

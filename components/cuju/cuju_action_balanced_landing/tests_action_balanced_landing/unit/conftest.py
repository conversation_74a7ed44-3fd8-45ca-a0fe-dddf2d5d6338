"""Pytest fixtures for the main_person_reidentification module."""

from unittest.mock import MagicMock, patch

import onnxruntime
import pytest
from cuju_action_balanced_landing.p_balanced_landing_recognizer import (
    BalancedLandingRecognizer,
    BalancedLandingRecognizerConfig,
)
from cuju_data_scene.p_scene_registry import SceneRegistry
from cuju_prov_provider.p_state_aggregator import StateAggregator
from imgeta.plugin_base.bases.control_flag import ControlFlag
from imgeta.plugin_base.imgeta_tree.imgeta_views import ImgetaViews


# Fixtures at module level that are used by the test class
@pytest.fixture
def model_provider() -> StateAggregator:
    """Fixture for StateAggregator."""
    return MagicMock(spec=StateAggregator)


@pytest.fixture
def imgeta_views() -> ImgetaViews:
    """Fixture for ImgetaViews."""
    return MagicMock(spec=ImgetaViews)


@pytest.fixture
def scene_registry() -> SceneRegistry:
    """Fixture for SceneRegistry."""
    return MagicMock(spec=SceneRegistry)


@pytest.fixture
def default_config(model_provider: StateAggregator) -> BalancedLandingRecognizerConfig:
    """Fixture for default BalancedLandingRecognizerConfig."""
    return BalancedLandingRecognizerConfig(
        on_control_flag=ControlFlag.END_OF_STREAM,
        model_provider=model_provider,
        key_model="balanced_model",
        window_shift=15,
    )


@pytest.fixture
def recognizer(
    default_config: BalancedLandingRecognizerConfig,
) -> BalancedLandingRecognizer:
    """Fixture for BalancedLandingRecognizer."""
    with (
        patch(
            "cuju_inference_onnx.utils.fetch_model",
            return_value=MagicMock(spec=bytes),
        ),
        patch(
            "onnxruntime.InferenceSession",
            return_value=MagicMock(spec=onnxruntime.InferenceSession),
        ),
    ):
        recognizer = BalancedLandingRecognizer(config=default_config)
    recognizer._classifier.predict = MagicMock(return_value=(True, [20, 30]))  # noqa: SLF001
    return recognizer

"""Test Cases for p_run_start_recognizer."""

from copy import deepcopy
from unittest.mock import MagicMock

import pytest
from cuju_action_timer.p_timer import (
    SceneTimer,
    SceneTimerConfig,
)
from cuju_data_scene.actions.timer_actions import TimerStartAction, TimeUpAction
from cuju_data_scene.entities.cone import Cone
from cuju_data_scene.entities.person import Person
from cuju_prov_provider.p_memory_obj_provider import MemoryObjProvider
from imgeta.pipeline.hook_markers import PLUGIN_NAMESPACE
from imgeta.pipeline.hook_specs import HookSpecs
from imgeta.plugin_base.builder.object_builder import ObjectBuilder
from imgeta.plugin_base.imgeta_tree.frame_time import FrameTime
from pluggy import PluginManager


@pytest.mark.unittest
class TestActionTimer:
    """Test cases for TestActionTimer class."""

    def test_local_apply_creates_timer_start_action(self) -> None:
        """Create TimerStartAction & TimeUpAction with correct time and duration."""
        duration, start_frame, frame_rate = 10, 100, 30
        video_length = start_frame + duration * frame_rate + 1

        cone_mock = MagicMock(spec=Cone)
        cone_mock.get_frame_index.return_value = 0
        cone_mock.to_point.return_value = (0, 0)
        cone_mock.frame_time = [MagicMock(spec=FrameTime)] * video_length
        cone_mock.frame_time[0].frame_rate = frame_rate
        cone_mock.id = "cone_id"
        cone_mock.frame_numbers = list(range(video_length))
        scene_registry_mock = MagicMock()
        scene_registry_mock.retrieve_all_entities.return_value = [cone_mock]

        main_person_mock = MagicMock(spec=Person)
        main_person_mock.get_frame_index.return_value = 0
        main_person_mock.to_point.return_value = (0, 0)
        main_person_mock.frame_time = [MagicMock(spec=FrameTime)] * video_length
        main_person_mock.frame_time[0].frame_rate = frame_rate
        main_person_mock.id = "person_id"
        main_person_mock.frame_numbers = list(range(video_length))

        scene_registry_mock.retrieve_entity.return_value = main_person_mock

        config = SceneTimerConfig(
            duration_in_seconds=duration,
            state_storage=MagicMock(spec=MemoryObjProvider),
        )
        config.state_storage.get_state.return_value = start_frame

        timer = SceneTimer(config)
        timer._local_apply(  # noqa: SLF001
            imgeta_views=MagicMock(),
            scene_registry=scene_registry_mock,
            precheck_existing_codes=False,
        )

        scene_registry_mock.upsert_action.assert_called()
        args_list = scene_registry_mock.upsert_action.call_args_list
        assert len(args_list) == 2  # Ensure upsert_action is called twice
        action1 = args_list[0][0][0]
        assert isinstance(action1, TimerStartAction)
        assert action1.time == cone_mock.frame_time[0], "Wrong TimerStart time"
        assert action1.duration_in_seconds == duration, "Wrong TimerStart duration"
        assert action1.entity_id == "person_id", "Wrong TimerStart entity_id"

        action2 = args_list[1][0][0]
        assert isinstance(action2, TimeUpAction)
        assert action2.position == (0, 0)
        main_person_mock.to_point.assert_called()


@pytest.mark.unittest
class TestNodeRegister:
    """A test class for the 'SceneTimer' registration.

    This class tests the node registration for 'SceneTimer', ensuring that
    it is correctly registered within the plugin manager and available in the
    'ObjectBuilder' registry.
    """

    def test_node_register(self) -> None:
        """Test the registration of the 'SceneTimer' node.

        This test verifies that the 'SceneTimer' node is properly
        registered in the plugin manager and added to the 'ObjectBuilder' registry.
        """
        # --- create plugin manager from scratch ---
        registry = deepcopy(ObjectBuilder._registry)  # noqa: SLF001
        ObjectBuilder._registry = {}  # noqa: SLF001
        pm = PluginManager(PLUGIN_NAMESPACE)
        pm.add_hookspecs(HookSpecs)
        if (
            pm.is_registered(SceneTimer) or "SceneTimer" in ObjectBuilder._registry  # noqa: SLF001
        ):
            pytest.fail("'SceneTimer' already registered")

        # --- register node to plugin manager ---
        if not pm.is_registered(SceneTimer):
            pm.register(SceneTimer)
        pm.hook.node_register()

        # --- check whether registration has been successfully ---
        if (
            not pm.is_registered(SceneTimer)
            or "SceneTimer" not in ObjectBuilder._registry  # noqa: SLF001
        ):
            pytest.fail("'SceneTimer' could not have been registered")

        # --- check whether registered object can be actually build ---
        pipe_config = {
            "id": "app_recognize_jump",
            "type": "SceneTimer",
            "param": {
                "duration_in_seconds": 10,
                "state_storage": MagicMock(spec=MemoryObjProvider),
            },
        }
        (builder_class, _) = ObjectBuilder.get_builder_class(
            "SceneTimer",
        )
        pipe_obj = builder_class()(pipe_config, resolve_references=False)
        if not isinstance(pipe_obj, SceneTimer):
            pytest.fail(
                "'SceneTimer' object could have been successfully built "
                "through the registered classes.",
            )
        ObjectBuilder._registry = registry  # noqa: SLF001

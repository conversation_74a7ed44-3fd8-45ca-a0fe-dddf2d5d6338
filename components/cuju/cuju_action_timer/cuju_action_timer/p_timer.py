"""Scene Timer for timed exercises."""

from __future__ import annotations

from typing import Any

from cuju_data_scene.actions.timer_actions import TimerStartAction, TimeUpAction
from cuju_data_scene.constants import EntityRole
from cuju_data_scene.entities.cone import Cone
from cuju_data_scene.entities.person import Person
from cuju_data_scene.entities.tracked_entity import ToPointMethod
from cuju_data_scene.p_scene_registry import MatchingCriteria, SceneRegistry
from cuju_data_scene.scene_node import SceneNode
from imgeta.pipeline.hook_markers import hook_impl
from imgeta.plugin_base.bases.config_base import ConfigBase
from imgeta.plugin_base.bases.control_flag import ControlFlag
from imgeta.plugin_base.builder.object_builder import ObjectBuilder
from loguru import logger as logging


class SceneTimerConfig(ConfigBase):
    """Configuration for SceneTimer.

    This SceneTimer is a very lightweight plugin that shall be used in timer based
    exercises, like <PERSON><PERSON><PERSON>, Wall Ball and Corridor. It will compute the end time
    of the timer based on the start signal from the backend and the configured
    duration.

    Attributes
    ----------
    duration_in_seconds: int
        The duration of the timer in seconds. Defaults to 60 seconds.
    state_storage: MemoryObjProvider
        The global state storage holding the information from the backend.
    on_control_flag: ControlFlag
        The control flag used for controlling the execution. Defaults to END_OF_STREAM.
    """

    duration_in_seconds: int = 60
    state_storage: Any
    on_control_flag: ControlFlag = ControlFlag.END_OF_STREAM


class SceneTimer(SceneNode):
    """SceneTimer."""

    def __init__(self, config: SceneTimerConfig, **kwargs: dict) -> None:
        """Initialize SceneTimer."""
        super().__init__(
            ids=config.ids,
            on_control_flag=config.on_control_flag,
            state_storage=config.state_storage,
            **kwargs,
        )
        self._cfg = config

    def _understand_scene(
        self,
        scene_registry: SceneRegistry,
        **kwargs: dict,  # noqa: ARG002
    ) -> None:
        """Calculate the end of the timer and upsert the TimeUpAction."""
        cone = scene_registry.retrieve_all_entities(
            entity_type=Cone,
        )[0]
        main_person = scene_registry.retrieve_entity(
            criteria=EntityRole.MAIN_PERSON,
            matching_criteria=MatchingCriteria.ROLE,
            entity_type=Person,
        )
        start_frame_number = self._cfg.state_storage.get_state("start_frame")

        start_frame_idx = cone.get_frame_index(start_frame_number)
        start_frame_time = cone.frame_time[start_frame_idx]

        start_action = TimerStartAction(
            time=start_frame_time,
            entity_id=main_person.id,
            duration_in_seconds=self._cfg.duration_in_seconds,
        )
        scene_registry.upsert_action(start_action)
        expected_end_frame_number = start_frame_number + round(
            self._cfg.duration_in_seconds * start_frame_time.frame_rate
        )
        end_frame_idx, end_frame_number = min(
            enumerate(main_person.frame_numbers),
            key=lambda x: abs(x[1] - expected_end_frame_number),
        )

        if end_frame_number != expected_end_frame_number:
            logging.warning(
                f"Main Person not detected in Timer End Frame (Frame "
                f"{expected_end_frame_number}). Using closest frame with detection "
                f"instead (Frame {end_frame_number})"
            )
        end_frame_time = main_person.frame_time[end_frame_idx]

        end_action = TimeUpAction(
            time=end_frame_time,
            entity_id=main_person.id,
            position=main_person.to_point(
                method=ToPointMethod.KPT_CENTER,
                frame=end_frame_number,
                keypoints=["left_hip", "right_hip"],
            ),
        )

        scene_registry.upsert_action(end_action)

    @staticmethod
    @hook_impl
    def node_register() -> None:
        """Register a SceneTimer."""
        ObjectBuilder.direct_register(
            id_str="SceneTimer",
            object_type=(
                "SceneTimer",
                "cuju_action_timer.p_timer",
            ),
            config_type=(
                "SceneTimerConfig",
                "cuju_action_timer.p_timer",
            ),
        )

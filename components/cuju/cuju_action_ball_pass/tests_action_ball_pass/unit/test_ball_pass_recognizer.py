"""Unit Tests for BallPass Action Recognizer."""

from collections.abc import Callable
from copy import deepcopy

import pytest
from cuju_action_ball_pass.p_ball_pass_recognizer import (
    BallPassActionRecognizer,
    BallPassActionRecognizerConfig,
)
from cuju_data_scene.actions.ball_pass_action import BallPassedAction
from cuju_data_scene.actions.filter import SceneActionFilter
from cuju_data_scene.entities.direction import Direction
from cuju_data_scene.p_scene_registry import SceneRegistry
from imgeta.pipeline.hook_markers import PLUGIN_NAMESPACE
from imgeta.pipeline.hook_specs import HookSpecs
from imgeta.plugin_base.builder.object_builder import ObjectBuilder
from pluggy import PluginManager


@pytest.mark.skip
class TestBallPassActionRecognizer:
    """A test class for the 'BallPassActionRecognizer' class.

    This class contains unit tests for the 'BallPassActionRecognizer' class,
    ensuring that it behaves as expected in various scenarios.
    """

    @pytest.mark.unittest
    def test_get_shooting_foot(self, request: pytest.FixtureRequest) -> None:
        """Test the get_shooting_foot method of BallPassActionRecognizer."""
        # --- setup ---
        recognizer = BallPassActionRecognizer(
            BallPassActionRecognizerConfig(
                ids="test",
                var_window_size=3,
            ),
        )
        main_person = request.getfixturevalue("person")
        ball_pass_detected_frames = [5, 16]
        ball_pass_start_frames = [2, 13]
        # --- test get_shooting_foot ---
        foot_used = recognizer._get_shooting_foot(  # noqa: SLF001
            main_person=main_person,
            ball_pass_detected_frames=ball_pass_detected_frames,
            ball_pass_start_frames=ball_pass_start_frames,
        )
        # --- check if the foot used is correct ---
        if foot_used != ["right_foot", "right_foot"]:
            pytest.fail("BallPassActionRecognizer did not recognize the foot used")

    @pytest.mark.parametrize("direction", [Direction.RTL, Direction.LTR])
    def test_get_valid_ball_pass_action(
        self,
        direction: Direction,
        create_scene_registry: Callable[..., SceneRegistry],
    ) -> None:
        """Test the get_valid_ball_pass_action method of BallPassActionRecognizer."""
        # --- setup ---
        recognizer = BallPassActionRecognizer(
            BallPassActionRecognizerConfig(
                ids="test",
                var_window_size=3,
            ),
        )

        scene_registry = create_scene_registry(direction)
        actions, lines_entities_dict = recognizer._map_lines_to_entities(  # noqa: SLF001
            scene_registry=scene_registry,
            lines=[
                "perspective-line-middle",
                "perspective-line-bottom",
                "perspective-line-top",
            ],
        )

        # --- test get_valid_ball_pass_action ---
        valid_actions = recognizer._get_ball_pass_action(  # noqa: SLF001
            actions=actions,
            lines_entities_dict=lines_entities_dict,
            setup_direction=direction,
        )
        # --- check if the action is valid ---
        if direction == Direction.RTL:
            if not valid_actions:
                pytest.fail("BallPassActionRecognizer did not recognize the action")
            if len(valid_actions) != 1:
                pytest.fail(
                    "BallPassActionRecognizer did not recognize the action correctly",
                )
        if direction == Direction.LTR and valid_actions:
            pytest.fail("BallPassActionRecognizer should not recognize the action")

    @pytest.mark.parametrize("direction", [Direction.RTL, Direction.LTR])
    def test_get_invalid_ball_pass_action(
        self,
        direction: Direction,
        create_scene_registry: Callable[..., SceneRegistry],
    ) -> None:
        """Test the get_invalid_ball_pass_action method of BallPassActionRecognizer."""
        # --- setup ---
        recognizer = BallPassActionRecognizer(
            BallPassActionRecognizerConfig(
                ids="test",
                var_window_size=3,
            ),
        )

        scene_registry = create_scene_registry(direction)
        actions, lines_entities_dict = recognizer._map_lines_to_entities(  # noqa: SLF001
            scene_registry=scene_registry,
            lines=[
                "perspective-line-middle",
                "perspective-line-bottom",
                "perspective-line-top",
            ],
        )

        # --- test get_valid_ball_pass_action ---
        invalid_actions = recognizer._get_invalid_ball_pass_action(  # noqa: SLF001
            actions=actions,
            lines_entities_dict=lines_entities_dict,
            setup_direction=direction,
        )
        # --- check if the action is valid ---
        if direction == Direction.LTR:
            if not invalid_actions:
                pytest.fail("BallPassActionRecognizer did not recognize the action")
            if len(invalid_actions) != 1:
                pytest.fail(
                    "BallPassActionRecognizer did not recognize the action correctly",
                )
        if direction == Direction.RTL and invalid_actions:
            pytest.fail("BallPassActionRecognizer should not recognize the action")

    @pytest.mark.parametrize("direction", [Direction.RTL, Direction.LTR])
    def test_local_apply(
        self, direction: Direction, create_scene_registry: Callable[..., SceneRegistry]
    ) -> None:
        """Test the local_apply method of BallPassActionRecognizer.

        This test verifies that the local_apply method correctly processes
        input data and returns the expected output.
        """
        # --- setup ---
        recognizer = BallPassActionRecognizer(
            BallPassActionRecognizerConfig(
                ids="test",
                var_window_size=3,
            ),
        )
        scene_registry = create_scene_registry(direction)

        # --- test local_apply ---
        recognizer._local_apply(  # noqa: SLF001
            scene_registry=scene_registry,
        )
        # --- check if the action is recognized ---
        if direction == Direction.RTL:
            ball_pass_action = scene_registry.retrieve_actions(
                SceneActionFilter(action_type=BallPassedAction),
            )
            if not ball_pass_action:
                pytest.fail("BallPassActionRecognizer did not recognize the action")
            if ball_pass_action[0].validation_status != "valid":
                pytest.fail(
                    "BallPassActionRecognizer did not recognize the action correctly",
                )
            if ball_pass_action[0].shooting_foot != "right_foot":
                pytest.fail(
                    "BallPassActionRecognizer did not recognize the action correctly",
                )
        else:
            ball_pass_action = scene_registry.retrieve_actions(
                SceneActionFilter(action_type=BallPassedAction),
            )
            if ball_pass_action[0].validation_status != "invalid":
                pytest.fail(
                    "BallPassActionRecognizer did not recognize the action correctly",
                )
            if ball_pass_action[0].shooting_foot != "right_foot":
                pytest.fail(
                    "BallPassActionRecognizer did not recognize the action correctly",
                )


@pytest.mark.unittest
class TestNodeRegister:
    """A test class for the 'BallPassActionRecognizer' registration.

    This class tests the node registration for 'BallPassActionRecognizer', ensuring that
    it is correctly registered within the plugin manager and available in the
    'ObjectBuilder' registry.
    """

    def test_node_register(self) -> None:
        """Test the registration of the 'BallPassActionRecognizer' node.

        This test verifies that the 'BallPassActionRecognizer' node is properly
        registered in the plugin manager and added to the 'ObjectBuilder' registry.
        """
        # --- create plugin manager from scratch ---
        registry = deepcopy(ObjectBuilder._registry)  # noqa: SLF001
        ObjectBuilder._registry = {}  # noqa: SLF001
        pm = PluginManager(PLUGIN_NAMESPACE)
        pm.add_hookspecs(HookSpecs)
        if (
            pm.is_registered(BallPassActionRecognizer)
            or "BallPassActionRecognizer" in ObjectBuilder._registry  # noqa: SLF001
        ):
            pytest.fail("'BallPassActionRecognizer' already registered")

        # --- register node to plugin manager ---
        if not pm.is_registered(BallPassActionRecognizer):
            pm.register(BallPassActionRecognizer)
        pm.hook.node_register()

        # --- check whether registration has been successfully ---
        if (
            not pm.is_registered(BallPassActionRecognizer)
            or "BallPassActionRecognizer" not in ObjectBuilder._registry  # noqa: SLF001
        ):
            pytest.fail("'BallPassActionRecognizer' could not have been registered")

        # --- check whether registered object can be actually build ---
        pipe_config = {
            "id": "app_recognize_ball_pass",
            "type": "BallPassActionRecognizer",
            "param": {},
        }
        (builder_class, _) = ObjectBuilder.get_builder_class(
            "BallPassActionRecognizer",
        )
        pipe_obj = builder_class()(pipe_config, resolve_references=False)
        if not isinstance(pipe_obj, BallPassActionRecognizer):
            pytest.fail(
                "'BallPassActionRecognizer' object could have been successfully built "
                "through the registered classes.",
            )
        ObjectBuilder._registry = registry  # noqa: SLF001

"""Conftests for cuju_action_ball_pass."""

import uuid
from collections.abc import Callable
from unittest.mock import <PERSON><PERSON><PERSON>

import numpy as np
import pytest
from cuju_data_detection.bounding_box.box_base import BoundingBox
from cuju_data_pose.pose2d.pose_base import Pose
from cuju_data_scene.actions.line_crossed_action import Line<PERSON>rossedAction
from cuju_data_scene.actions.setup_checked_action import Setup<PERSON>heckedAction
from cuju_data_scene.constants import EntityR<PERSON>, ExerciseId
from cuju_data_scene.entities.direction import Direction
from cuju_data_scene.entities.line import LineEntity
from cuju_data_scene.entities.person import Person
from cuju_data_scene.p_scene_registry import MatchingCriteria, SceneRegistry
from imgeta.plugin_base.imgeta_tree.frame_time import FrameTime


def get_frame_time(frame_number: int, fps: float = 10.0) -> FrameTime:
    """Get the frame time for a given frame number."""
    frame_time = 1 / fps
    return FrameTime(
        frame_number=frame_number,
        fps=fps,
        frame_time=frame_time,
        time_since_start=frame_number * frame_time,
    )


@pytest.fixture
def person() -> Person:
    """Create a Person entity for testing."""
    np.random.seed(0)  # For reproducibility
    boxes = []
    poses = []
    frame_times = []
    for i in range(30):
        boxes.append(BoundingBox(coords=np.array([10, 10, 20, 20])))
        poses.append(Pose(coords=np.random.rand(17, 2), visibility=np.random.rand(17)))
        frame_times.append(get_frame_time(i))

    return Person(
        role=EntityRole.MAIN_PERSON,
        name="test_person",
        boxes=boxes,
        poses=poses,
        frame_time=frame_times,
    )


def setup_action_factory(direction: Direction) -> Callable[..., SetupCheckedAction]:
    """Create setup checked actions with different directions."""
    action = SetupCheckedAction(
        exercise_id=ExerciseId.WALL_BALL_WB,
        time=get_frame_time(0),
    )
    action.setup_direction = direction
    return action


@pytest.fixture
def create_scene_registry(
    person: Person,
) -> Callable[..., SceneRegistry]:
    """Fixture for SceneRegistry."""

    def _create_registry(direction: Direction = Direction.LTR) -> SceneRegistry:
        scene_registry = SceneRegistry()
        # Add person
        scene_registry.upsert_entity(person, MatchingCriteria.ID)
        scene_registry.upsert_action(
            setup_action_factory(direction),
        )
        line_names = [
            "perspective-line-middle",
            "perspective-line-bottom",
            "perspective-line-top",
        ]
        line_ids = [str(uuid.uuid4()), str(uuid.uuid4()), str(uuid.uuid4())]
        moc_line_entity = [
            MagicMock(spec=LineEntity),
            MagicMock(spec=LineEntity),
            MagicMock(spec=LineEntity),
        ]
        for line_name, line_id, line_entity in zip(
            line_names, line_ids, moc_line_entity, strict=False
        ):
            line_entity.name = line_name
            line_entity.id = line_id
            scene_registry.upsert_entity(line_entity, MatchingCriteria.ID)

        # mock line cross action for MB
        mock_line_cross_action = [
            MagicMock(spec=LineCrossedAction),
            MagicMock(spec=LineCrossedAction),
            MagicMock(spec=LineCrossedAction),
            MagicMock(spec=LineCrossedAction),
        ]
        directions = [
            {"direction": Direction.LTR, "line_id": line_ids[0]},
            {"direction": Direction.RTL, "line_id": line_ids[1]},
            {"direction": Direction.RTL, "line_id": line_ids[0]},
            {"direction": Direction.LTR, "line_id": line_ids[0]},
        ]
        for cross_direction, action in zip(
            directions, mock_line_cross_action, strict=False
        ):
            action.id = str(uuid.uuid4())
            action.entity_id = "MB"
            action.crossing_direction = cross_direction["direction"]
            action.line_id = cross_direction["line_id"]
            action.time = MagicMock(spec=FrameTime)
            action.frame_number = MagicMock()
            scene_registry.upsert_action(action)

        return scene_registry

    return _create_registry

"""BallPassActionRecognizer module.

Recognizes ball-pass actions by orchestrating line-crossing detection and
foot-movement analysis.

    Workflow:
      1. Maps valid, invalid, and optional high lines to scene entity IDs.
      2. Retrieves ball line-cross events and pairs them into candidate passes
         based on setup direction and any intervening crossings.
      3. If a high line is specified, validates passes that cross it an even number
         of times and in alternating directions.
      4. Determines which foot the main person used for each pass by comparing
         ankle-trajectory variances in a configurable frame window.
      5. Converts validated pass events and foot data into BallPassedAction objects.

    This class delegates detection to PassDetector and analysis to FootAnalyzer,
    supplying a clean interface to produce high-level pass actions for the pipeline.
"""

from cuju_data_scene.actions.ball_pass_action import BallPassedAction
from cuju_data_scene.actions.filter import SceneActionFilter
from cuju_data_scene.actions.setup_checked_action import SetupCheckedAction
from cuju_data_scene.constants import EntityRole
from cuju_data_scene.entities.ball import Ball
from cuju_data_scene.entities.line import LineEntity
from cuju_data_scene.entities.person import Person
from cuju_data_scene.p_scene_registry import MatchingCriteria, SceneRegistry
from cuju_data_scene.scene_node import SceneNode
from imgeta.pipeline.hook_markers import hook_impl
from imgeta.plugin_base.bases.config_base import ConfigBase
from imgeta.plugin_base.builder.object_builder import ObjectBuilder
from loguru import logger

from cuju_action_ball_pass.src.foot_movement_analyzer import (
    FootAnalyzer,
)
from cuju_action_ball_pass.src.pass_detector import (
    PassDetector,
)


class BallPassActionRecognizerConfig(ConfigBase):
    """Configuration for BallPassActionRecognizer.

    Attributes
    ----------
    var_window_size : int
        The size of the variance window for foot analysis.
    window_shift: int
        Due to the interpolation and imperfect ball detection,
        we oftentimes detect passes very early. In order to
        compensate that, we can set this parameter to shift the
        window "beyond" the pass detection frame in order to
        capture the full swinging motion. This parameter controls
        this behavior.
    """

    var_window_size: int = 9
    window_shift: int = 4


class BallPassActionRecognizer(SceneNode):
    """Orchestrates detection, analysis, and creation of BallPassedAction."""

    def __init__(self, config: BallPassActionRecognizerConfig) -> None:
        """Initialize the BallPassActionRecognizer.

        Parameters
        ----------
        config : BallPassActionRecognizerConfig
            The configuration for the recognizer.
        """
        super().__init__(
            ids=config.ids,
            on_control_flag=config.on_control_flag,
            state_storage=config.state_storage,
        )
        self.config = config

    def recognize_actions(
        self,
        scene_registry: SceneRegistry,
        valid_line: str,
        invalid_lines: list[str] | None = None,
    ) -> list[BallPassedAction]:
        """Recognize ball pass actions.

        Parameters
        ----------
        scene_registry : SceneRegistry
            The scene registry to use for action recognition.
        valid_line : str
            The name of the valid line for pass detection.
        invalid_lines : list[str]
            The names of the invalid lines for pass detection.

        Returns
        -------
        list[BallPassedAction]
            A list of recognized BallPassedAction objects.
        """
        # Map lines
        if invalid_lines is None:
            invalid_lines = []
        lines = [valid_line, *invalid_lines]
        line_map = {}
        for name in lines:
            line = scene_registry.retrieve_entity(
                name, MatchingCriteria.NAME, LineEntity
            )
            if line is None:
                msg = (
                    f"Tried to use line `{name}` in BallPassActionRecognizer, but a"
                    " line with this name does not exist in the scene registry."
                )
                logger.warning(msg)
            else:
                line_map[name] = line.id

        # Setup direction
        setup = scene_registry.retrieve_actions(
            SceneActionFilter(action_type=SetupCheckedAction)
        )
        if not setup:
            return []
        setup_direction = setup[0].setup_direction

        # Ball entity
        ball = scene_registry.retrieve_entity(
            criteria=EntityRole.MAIN_BALL,
            matching_criteria=MatchingCriteria.ROLE,
            entity_type=Ball,
        )
        if not ball:
            return []

        # Detect passes
        detector = PassDetector(scene_registry, ball.id, line_map)
        passes = detector.get_pass_events(valid_line, invalid_lines, setup_direction)

        # Analyze feet
        person = scene_registry.retrieve_entity(
            criteria=EntityRole.MAIN_PERSON,
            matching_criteria=MatchingCriteria.ROLE,
            entity_type=Person,
        )
        feet = None
        if person:
            detect_frames = [start.frame_number for start, _, _ in passes]
            feet = FootAnalyzer(
                self.config.var_window_size, self.config.window_shift
            ).determine_feet(person, ball, detect_frames)

        # Transform to BallPassedAction
        results: list[BallPassedAction] = []
        for idx, (start, end, valid) in enumerate(passes):
            if feet[idx] is None:
                continue
            results.append(
                BallPassedAction(
                    action_window=(start.frame_number, end.frame_number),
                    position=start.position,
                    side=feet[idx][0],
                    is_valid=valid,
                    passing_line_name=valid_line,
                    time=start.time,
                    entity_id=ball.id,
                    analysis_window=feet[idx][1:],
                )
            )
        return results

    def _understand_scene(
        self,
        scene_registry: SceneRegistry,
        valid_line: str,
        invalid_lines: list[str] | None = None,
    ) -> None:
        """Apply the action recognizer to the scene registry.

        Parameters
        ----------
        scene_registry : SceneRegistry
            The scene registry to apply the action recognizer to.
        valid_line : str
            The name of the valid line for pass detection.
        invalid_lines : list[str]
            The names of the invalid lines for pass detection.
        """
        actions = self.recognize_actions(
            scene_registry=scene_registry,
            valid_line=valid_line,
            invalid_lines=invalid_lines,
        )
        for action in actions:
            scene_registry.upsert_action(action, overwrite=True)

    @staticmethod
    @hook_impl
    def node_register() -> None:
        """Register a BallPassActionRecognizer."""
        ObjectBuilder.direct_register(
            id_str="BallPassActionRecognizer",
            object_type=(
                "BallPassActionRecognizer",
                "cuju_action_ball_pass.p_ball_pass_recognizer",
            ),
            config_type=(
                "BallPassActionRecognizerConfig",
                "cuju_action_ball_pass.p_ball_pass_recognizer",
            ),
        )

"""Conftest for cuju_embedding_fastreid tests."""

import tempfile
from typing import Any
from unittest import mock

import cv2 as cv
import numpy as np
import pytest
from cuju_agg_frames.p_storage_frame_aggregator import (
    StorageFrameAggregator,
    StorageFrameAggregatorConfig,
)
from cuju_agg_frames.p_video_frame_aggregator import (
    VideoFrameAggregator,
    VideoFrameAggregatorConfig,
)
from imgeta.plugin_base.bases.data_serializer_base import DataSerializerBase
from imgeta.plugin_base.bases.storage_base import StorageBase


@pytest.fixture
def video_frame_aggregator_config(request: pytest.FixtureRequest) -> dict[str, Any]:
    """Fixture for VideoFrameAggregatorConfig.

    Parameters
    ----------
    file_extension : str, optional
        File extension of the video, by default "mp4"
    frame_rate : float, optional
        Frame rate of the video, by default 30
    """
    params = request.param if hasattr(request, "param") else {}
    return {
        "ids": " ",
        "file_extension": params.get("file_extension", "mp4"),
        "frame_rate": params.get("frame_rate", 30),
    }


@pytest.fixture
def frame() -> np.ndarray:
    """Fixture for frame."""
    return np.zeros((5, 5, 3))


@pytest.fixture
def video_frame_aggregator(request: pytest.FixtureRequest) -> VideoFrameAggregator:
    """Fixture for VideoFrameAggregator.

    Parameters
    ----------
    second_frame : bool, optional
        Whether to create a second frame, by default False
    """
    aggr = VideoFrameAggregator(
        VideoFrameAggregatorConfig(
            **request.getfixturevalue("video_frame_aggregator_config"),
        ),
    )
    if hasattr(request, "param") and "second_frame" in request.param:
        aggr._tmp_file = mock.MagicMock(  # noqa: SLF001
            spec=tempfile.NamedTemporaryFile,
        )
        aggr._tmp_file.__str__ = lambda x: "tmp_file"  # noqa: SLF001, ARG005
        aggr._tmp_file.close = mock.MagicMock()  # noqa: SLF001
        aggr._tmp_file.name = "tmp_file"  # noqa: SLF001
        aggr._video_writer = mock.MagicMock(spec=cv.VideoWriter)  # noqa: SLF001
    return aggr


@pytest.fixture
def storage_frame_aggregator_config(request: pytest.FixtureRequest) -> dict[str, Any]:
    """Fixture for StorageFrameAggregatorConfig.

    Parameters
    ----------
    file_extension : str, optional
        File extension of the frames, by default "mp4"
    storage_connector : StorageBase, optional
        Storage connector to store frames, by default mock.MagicMock(spec=StorageBase)
    data_serializer : DataSerializerBase, optional
        Data serializer to serialize frames, by default
        mock.MagicMock(spec=DataSerializerBase)
    """
    params = request.param if hasattr(request, "param") else {}
    mock_storage = mock.MagicMock(spec=StorageBase)
    mock_storage.base_dir = tempfile.mkdtemp()
    mock_storage.get_names_in_dir.return_value = [
        "file1.png",
        "file2.png",
        "file3.png",
        "file4.png",
        "file5.png",
    ]

    mock_serializer = mock.MagicMock(spec=DataSerializerBase)
    return {
        "ids": " ",
        "file_extension": params.get("file_extension", "mp4"),
        "storage_connector": params.get("storage_connector", mock_storage),
        "data_serializer": params.get("data_serializer", mock_serializer),
    }


@pytest.fixture
def storage_frame_aggregator(request: pytest.FixtureRequest) -> StorageFrameAggregator:
    """Fixture for StorageFrameAggregator.

    Parameters
    ----------
    called : bool, optional
        Whether to call the aggregate method, by default False
    """
    aggr = StorageFrameAggregator(
        StorageFrameAggregatorConfig(
            **request.getfixturevalue("storage_frame_aggregator_config"),
        ),
    )
    if hasattr(request, "param") and "called" in request.param:
        aggr._frame_count = 5  # noqa: SLF001
    return aggr

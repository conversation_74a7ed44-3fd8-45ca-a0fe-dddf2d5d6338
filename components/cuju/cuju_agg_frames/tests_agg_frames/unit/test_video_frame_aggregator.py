"""Tests for video_frame_aggregator.py.

This module contains the following tests:

- TestVideoFrameAggregator: tests the VideoFrameAggregator class
    1. test_aggregate: tests the aggregate method
    2. test_video_file_extension: tests the video_file_extension property
    3. test_on_finish: tests the on_finish method
    4. test_reset: tests the reset method
    5. test_get_paths: tests the get_paths method
"""

from copy import deepcopy
from pathlib import Path
from typing import NoReturn
from unittest.mock import patch

import cv2
import numpy as np
import pytest
from cuju_agg_frames.p_video_frame_aggregator import (
    VideoFrameAggregator,
    VideoFrameAggregatorConfig,
)
from imgeta.pipeline.hook_markers import PLUGIN_NAMESPACE
from imgeta.pipeline.hook_specs import HookSpecs
from imgeta.plugin_base.builder.object_builder import ObjectBuilder
from pluggy import PluginManager
from pydantic import ValidationError


class TestVideoFrameAggregator:
    """Tests for VideoFrameAggregator class."""

    @pytest.mark.parametrize(
        "video_frame_aggregator",
        [
            "first_frame",
            "second_frame",
        ],
        indirect=True,
    )
    def test_aggregate(
        self,
        video_frame_aggregator: VideoFrameAggregator,
        frame: np.ndarray,
    ) -> None:
        """Test the aggregate method.

        Parameters
        ----------
        video_frame_aggregator : VideoFrameAggregator
            Video frame aggregator instance
        frame : np.ndarray
            Frame to aggregate
        """
        is_second = video_frame_aggregator._tmp_file is not None  # noqa: SLF001
        with patch("cv2.VideoWriter") as mock_video_writer:
            video_frame_aggregator.aggregate(frame)
            mock_video_writer = (  # noqa: PLW2901
                video_frame_aggregator._video_writer  # noqa: SLF001
                if is_second
                else mock_video_writer
            )
            if not is_second and mock_video_writer.assert_called_once_with(
                video_frame_aggregator._tmp_file.name,  # noqa: SLF001
                cv2.VideoWriter_fourcc(
                    *video_frame_aggregator._cfg.file_extension.get_opencv_fourcc_str(),  # noqa: SLF001
                ),
                video_frame_aggregator._cfg.frame_rate,  # noqa: SLF001
                (frame.shape[1], frame.shape[0]),
            ):
                pytest.fail("VideoWriter not called with expected arguments.")

            if is_second:
                mock_video_writer.write.assert_called_once_with(frame)
            else:
                mock_video_writer.return_value.write.assert_called_once_with(frame)

    @pytest.mark.parametrize(
        ("video_frame_aggregator_config", "expected_fourcc"),
        [
            ({"file_extension": "mp4"}, "mp4v"),
            ({"file_extension": "avi"}, "XVID"),
            pytest.param(
                {"file_extension": "mov"},
                None,
                marks=pytest.mark.xfail(raises=ValidationError),
            ),
        ],
        indirect=["video_frame_aggregator_config"],
    )
    def test_video_file_extension(
        self,
        video_frame_aggregator_config: VideoFrameAggregatorConfig,
        expected_fourcc: str,
    ) -> None:
        """Test the video_file_extension property.

        Parameters
        ----------
        video_frame_aggregator_config : VideoFrameAggregatorConfig
            Configuration for the video frame aggregator
        expected_fourcc : str
            Expected OpenCV fourcc string
        """
        video_frame_aggregator = VideoFrameAggregator(
            VideoFrameAggregatorConfig(**video_frame_aggregator_config),
        )
        if video_frame_aggregator.video_file_extension != (
            f".{video_frame_aggregator_config['file_extension']}"
        ):
            pytest.fail("Video file extension not as expected.")
        if (
            video_frame_aggregator._cfg.file_extension.get_opencv_fourcc_str()  # noqa: SLF001
            != expected_fourcc
        ):
            pytest.fail("OpenCV fourcc string not as expected.")

    @pytest.mark.parametrize(
        "video_frame_aggregator",
        [
            pytest.param(
                "first_frame",
                marks=pytest.mark.xfail(raises=AttributeError),
            ),
            "second_frame",
        ],
        indirect=True,
    )
    def test_on_finish(self, video_frame_aggregator: VideoFrameAggregator) -> None:
        """Test the on_finish method.

        Parameters
        ----------
        video_frame_aggregator : VideoFrameAggregator
            Video frame aggregator instance
        """
        video_frame_aggregator.on_finish()
        if not video_frame_aggregator._video_writer.release.called:  # noqa: SLF001
            pytest.fail("VideoWriter release method not called.")

    @pytest.mark.parametrize(
        "video_frame_aggregator",
        [
            "second_frame",
        ],
        indirect=True,
    )
    def test_reset(self, video_frame_aggregator: VideoFrameAggregator) -> None:
        """Test the reset method.

        Parameters
        ----------
        video_frame_aggregator : VideoFrameAggregator
            Video frame aggregator instance
        """
        tmp_file = video_frame_aggregator._tmp_file  # noqa: SLF001
        video_frame_aggregator.reset()
        if video_frame_aggregator._video_writer is not None:  # noqa: SLF001
            pytest.fail("VideoWriter not reset.")
        if not tmp_file.close.called:
            pytest.fail("Temporary file not closed.")
        if video_frame_aggregator._tmp_file is not None:  # noqa: SLF001
            pytest.fail("Temporary file not reset.")

    @pytest.mark.parametrize(
        "video_frame_aggregator",
        [
            pytest.param(
                "first_frame",
                marks=pytest.mark.xfail(raises=AttributeError),
            ),
            "second_frame",
        ],
        indirect=True,
    )
    def test_get_paths(self, video_frame_aggregator: VideoFrameAggregator) -> None:
        """Test the get_paths method.

        Parameters
        ----------
        video_frame_aggregator : VideoFrameAggregator
            Video frame aggregator instance
        """
        paths = video_frame_aggregator.get_paths()
        if not paths:
            pytest.fail("No paths returned.")
        if len(paths) != 1:
            pytest.fail("More than one path returned.")
        if (
            paths[0]
            != Path(
                str(video_frame_aggregator._tmp_file),  # noqa: SLF001
            )
            .absolute()
            .as_posix()
        ):
            pytest.fail("Path not as expected.")


@pytest.mark.unittest
class TestNodeRegister:
    """A test class for the 'VideoFrameAggregator' registration.

    This class tests the node registration for 'VideoFrameAggregator', ensuring that
    it is correctly registered within the plugin manager and available in the
    'ObjectBuilder' registry.
    """

    def test_node_register(self) -> NoReturn:
        """Test the registration of the 'VideoFrameAggregator' node.

        This test verifies that the 'VideoFrameAggregator' node is properly registered
        in the plugin manager and added to the 'ObjectBuilder' registry.
        """
        # --- create plugin manager from scratch ---
        registry = deepcopy(ObjectBuilder._registry)  # noqa: SLF001
        ObjectBuilder._registry = {}  # noqa: SLF001
        pm = PluginManager(PLUGIN_NAMESPACE)
        pm.add_hookspecs(HookSpecs)
        if (
            pm.is_registered(VideoFrameAggregator)
            or "VideoFrameAggregator" in ObjectBuilder._registry  # noqa: SLF001
        ):
            pytest.fail("'VideoFrameAggregator' already registered")

        # --- register node to plugin manager ---
        if not pm.is_registered(VideoFrameAggregator):
            pm.register(VideoFrameAggregator)
        pm.hook.node_register()

        # --- check whether registration has been successfully ---
        if (
            not pm.is_registered(VideoFrameAggregator)
            or "VideoFrameAggregator" not in ObjectBuilder._registry  # noqa: SLF001
        ):
            pytest.fail("'VideoFrameAggregator' could not have been registered")

        # --- check whether registered object can be actually build ---
        pipe_config = {
            "id": "test",
            "type": "VideoFrameAggregator",
            "param": {},
        }
        (builder_class, _) = ObjectBuilder.get_builder_class(
            "VideoFrameAggregator",
        )
        pipe_obj = builder_class()(pipe_config, resolve_references=False)
        if not isinstance(pipe_obj, VideoFrameAggregator):
            pytest.fail(
                "'VideoFrameAggregator' object could have been successfully built "
                "through the registered classes.",
            )
        ObjectBuilder._registry = registry  # noqa: SLF001

"""Tests for storage_frame_aggregator module.

This module contains the following tests:

- TestStorageFrameAggregator: tests the StorageFrameAggregator class
    1. test_aggregate: tests the aggregate method
    2. test_video_file_extension: tests the video_file_extension property
    3. test_on_finish: tests the on_finish method
    4. test_reset: tests the reset method
    5. test_get_paths: tests the get_paths method
"""

from copy import deepcopy
from typing import NoReturn
from unittest.mock import MagicMock

import numpy as np
import pytest
from cuju_agg_frames.p_storage_frame_aggregator import (
    StorageFrameAggregator,
    StorageFrameAggregatorConfig,
)
from imgeta.pipeline.hook_markers import PLUGIN_NAMESPACE
from imgeta.pipeline.hook_specs import HookSpecs
from imgeta.plugin_base.bases.data_serializer_base import DataSerializerBase
from imgeta.plugin_base.bases.storage_base import StorageBase
from imgeta.plugin_base.builder.object_builder import ObjectBuilder
from pluggy import PluginManager


class TestStorageFrameAggregator:
    """Tests for StorageFrameAggregator class."""

    @pytest.mark.parametrize(
        "aggregate_calls",
        [
            1,
            5,
        ],
    )
    def test_aggregate(
        self,
        storage_frame_aggregator_config: dict,
        frame: np.ndarray,
        aggregate_calls: int,
    ) -> None:
        """Test the aggregate method.

        Parameters
        ----------
        storage_frame_aggregator_config : dict
            Configuration for the storage frame aggregator
        frame : np.ndarray
            Frame to aggregate
        aggregate_calls : int
            Number of times to call the aggregate method
        """
        storage_frame_aggregator = StorageFrameAggregator(
            StorageFrameAggregatorConfig(**storage_frame_aggregator_config),
        )
        for _ in range(aggregate_calls):
            storage_frame_aggregator.aggregate(frame)
        if (
            storage_frame_aggregator._cfg.data_serializer.serialize.call_count  # noqa: SLF001
            != aggregate_calls
        ):
            pytest.fail("serialize method not called as expected.")
        call_args_list = (
            storage_frame_aggregator._cfg.data_serializer.serialize.call_args_list  # noqa: SLF001
        )
        for i, call_args in enumerate(call_args_list):
            if not np.array_equal(call_args[0][0], frame):
                pytest.fail(f"Frame {i} not as expected")
            if call_args[0][1] != storage_frame_aggregator.file_extension:
                pytest.fail(f"File name {i} not as expected")
        if (
            storage_frame_aggregator._cfg.storage_connector.write.call_count  # noqa: SLF001
            != aggregate_calls
        ):
            pytest.fail("store_file method not called as expected.")

        if storage_frame_aggregator._frame_count != aggregate_calls:  # noqa: SLF001
            pytest.fail("Frame count not as expected.")

    def test_video_file_extension(
        self,
        storage_frame_aggregator_config: dict,
    ) -> None:
        """Test the video_file_extension property.

        Parameters
        ----------
        storage_frame_aggregator_config : dict
            Configuration for the storage frame aggregator
        """
        storage_frame_aggregator = StorageFrameAggregator(
            StorageFrameAggregatorConfig(**storage_frame_aggregator_config),
        )
        if storage_frame_aggregator.video_file_extension != "/":
            pytest.fail("Video file extension not as expected.")

    @pytest.mark.parametrize(
        "storage_frame_aggregator",
        [
            "not",
            "called",
        ],
        indirect=True,
    )
    def test_reset(self, storage_frame_aggregator: StorageFrameAggregator) -> None:
        """Test the reset method.

        Parameters
        ----------
        storage_frame_aggregator : StorageFrameAggregator
            Storage frame aggregator instance
        """
        storage_frame_aggregator.reset()
        if storage_frame_aggregator._frame_count != 0:  # noqa: SLF001
            pytest.fail("Frame count not reset.")
        if (
            storage_frame_aggregator._cfg.storage_connector.delete_all.call_count != 1  # noqa: SLF001
        ):
            pytest.fail("delete_all method not called as expected.")

    def test_get_paths(self, storage_frame_aggregator_config: dict) -> None:
        """Test the get_paths method.

        Parameters
        ----------
        storage_frame_aggregator_config : dict
            Configuration for the storage frame aggregator
        """
        storage_frame_aggregator = StorageFrameAggregator(
            StorageFrameAggregatorConfig(**storage_frame_aggregator_config),
        )
        paths = storage_frame_aggregator.get_paths()
        if not paths:
            pytest.fail("No paths returned.")
        if not isinstance(paths, list):
            pytest.fail("Paths not returned as list.")


@pytest.mark.unittest
class TestNodeRegister:
    """A test class for the 'StorageFrameAggregator' registration.

    This class tests the node registration for 'StorageFrameAggregator', ensuring that
    it is correctly registered within the plugin manager and available in the
    'ObjectBuilder' registry.
    """

    def test_node_register(self) -> NoReturn:
        """Test the registration of the 'StorageFrameAggregator' node.

        This test verifies that the 'StorageFrameAggregator' node is properly registered
        in the plugin manager and added to the 'ObjectBuilder' registry.
        """
        # --- create plugin manager from scratch ---
        registry = deepcopy(ObjectBuilder._registry)  # noqa: SLF001
        ObjectBuilder._registry = {}  # noqa: SLF001
        pm = PluginManager(PLUGIN_NAMESPACE)
        pm.add_hookspecs(HookSpecs)
        if (
            pm.is_registered(StorageFrameAggregator)
            or "StorageFrameAggregator" in ObjectBuilder._registry  # noqa: SLF001
        ):
            pytest.fail("'StorageFrameAggregator' already registered")

        # --- register node to plugin manager ---
        if not pm.is_registered(StorageFrameAggregator):
            pm.register(StorageFrameAggregator)
        pm.hook.node_register()

        # --- check whether registration has been successfully ---
        if (
            not pm.is_registered(StorageFrameAggregator)
            or "StorageFrameAggregator" not in ObjectBuilder._registry  # noqa: SLF001
        ):
            pytest.fail("'StorageFrameAggregator' could not have been registered")

        # --- check whether registered object can be actually build ---
        pipe_config = {
            "id": "test",
            "type": "StorageFrameAggregator",
            "param": {
                "storage_connector": MagicMock(spec=StorageBase),
                "data_serializer": MagicMock(spec=DataSerializerBase),
                "file_extension": ".png",
            },
        }
        (builder_class, _) = ObjectBuilder.get_builder_class(
            "StorageFrameAggregator",
        )
        pipe_obj = builder_class()(pipe_config, resolve_references=False)
        if not isinstance(pipe_obj, StorageFrameAggregator):
            pytest.fail(
                "'StorageFrameAggregator' object could have been successfully built "
                "through the registered classes.",
            )
        ObjectBuilder._registry = registry  # noqa: SLF001

"""Module that provides FrameAggregatorBase class.

This class is the base class for all frame aggregators. Currently, the
following frame aggregators are available:
- VideoFrameAggregator
- StorageFrameAggregator
"""

from imgeta.pipeline.plugin_manager import get_plugin_manager

from cuju_agg_frames.p_storage_frame_aggregator import StorageFrameAggregator
from cuju_agg_frames.p_video_frame_aggregator import VideoFrameAggregator

pm = get_plugin_manager()

if not pm.is_registered(StorageFrameAggregator):
    pm.register(StorageFrameAggregator)

if not pm.is_registered(VideoFrameAggregator):
    pm.register(VideoFrameAggregator)

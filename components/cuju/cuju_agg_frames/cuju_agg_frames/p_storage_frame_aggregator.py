"""Module providing the StorageFrameAggregator class.

This module contains classes for aggregating and storing video frames using a
configurable storage connector. Frames are serialized and stored with specified file
extensions, allowing for flexible storage and retrieval of frame data.

Classes
-------
    1. StorageFrameAggregatorConfig
        Configuration class for setting up the frame aggregator, including storage
        connector, serializer, and file extension.
    2. StorageFrameAggregator
        Aggregates frames by storing them in the configured storage connector with
        options for resetting, retrieving paths, and controlling file output format.
"""

from __future__ import annotations

from pathlib import Path
from typing import TYPE_CHECKING

from imgeta.pipeline.hook_markers import hook_impl
from imgeta.plugin_base.bases.config_base import ConfigBase
from imgeta.plugin_base.bases.data_serializer_base import (
    DataSerializerBase,  # noqa: TC002
)
from imgeta.plugin_base.bases.storage_base import StorageBase  # noqa: TC002
from imgeta.plugin_base.builder.object_builder import ObjectBuilder
from pydantic import InstanceOf  # noqa: TC002

from cuju_agg_frames.frame_aggregator_base import (
    FrameAggregatorBase,
)

if TYPE_CHECKING:
    from imgeta.plugin_base.imgeta_tree.imgeta_node import ImgetaNode


class StorageFrameAggregatorConfig(ConfigBase):
    """Configuration class for 'StorageFrameAggregator' node.

    Attributes
    ----------
    storage_connector : StorageBase
        Storage connector used to store frames.
    data_serializer : DataSerializerBase
        Serializer for converting frames to the specified file format.
    file_extension : str
        File extension for stored frames (e.g., ".jpg", ".png").
    """

    storage_connector: InstanceOf[StorageBase]
    data_serializer: InstanceOf[DataSerializerBase]
    file_extension: str


class StorageFrameAggregator(FrameAggregatorBase):
    """Frame aggregator for storing frames in the given storage connector.

    The 'StorageFrameAggregator' class manages the storage of frames, enabling
    serialization, retrieval of file paths, and reset functionality. Frames are
    stored with a configurable file extension, and the class supports various storage
    connectors and serializers.
    """

    def __init__(self, config: StorageFrameAggregatorConfig) -> None:
        """Initialize an instance of 'StorageFrameAggregator' node.

        Parameters
        ----------
        config : StorageFrameAggregatorConfig
            Configuration object for the storage frame aggregator, defining storage
            and serialization options.
        """
        super().__init__(config=config)
        self._frame_count = 0
        self._cfg = config

    @property
    def file_extension(self) -> str:
        """Get the file extension of the frames.

        Returns
        -------
        str
            File extension used for stored frames, prefixed with a dot if not already
            present.
        """
        return (
            self._cfg.file_extension
            if self._cfg.file_extension.startswith(".")
            else f".{self._cfg.file_extension}"
        )

    @property
    def video_file_extension(self) -> str:
        """Get the video file extension.

        Returns
        -------
        str
            File extension for video files; currently returns "/" as a placeholder.
        """
        return "/"

    def aggregate(self, frame: ImgetaNode) -> None:
        """Aggregate and store a frame.

        Notes
        -----
        - Increments the internal frame counter with each frame stored.
        - Uses the configured serializer to convert the frame before storage.

        Parameters
        ----------
        frame : ImgetaNode
            Frame to be aggregated and stored.
        """
        self._frame_count += 1
        data = self._cfg.data_serializer.serialize(frame, self.file_extension)
        self._cfg.storage_connector.write(
            data,
            f"frame_{self._frame_count}" + self.file_extension,
        )

    def on_finish(self) -> None:
        """Finalize the frame aggregation process.

        Notes
        -----
        Currently a placeholder; additional finalization actions can be implemented
        here if needed.
        """

    def get_paths(self) -> list[str]:
        """Retrieve the paths of all aggregated frames.

        Returns
        -------
        list[str]
            List of file paths for the stored frames.
        """
        base_dir = self._cfg.storage_connector.base_dir
        return [
            (Path(base_dir) / name).as_posix()
            for name in self._cfg.storage_connector.get_names_in_dir()
        ]

    def reset(self) -> None:
        """Reset the frame aggregator by clearing all stored frames."""
        self._frame_count = 0
        self._cfg.storage_connector.delete_all()

    @staticmethod
    def is_output_single_file() -> bool:
        """Check if the output is a single file.

        Returns
        -------
        bool
            True if the output is a single file, False otherwise
        """
        return False

    @staticmethod
    @hook_impl
    def node_register() -> None:
        """Register 'StorageFrameAggregator' as a node for use in pipeline."""
        ObjectBuilder.direct_register(
            id_str="StorageFrameAggregator",
            object_type=(
                "StorageFrameAggregator",
                "cuju_agg_frames.p_storage_frame_aggregator",
            ),
            config_type=(
                "StorageFrameAggregatorConfig",
                "cuju_agg_frames.p_storage_frame_aggregator",
            ),
        )

"""Module providing the 'VideoFrameAggregator' class.

This module contains classes for aggregating video frames and saving them to a
temporary video file. Frames are accumulated and stored in the specified video
format, enabling consolidated video output from individual frames.

Classes
-------
    1. VideoExtensionType
        Enum for supported video file extension (e.g., MP4, AVI).
    2. VideoFrameAggregatorConfig
        Configuration class for setting up the frame aggregator with parameters such
        as frame rate, frame size, and file extension.
    3. VideoFrameAggregator
        Aggregates frames into a video file stored temporarily, with options for
        resetting, retrieving file paths, and specifying video format.
"""

from __future__ import annotations

import shutil
import tempfile
from enum import Enum
from pathlib import Path
from typing import TYPE_CHECKING

import cv2
from imgeta.pipeline.hook_markers import hook_impl
from imgeta.plugin_base.bases.config_base import ConfigBase
from imgeta.plugin_base.builder.object_builder import ObjectBuilder
from loguru import logger as logging

from cuju_agg_frames.frame_aggregator_base import (
    FrameAggregatorBase,
)

if TYPE_CHECKING:
    import numpy as np


class VideoExtensionType(Enum):
    """Enum for video extension types.

    This enum defines the supported video file extensions, used for setting the
    output format in the video aggregator.

    Attributes
    ----------
    MP4 : str
        MP4 video file extension.
    AVI : str
        AVI video file extension.
    """

    MP4 = "mp4"
    AVI = "avi"

    def get_opencv_fourcc_str(self) -> str:
        """Get the OpenCV fourcc string for the video extension type.

        Returns
        -------
        str
            OpenCV fourcc string associated with the video extension type.

        Raises
        ------
        ValueError
            If the video extension type is not supported.
        """
        if self == VideoExtensionType.MP4:
            return "mp4v"
        if self == VideoExtensionType.AVI:
            return "XVID"
        msg = f"Unsupported video extension type: {self}"
        raise ValueError(msg)


class VideoFrameAggregatorConfig(ConfigBase):
    """Configuration class for the 'VideoFrameAggregator' node.

    Attributes
    ----------
    frame_rate : float
        Frame rate for the video output, default is 30 frames per second.
    frame_size: tuple[int, int] | None
        Desired frame size for the video (width, height)
    file_extension: VideoExtensionType
        File extension and format for the output video.
    """

    frame_rate: float = 30
    frame_size: tuple[int, int] | None = None
    file_extension: VideoExtensionType = VideoExtensionType.MP4


class VideoFrameAggregator(FrameAggregatorBase):
    """Frame aggregator for aggregating frames into a video.

    The 'VideoFrameAggregator' class accumulates individual frames and stores them in
    a temporary video file, enabling the creation of video files from frames. The
    output video format is configurable via 'VideoFrameAggregatorConfig'.
    """

    def __init__(self, config: VideoFrameAggregatorConfig) -> None:
        """Initialize an instance of 'VideoFrameAggregator' node.

        Parameters
        ----------
        config : VideoFrameAggregatorConfig
            Configuration object specifying the video frame rate, size, and file format.
        """
        super().__init__(config=config)
        self._tmp_file = None
        self._video_writer = None
        self._cfg = config

    @property
    def video_file_extension(self) -> str:
        """Get the video file extension.

        Returns
        -------
        str
            File extension for the output video, prefixed with a dot.
        """
        return f".{self._cfg.file_extension.value}"

    def aggregate(self, frame: np.ndarray, frame_rate: float | None = None) -> None:
        """Aggregate a frame into the video.

        Writes the given frame to the temporary video file. If the video writer is
        not initialized, it creates a new writer with the specified configuration.

        Notes
        -----
        - If the frame size specified in the configuration does not match the actual
        frame size, a warning is logged, and the actual frame size is used.

        Parameters
        ----------
        frame : np.ndarray
            Frame to be added to the video file.
        frame_rate : float
            Frame rate for the video, used to set the video writer's frame rate.
        """
        if self._tmp_file is None:
            self._tmp_file = tempfile.NamedTemporaryFile(
                suffix=f".{self._cfg.file_extension.value}",
                delete=False,
            )
        if self._video_writer is None:
            height, width, _ = frame.shape
            if self._cfg.frame_size is not None and self._cfg.frame_size != (
                width,
                height,
            ):
                logging.warning(
                    f"Frame size {self._cfg.frame_size} does not match image size"
                    f" {width, height}.",
                )
                self._cfg.frame_size = (width, height)

            self._video_writer = cv2.VideoWriter(
                self._tmp_file.name,
                cv2.VideoWriter_fourcc(
                    *self._cfg.file_extension.get_opencv_fourcc_str(),
                ),
                frame_rate if frame_rate is not None else self._cfg.frame_rate,
                (
                    self._cfg.frame_size
                    if self._cfg.frame_size is not None
                    else (frame.shape[1], frame.shape[0])
                ),
            )

        self._video_writer.write(frame)

    def on_finish(self) -> None:
        """Finalize the video aggregation process.

        Releases the video writer to complete writing the video file.

        Raises
        ------
        AttributeError
            If there is no video writer initialized.
        """
        if self._video_writer is None:
            msg = "No video writer available."
            raise AttributeError(msg)
        self._video_writer.release()

    def get_paths(self) -> list[str]:
        """Get the paths of the aggregated video.

        Returns
        -------
        list[str]
            List containing the file path of the aggregated video.

        Raises
        ------
        AttributeError
            If no frames were aggregated, thus no video file was created.
        """
        if self._tmp_file is None:
            msg = "No video file available as no frames were aggregated."
            raise AttributeError(msg)
        return [Path(str(self._tmp_file.name)).absolute().as_posix()]

    def reset(self) -> None:
        """Reset the video frame aggregator.

        Deletes the temporary video file and resets the video writer, preparing the
        aggregator for a new video aggregation.
        """
        if self._video_writer is not None:
            self._video_writer.release()
            self._video_writer = None

        if self._tmp_file is not None:
            shutil.rmtree(self._tmp_file.name, ignore_errors=True)
            self._tmp_file.close()
            self._tmp_file = None

    @staticmethod
    @hook_impl
    def node_register() -> None:
        """Register 'VideoFrameAggregator' as a node for use in pipeline."""
        ObjectBuilder.direct_register(
            id_str="VideoFrameAggregator",
            object_type=(
                "VideoFrameAggregator",
                "cuju_agg_frames.p_video_frame_aggregator",
            ),
            config_type=(
                "VideoFrameAggregatorConfig",
                "cuju_agg_frames.p_video_frame_aggregator",
            ),
        )

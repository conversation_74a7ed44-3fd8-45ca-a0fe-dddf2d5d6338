"""Initializer for Video Preprocessing Pipelines.

The VidprocInitializer class is a node for validating input metadata based
on the specified mode.
"""

from __future__ import annotations

from imgeta.pipeline.hook_markers import hook_impl
from imgeta.plugin_base.builder.object_builder import ObjectBuilder
from loguru import logger as logging
from pydantic import BaseModel, Field, model_validator

from cuju_data_deployment.base_initializer import BaseInitializer, MetaList, Mode


class ConeCoords(BaseModel):
    """Pydantic model used to represent the coordinates of cones in a video.

    The class has three attributes: 'x', 'y', and 'r', which represent the
    x-coordinate, y-coordinate, and radius of a cone, respectively.

    Attributes
    ----------
    x : int
        The x-coordinate of the cone.
    y : int
        The y-coordinate of the cone.
    r : int
        The radius of the cone.
    """

    x: int
    y: int
    r: int


class VidprocRequest(BaseModel):
    """Pydantic model used for validating input metadata.

    Attributes
    ----------
    mode : Mode
        The mode of operation for the data processing pipeline. It can be
        either 'HD' or 'S3'.
    video_file_path : str
        The file path of the video.
    cones_coords : list[ConeCoords | None]
        The list of coordinates of cones in the video. Each coordinate can be None.
    video_bucket_name : str | None
        The name of the S3 bucket for storing the video data. This attribute is
        only required when the mode is 'S3'.
    """

    mode: Mode = Field(default=Mode.HD, exclude=True)

    video_file_path: str
    cones_coords: list[ConeCoords | None]
    video_bucket_name: str | None = None

    @model_validator(mode="after")
    def check_bucket_name(self) -> VidprocRequest:
        """Validate the 'video_bucket_name' based on the 'mode' attribute.

        This method checks if the 'mode' is set to 'S3' and if the
        'video_bucket_name' is None. If both conditions are met, it raises
        ValueError with a descriptive message.

        Notes
        -----
        - This method does not return any value. It raises a ValueError if the
        conditions are not met.

        Raises
        ------
        ValueError
            If the 'mode' is 'S3' and 'video_bucket_name' is None.
        """
        if self.mode == Mode.S3 and (
            not hasattr(self, "video_bucket_name") or self.video_bucket_name is None
        ):
            msg = "Parameter 'video_bucket_name' is required when mode is S3"
            raise ValueError(msg)

        return self


class VidprecInput(BaseModel):
    """TBD."""

    pipe_request: VidprocRequest
    pipe_states: dict
    pipe_codes: dict


class VidprocInitializer(BaseInitializer):
    """Input validator.

    VidprocInitializer is a node for validating input metadata in a data
    processing pipeline.
    """

    def _local_apply(self, metas: MetaList | None = None) -> None:
        if metas is None or len(metas) == 0:
            msg = "No metas provided for VidprocInitializer"
            logging.error(msg)
            raise ValueError(msg)

        for meta in metas:
            # Validate and store pipeline request data
            pipe_request = meta.get("pipe_request")
            VidprocRequest(mode=self._mode, **pipe_request)
            if self._state_storage is None:
                break

            self._set_states(
                meta=meta,
                pipe_request=pipe_request,
                set_cones=True,
            )

    @staticmethod
    @hook_impl
    def node_register() -> None:
        """Register 'VidprocInitializer' as a node for use in pipeline."""
        ObjectBuilder.direct_register(
            id_str="VidprocInitializer",
            object_type=(
                "VidprocInitializer",
                "cuju_data_deployment.p_vidproc_initializer",
            ),
            config_type=None,
        )

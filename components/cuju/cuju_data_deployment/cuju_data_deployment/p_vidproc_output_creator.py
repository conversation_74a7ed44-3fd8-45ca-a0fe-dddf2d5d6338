"""Output Validator for Vidproc pipelines.

The VidprocOutputCreator class is a node for validating output metadata based
on the specified mode.
"""

from __future__ import annotations

from typing import Any

from imgeta.pipeline.hook_markers import hook_impl
from imgeta.plugin_base.builder.object_builder import ObjectBuilder

from cuju_data_deployment.base_output_creator import OutputCreator

MetaType = dict[str, Any]
MetaList = list[MetaType]


class VidprocOutputCreator(OutputCreator):
    """Node for validating output metadata based on the specified mode.

    This node takes the output metadata and validates it based on the mode
    of operation. It ensures that the metadata is in the correct format
    and contains all the necessary information for further processing.
    """

    @staticmethod
    @hook_impl
    def node_register() -> None:
        """Register 'VidprocOutputCreator' as a node for use in pipeline."""
        ObjectBuilder.direct_register(
            id_str="VidprocOutputCreator",
            object_type=(
                "VidprocOutputCreator",
                "cuju_data_deployment.p_vidproc_output_creator",
            ),
            config_type=(
                "OutputCreatorConfig",
                "cuju_data_deployment.base_output_creator",
            ),
        )

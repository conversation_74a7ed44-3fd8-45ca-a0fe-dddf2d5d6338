"""Test Raw Storage Connector."""

from __future__ import annotations

import tempfile
from copy import deepcopy
from pathlib import Path
from typing import NoReturn

import pytest
from cuju_con_localstorage.p_memory_raw_storage_connector import (
    MemoryRawStorageConnector,
)
from imgeta.pipeline.hook_markers import PLUGIN_NAMESPACE
from imgeta.pipeline.hook_specs import HookSpecs
from imgeta.plugin_base.bases.config_base import ConfigBase
from imgeta.plugin_base.builder.object_builder import ObjectBuilder
from pluggy import PluginManager


@pytest.fixture
def connector(
    request: pytest.FixtureRequest,
) -> MemoryRawStorageConnector:
    """Reusable Connector Fixture for tests.

    This fixture creates a new instance of the MemoryRawStorageConnector

    Parameters
    ----------
    request : pytest.FixtureRequest
        The pytest request object.

    Returns
    -------
    MemoryRawStorageConnector
        The MemoryRawStorageConnector instance.
    """
    config = ConfigBase(
        ids="",
    )
    connector_obj = MemoryRawStorageConnector(config=config)
    connector_obj._memory_map = {}  # noqa: SLF001
    if hasattr(request, "param"):
        for param in request.param:
            connector_obj._memory_map[param] = b"test"  # noqa: SLF001

    return connector_obj


@pytest.mark.unittest
class TestMemoryRawStorageConnector:
    """Test cases for the RawStorageConnector Node."""

    def test_write_non_bytes_object(self, connector: MemoryRawStorageConnector) -> None:
        """Test writing a non byte object.

        This test checks if the connector raises a TypeError when trying to write
        a non-bytes object.

        Parameters
        ----------
        connector : MemoryRawStorageConnector
            The MemoryRawStorageConnector instance.
        """
        obj = "non-bytes object"
        name = "test_file.txt"

        with pytest.raises(
            TypeError,
            match=r"Could not write object. Object must be of type bytes",
        ):
            connector.write(obj=obj, name=name)

    @pytest.mark.parametrize(
        "connector",
        [
            [],
            ["test_file.txt"],
        ],
        indirect=True,
    )
    def test_write_successful(self, connector: MemoryRawStorageConnector) -> None:
        """Test a successful write.

        This test checks if the connector writes a file successfully.

        Parameters
        ----------
        connector : MemoryRawStorageConnector
            The MemoryRawStorageConnector instance.
        """
        obj = b"test bytes"
        name = "test_file.txt"

        success = connector.write(obj=obj, name=name)

        if success is not True:
            pytest.fail("Expected write to be successful.")
        if name not in connector._memory_map:  # noqa: SLF001
            pytest.fail("Expected file to be stored in memory map.")
        if connector._memory_map[name] != obj:  # noqa: SLF001
            pytest.fail("Expected file content to match the object.")

    def test_read_non_existent_file(self, connector: MemoryRawStorageConnector) -> None:
        """Test reading of non-existent file.

        This test checks if the connector raises a KeyError when trying to read
        a non-existent file.

        Parameters
        ----------
        connector : MemoryRawStorageConnector
            The MemoryRawStorageConnector instance.
        """
        with pytest.raises(KeyError):
            connector.read("non_existent_file.txt")

    def test_successful_file_upload(
        self,
        connector: MemoryRawStorageConnector,
    ) -> bool:
        """Test file upload functionality.

        This test checks if the connector uploads a file successfully.

        Parameters
        ----------
        connector : MemoryRawStorageConnector
            The MemoryRawStorageConnector instance.
        """
        name = "moved_file.txt"
        tempfile_path = Path(tempfile.mkdtemp()).joinpath("test_file.txt")
        tempfile_path.touch()
        success = connector.upload_file(str(tempfile_path), name)

        if not success:
            pytest.fail(f"Expected file upload of {name} to succeed.")
        if name not in connector._memory_map:  # noqa: SLF001
            pytest.fail(f"Expected {name} to exist in the storage.")
        if tempfile_path.exists():
            pytest.fail(f"Expected {tempfile_path} to be moved to destination.")

    def test_unsuccessful_file_upload(
        self,
        connector: MemoryRawStorageConnector,
    ) -> bool:
        """Test file upload functionality.

        This test checks if the connector raises an error when trying to upload
        a non-existent file.

        Parameters
        ----------
        connector : MemoryRawStorageConnector
            The MemoryRawStorageConnector instance.
        """
        name = "moved_file.txt"

        success = connector.upload_file("test_file", name)

        if success is not False:
            pytest.fail("File-Upload should have failed because of a Connection error.")
        if name in connector._memory_map:  # noqa: SLF001
            pytest.fail(f"Expected {name} to not exist in the storage.")

    @pytest.mark.parametrize(
        "connector",
        [
            ["test"],
        ],
        indirect=True,
    )
    def test_read(
        self,
        connector: MemoryRawStorageConnector,
    ) -> None:
        """Test successful reading of a key.

        This test checks if the connector reads a key successfully.

        Parameters
        ----------
        connector : MemoryRawStorageConnector
            The MemoryRawStorageConnector instance.
        """
        obj = connector.read("test")

        if obj != b"test":
            pytest.fail("Expected content to be 'test'.")

    @pytest.mark.parametrize(
        "connector",
        [
            ["test"],
        ],
        indirect=True,
    )
    def test_delete(
        self,
        connector: MemoryRawStorageConnector,
    ) -> None:
        """Test successful deletion of a file.

        This test checks if the connector deletes a file successfully.

        Parameters
        ----------
        connector : MemoryRawStorageConnector
            The MemoryRawStorageConnector instance.
        """
        connector.delete("test")

        if "test" in connector._memory_map:  # noqa: SLF001
            pytest.fail("Expected test to be deleted from the storage.")

    def test_delete_non_existant(self, connector: MemoryRawStorageConnector) -> None:
        """Test deletion of non-existant-file."""
        with pytest.raises(KeyError):
            connector.delete("does-not-exist.txt")

    @pytest.mark.parametrize(
        ("connector", "filter_suffix", "expected"),
        [
            (["test.txt", "test2.txt", "test3.jpg"], ".txt", ["test3.jpg"]),
            (["test.txt", "test2.txt", "test3.jpg"], ".jpg", ["test.txt", "test2.txt"]),
            (["test.txt", "test2.txt", "test3.jpg"], None, []),
        ],
        indirect=["connector"],
    )
    def test_delete_all(
        self,
        connector: MemoryRawStorageConnector,
        filter_suffix: str | None,
        expected: list[str],
    ) -> None:
        """Test deletion of multiple files with a shared suffix.

        This test checks if the connector deletes all files with a shared suffix.

        Parameters
        ----------
        connector : MemoryRawStorageConnector
            The MemoryRawStorageConnector instance.
        filter_suffix : str | None
            The suffix to filter the files.
        expected : list[str]
            The expected files after deletion.
        """
        connector.delete_all(filter_suffix)
        names = list(connector._memory_map.keys())  # noqa: SLF001
        if set(names) != set(expected):
            pytest.fail("Expected all files to be deleted.")

    @pytest.mark.parametrize(
        ("connector", "filter_suffix", "expected"),
        [
            (
                ["test.txt", "test2.txt", "test3.jpg"],
                ".txt",
                ["test.txt", "test2.txt"],
            ),
            (
                ["test.txt", "test2.txt", "test3.jpg"],
                ".jpg",
                ["test3.jpg"],
            ),
            (
                ["test.txt", "test2.txt", "test3.jpg"],
                None,
                ["test.txt", "test2.txt", "test3.jpg"],
            ),
        ],
        indirect=["connector"],
    )
    def test_get_names_in_dir(
        self,
        connector: MemoryRawStorageConnector,
        filter_suffix: str | None,
        expected: list[str],
    ) -> None:
        """Test getting names in directory.

        This test checks if the connector returns the correct names in the directory.

        Parameters
        ----------
        connector : MemoryRawStorageConnector
            The MemoryRawStorageConnector instance.
        filter_suffix : str | None
            The suffix to filter the files.
        expected : list[str]
            The expected files after deletion.
        """
        names = list(connector.get_names_in_dir(filter_suffix=filter_suffix))
        if set(names) != set(expected):
            pytest.fail("Expected all files to be returned.")

    @pytest.mark.parametrize(
        "connector",
        [
            ["test.txt"],
        ],
        indirect=True,
    )
    def test_download_file(
        self,
        connector: MemoryRawStorageConnector,
    ) -> None:
        """Test downloading a file.

        This test checks if the connector downloads a file successfully.

        Parameters
        ----------
        connector : MemoryRawStorageConnector
            The MemoryRawStorageConnector instance.
        """
        name = "test.txt"
        download_path = Path(tempfile.mkdtemp()).joinpath("downloaded_file.txt")
        success = connector.download_file(download_path, name)

        if not success:
            pytest.fail("Expected download to be successful.")
        if not download_path.exists():
            pytest.fail("Expected file to be downloaded.")
        with download_path.open("rb") as file:
            content = file.read()
            if content != b"test":
                pytest.fail("Expected content to be 'test'.")
        download_path.unlink()

    @pytest.mark.parametrize(
        "connector",
        [
            [],
        ],
        indirect=True,
    )
    def test_download_file_unsuccessful(
        self,
        connector: MemoryRawStorageConnector,
    ) -> None:
        """Test downloading a file.

        This test checks if the connector raises an error when trying to download
        a non-existent file.

        Parameters
        ----------
        connector : MemoryRawStorageConnector
            The MemoryRawStorageConnector instance.
        """
        name = "test.txt"
        download_path = Path(tempfile.mkdtemp()).joinpath("downloaded_file.txt")
        success = connector.download_file(download_path, name)
        if success:
            pytest.fail("Expected download to be unsuccessful.")
        if download_path.exists():
            pytest.fail("Expected file to not be downloaded.")


@pytest.mark.unittest
class TestNodeRegister:
    """A test class for the 'MemoryRawStorageConnector' registration.

    This class tests the node registration for 'MemoryRawStorageConnector',
    ensuring that it is correctly registered within the plugin manager
    and available in the 'ObjectBuilder' registry.
    """

    def test_node_register(self) -> NoReturn:
        """Test the registration of the 'MemoryRawStorageConnector' node.

        This test verifies that the 'MemoryRawStorageConnector' node is
        properly registered in the plugin manager and added to the
        'ObjectBuilder' registry.
        """
        # --- create plugin manager from scratch ---
        registry = deepcopy(ObjectBuilder._registry)  # noqa: SLF001
        ObjectBuilder._registry = {}  # noqa: SLF001
        pm = PluginManager(PLUGIN_NAMESPACE)
        pm.add_hookspecs(HookSpecs)
        if (
            pm.is_registered(MemoryRawStorageConnector)
            or "MemoryRawStorageConnector" in ObjectBuilder._registry  # noqa: SLF001
        ):
            pytest.fail("'MemoryRawStorageConnector' already registered")

        # --- register node to plugin manager ---
        if not pm.is_registered(MemoryRawStorageConnector):
            pm.register(MemoryRawStorageConnector)
        pm.hook.node_register()

        # --- check whether registration has been successfully ---
        if (
            not pm.is_registered(MemoryRawStorageConnector)
            or "MemoryRawStorageConnector" not in ObjectBuilder._registry  # noqa: SLF001
        ):
            pytest.fail("'MemoryRawStorageConnector' could not have been registered")

        # --- check whether registered object can be actually build ---
        pipe_config = {
            "id": "test",
            "type": "MemoryRawStorageConnector",
            "param": {},
        }
        (builder_class, _) = ObjectBuilder.get_builder_class(
            "MemoryRawStorageConnector",
        )
        pipe_obj = builder_class()(pipe_config, resolve_references=False)
        if not isinstance(pipe_obj, MemoryRawStorageConnector):
            pytest.fail(
                "'MemoryRawStorageConnector' object could have been successfully built "
                "through the registered classes.",
            )
        ObjectBuilder._registry = registry  # noqa: SLF001

"""Test Raw Storage Connector."""

from copy import deepcopy
from dataclasses import dataclass
from pathlib import Path
from typing import NoReturn
from unittest import mock

import pytest
from cuju_con_localstorage.p_disk_raw_storage_connector import (
    DiskRawStorageConnector,
    DiskRawStorageConnectorConfig,
)
from imgeta.pipeline.hook_markers import PLUGIN_NAMESPACE
from imgeta.pipeline.hook_specs import HookSpecs
from imgeta.plugin_base.builder.object_builder import ObjectBuilder
from pluggy import PluginManager


@pytest.fixture
def connector(
    tmp_path_factory: pytest.TempPathFactory,
) -> DiskRawStorageConnector:
    """Reusable Connector Fixture for tests."""
    tmp_dir = tmp_path_factory.mktemp("connector")
    config = DiskRawStorageConnectorConfig(
        ids="test_connector",
        base_dir=str(tmp_dir),
    )
    return DiskRawStorageConnector(config=config)


def _generate_sample_files(
    folder: str | Path,
    num_files: int,
    file_name: str = "test",
    file_ending: str = "txt",
    file_content: str = b"Test text.",
) -> list[Path]:
    if type(folder) is str:
        folder = Path(folder)

    file_paths = []
    for i in range(num_files):
        file_path = folder.joinpath(f"{file_name}_{i}.{file_ending}")
        file_path.write_bytes(file_content)
        file_paths.append(file_path)
    return file_paths


def _generate_sample_folders(
    root: str | Path,
    num_folders: int,
    folder_name: str = "test",
) -> list[Path]:
    if type(root) is str:
        root = Path(root)

    folder_paths = []
    for i in range(num_folders):
        folder_path = root.joinpath(f"{folder_name}_{i}")
        folder_path.mkdir()
        folder_paths.append(folder_path)
    return folder_paths


@dataclass
class _TestFileFolder:
    connector: DiskRawStorageConnector
    dir_path: Path
    file_content: any
    file_name: str
    file_path: Path


@pytest.fixture
def test_folder(
    connector: DiskRawStorageConnector,
) -> tuple[Path, Path, str]:
    """Reusable test directory containing a sample file for tests."""
    tmp_dir = Path(connector.base_dir)
    file_name = "test_file.txt"
    file_content = b"Test text."
    file_path = tmp_dir.joinpath(file_name)
    file_path.write_bytes(file_content)
    return _TestFileFolder(
        connector=connector,
        dir_path=tmp_dir,
        file_content=file_content,
        file_name=file_name,
        file_path=file_path,
    )


@pytest.mark.unittest
class TestRawStorageConnector:
    """Test cases for the RawStorageConnector Node."""

    def test_write_non_bytes_object(self, connector: DiskRawStorageConnector) -> None:
        """Test writing a non byte object."""
        obj = "non-bytes object"
        name = "test_file.txt"

        with pytest.raises(
            TypeError,
            match=r"Could not write object. Object must be of type bytes",
        ):
            connector.write(obj=obj, name=name)

    def test_write_non_existent_directory(
        self,
        tmp_path: Path,
        connector: DiskRawStorageConnector,
    ) -> None:
        """Test writing to a non-existent directory."""
        non_exist_path = tmp_path.joinpath("doesnt").joinpath("exist")
        connector.base_dir = str(non_exist_path)
        name = "test_file.txt"
        obj = b"test bytes"

        with mock.patch("loguru.logger.error") as mock_error:
            # Should log error when dir does not exist and make_dirs is False
            connector.write(obj=obj, name=name, make_dirs=False)
            mock_error.assert_called_once()

        # Should create parent directories if make_dirs is True
        connector.write(obj=obj, name=name, make_dirs=True)
        if not non_exist_path.exists() or not non_exist_path.joinpath(name).exists():
            pytest.fail(
                f"Expected {non_exist_path.absolute()} and {name} to exist"
                " because make_dirs is set to True and the write should be done.",
            )

    def test_write_successful(self, connector: DiskRawStorageConnector) -> None:
        """Test a successful write."""
        obj = b"test bytes"
        name = "test_file.txt"

        success = connector.write(obj=obj, name=name)

        if success is not True:
            pytest.fail("Expected write to be successful.")
        if not Path(connector.base_dir).joinpath(name).exists():
            pytest.fail("File was not written to disk.")

    def test_write_append_successful(
        self,
        test_folder: _TestFileFolder,
    ) -> None:
        """Test a successful write (append)."""
        test_folder.connector._cfg.append = True  # noqa: SLF001
        success_append = test_folder.connector.write(
            obj=test_folder.file_content,
            name=test_folder.file_name,
        )

        if not success_append:
            pytest.fail("Expected append operation to be successful.")

        file_content = test_folder.file_path.read_bytes()
        if file_content != test_folder.file_content * 2:
            pytest.fail("Appending to file failed.")

    def test_read_non_existent_file(self, connector: DiskRawStorageConnector) -> None:
        """Test reading of non-existent file."""
        with pytest.raises(FileNotFoundError):
            connector.read("non_existent_file.txt")

    @mock.patch(
        "cuju_con_localstorage.p_disk_raw_storage_connector.Path.open",
    )
    def test_read_permission_error(
        self,
        mock_read: mock.Mock,
        test_folder: _TestFileFolder,
    ) -> None:
        """Test reading a file with permission issues."""
        mock_read.side_effect = PermissionError
        with pytest.raises(PermissionError):
            test_folder.connector.read(test_folder.file_path)

    def test_successful_file_upload(
        self,
        test_folder: _TestFileFolder,
    ) -> bool:
        """Test file upload functionality."""
        name = "moved_file.txt"
        success = test_folder.connector.upload_file(test_folder.file_path, name)

        if not success:
            pytest.fail(f"Expected file upload of {name} to succeed.")
        if not Path(test_folder.connector.base_dir).joinpath(name).exists():
            pytest.fail(f"Expected {name} to exist in the storage.")
        if test_folder.file_path.exists():
            pytest.fail(f"Expected {test_folder.file_path} to be moved to destination.")

    @mock.patch("cuju_con_localstorage.p_disk_raw_storage_connector.shutil.move")
    def test_unsuccessful_file_upload(
        self,
        mock_move: mock.Mock,
        test_folder: _TestFileFolder,
    ) -> bool:
        """Test file upload functionality."""
        name = "moved_file.txt"
        msg = "Could not Connect"
        mock_move.side_effect = ConnectionError(msg)

        success = test_folder.connector.upload_file(test_folder.file_path, name)
        mock_move.assert_called_once()

        if success is not False:
            pytest.fail("File-Upload should have failed because of a Connection error.")
        if Path(test_folder.connector.base_dir).joinpath(name).exists():
            pytest.fail("File should not have been moved.")
        if not test_folder.file_path.exists():
            pytest.fail("Since upload was aborted, the file should still be here.")

    def test_delete(
        self,
        test_folder: _TestFileFolder,
    ) -> None:
        """Test successful deletion of a file."""
        test_folder.connector.delete(test_folder.file_path)
        if test_folder.file_path.exists():
            pytest.fail(
                f"Expected {test_folder.file_path} to be deleted "
                f"from {test_folder.connector.base_dir}",
            )

    def test_delete_parent_folder(self, connector: DiskRawStorageConnector) -> None:
        """Test successful deletion of a file and its empty parent folder."""
        file_name1, file_name2 = "test1.txt", "test2.txt"
        to_be_delted_path = Path(connector.base_dir).joinpath("to_be_deleted")
        to_be_delted_path.mkdir()
        to_be_delted_path.joinpath(file_name1).touch()
        to_be_delted_path.joinpath(file_name2).touch()

        if not to_be_delted_path.joinpath(file_name1).exists():
            pytest.fail(
                f"{file_name1} & {file_name2} should exist in {to_be_delted_path}",
            )
        connector.delete(str(to_be_delted_path.joinpath(file_name1)))
        if to_be_delted_path.joinpath(file_name1).exists():
            pytest.fail(
                f"Expected {file_name1} to be deleted from {connector.base_dir}",
            )
        if not to_be_delted_path.exists():
            pytest.fail(
                "Expected {to_be_deleted} to exist because it has another child.",
            )
        connector.delete(str(to_be_delted_path.joinpath(file_name2)))
        if to_be_delted_path.exists():
            pytest.fail(f"Expected empty parent path of {file_name2} to be deleted.")

    def test_delete_non_existant(self, connector: DiskRawStorageConnector) -> None:
        """Test deletion of non-existant-file."""
        with pytest.raises(FileNotFoundError):
            connector.delete("does-not-exist.txt")

    @mock.patch(
        "cuju_con_localstorage.p_disk_raw_storage_connector.Path.unlink",
    )
    def test_delete_unexpected_errors(
        self,
        mock_unlink: mock.Mock,
        test_folder: _TestFileFolder,
    ) -> None:
        """Test deletion with unexpected errors."""
        mock_unlink.side_effect = OSError("No permission to read file.")
        with mock.patch("loguru.logger.warning") as mock_logger:
            test_folder.connector.delete(test_folder.file_name)
            mock_logger.assert_called_once()
        msg = "Unexpected error"
        mock_unlink.side_effect = BaseException(msg)

        with pytest.raises(BaseException, match=msg):
            test_folder.connector.delete(test_folder.file_name)

    def test_delete_all(self, connector: DiskRawStorageConnector) -> None:
        """Test deletion of multiple files with a shared suffix."""
        num_files = 10
        file_paths = _generate_sample_files(connector.base_dir, num_files)
        undeleted_file = Path(connector.base_dir).joinpath("should_stay.jpg")
        undeleted_file.touch()

        if not undeleted_file.exists():
            pytest.fail(f"Expected should_stay.jpg to exist in {connector.base_dir}")
        if not all(map(Path.exists, file_paths)):
            pytest.fail(f"Expected all files to exist in {connector.base_dir}")

        connector.delete_all(name_suffix=".txt")
        if any(map(Path.exists, file_paths)):
            pytest.fail(f"Expected all files from {connector.base_dir} to be deleted")
        if not undeleted_file.exists():
            pytest.fail(
                "Expected should_stay.jpg to not be ",
                "affected by the delete operation.",
            )

    def test_get_url(self, connector: DiskRawStorageConnector) -> None:
        """Test get_url function of connector."""
        f_name = "test.txt"
        if connector.get_url(f_name) != str(Path(connector.base_dir).joinpath(f_name)):
            pytest.fail(f"Expected {f_name} to be accessible via get_url.")

    def test_get_dirs(self, connector: DiskRawStorageConnector) -> None:
        """Test get_dirs function of connector."""
        dir_paths = _generate_sample_folders(connector.base_dir, num_folders=10)
        retrieved = connector.get_dirs(recursive=False)
        rel_dir_paths = [d.relative_to(connector.base_dir) for d in dir_paths]
        if set(retrieved) != set(rel_dir_paths):
            pytest.fail("Expected all directories to be retrieved.")

    def test_get_dirs_recursive(self, connector: DiskRawStorageConnector) -> None:
        """Test recursive get_dirs function of connector and exclusion of files."""
        dir_paths = _generate_sample_folders(connector.base_dir, num_folders=10)

        sub_dirs = []
        for dir_path in dir_paths:
            sub_dir = Path(dir_path).joinpath("second_level")
            sub_dir.mkdir()
            sub_dirs.append(sub_dir)
            sub_dir.joinpath("testfile.txt").touch()

        retrieved_recursive = connector.get_dirs(recursive=True)

        def relative_paths(p: Path) -> Path:
            return Path(p).relative_to(connector.base_dir)

        if set(retrieved_recursive) != set(map(relative_paths, dir_paths + sub_dirs)):
            pytest.fail("Expected all directories and subdirectories to be retrieved.")

    def test_get_names_in_dir(self, connector: DiskRawStorageConnector) -> None:
        """Test get_names_in_dir function of connector."""
        file_paths = _generate_sample_files(connector.base_dir, num_files=10)
        file_names = [f.name for f in file_paths]

        dir_name = "intermediate"
        inter_path = Path(connector.base_dir).joinpath(dir_name)
        inter_path.mkdir()

        i_paths = _generate_sample_files(inter_path, num_files=10)
        rel_i_paths = [str(Path(i).relative_to(connector.base_dir)) for i in i_paths]

        for file_path in file_paths + i_paths:
            file_path.touch()

        files_explicit = connector.get_names_in_dir(cur_dir=connector.base_dir)
        files_default = connector.get_names_in_dir()
        if set(files_default) != set(files_explicit):
            pytest.fail(
                "Expected default directory to yield the same"
                " results as explicitly passing the base_dir",
            )

        if not all(map(Path.exists, file_paths + i_paths)):
            pytest.fail(f"Expected all files to exist in {connector.base_dir}")
        names = set(connector.get_names_in_dir(recursive=False))
        if names != {str(Path(dir_name).joinpath(f_name)) for f_name in file_names}:
            pytest.fail("Expected all created files to be returned.")
        names_recursive = set(connector.get_names_in_dir(recursive=True))
        if names_recursive != set(file_names + rel_i_paths):
            pytest.fail("Expected all created files to be returned.")


@pytest.mark.unittest
class TestNodeRegister:
    """A test class for the 'DiskRawStorageConnector' registration.

    This class tests the node registration for 'DiskRawStorageConnector', ensuring that
    it is correctly registered within the plugin manager and available in the
    'ObjectBuilder' registry.
    """

    def test_node_register(self) -> NoReturn:
        """Test the registration of the 'DiskRawStorageConnector' node.

        This test verifies that the 'DiskRawStorageConnector' node is properly
        registered in the plugin manager and added to the 'ObjectBuilder' registry.
        """
        # --- create plugin manager from scratch ---
        registry = deepcopy(ObjectBuilder._registry)  # noqa: SLF001
        ObjectBuilder._registry = {}  # noqa: SLF001
        pm = PluginManager(PLUGIN_NAMESPACE)
        pm.add_hookspecs(HookSpecs)
        if (
            pm.is_registered(DiskRawStorageConnector)
            or "DiskRawStorageConnector" in ObjectBuilder._registry  # noqa: SLF001
        ):
            pytest.fail("'DiskRawStorageConnector' already registered")

        # --- register node to plugin manager ---
        if not pm.is_registered(DiskRawStorageConnector):
            pm.register(DiskRawStorageConnector)
        pm.hook.node_register()

        # --- check whether registration has been successfully ---
        if (
            not pm.is_registered(DiskRawStorageConnector)
            or "DiskRawStorageConnector" not in ObjectBuilder._registry  # noqa: SLF001
        ):
            pytest.fail("'DiskRawStorageConnector' could not have been registered")

        # --- check whether registered object can be actually build ---
        pipe_config = {
            "id": "test",
            "type": "DiskRawStorageConnector",
            "param": {
                "base_dir": "",
            },
        }
        (builder_class, _) = ObjectBuilder.get_builder_class(
            "DiskRawStorageConnector",
        )
        pipe_obj = builder_class()(pipe_config, resolve_references=False)
        if not isinstance(pipe_obj, DiskRawStorageConnector):
            pytest.fail(
                "'DiskRawStorageConnector' object could have been successfully built "
                "through the registered classes.",
            )
        ObjectBuilder._registry = registry  # noqa: SLF001

"""Local storage connector plugin.

This plugin provides functionality to access local storages,
such as hard disks or memory.
"""

from imgeta.pipeline.plugin_manager import get_plugin_manager

from cuju_con_localstorage.p_disk_raw_storage_connector import DiskRawStorageConnector
from cuju_con_localstorage.p_memory_raw_storage_connector import (
    MemoryRawStorageConnector,
)

pm = get_plugin_manager()

if not pm.is_registered(DiskRawStorageConnector):
    pm.register(DiskRawStorageConnector)

if not pm.is_registered(MemoryRawStorageConnector):
    pm.register(MemoryRawStorageConnector)

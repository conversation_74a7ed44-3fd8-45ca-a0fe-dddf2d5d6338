"""General connector to raw data on a local disk.

This module provides:
- DiskRawStorageConnector: connector to store raw data on a local disk;
"""

from __future__ import annotations

import os
import shutil
import typing
from pathlib import Path
from typing import TYPE_CHECKING

from imgeta.pipeline.hook_markers import hook_impl
from imgeta.plugin_base.bases.config_base import ConfigBase
from imgeta.plugin_base.bases.storage_base import StorageBase
from imgeta.plugin_base.builder.object_builder import ObjectBuilder
from loguru import logger as logging

if TYPE_CHECKING:
    from collections.abc import Iterable  # pragma: no cover


class DiskRawStorageConnectorConfig(ConfigBase):
    """Configuration of disk raw storage connector.

    Attributes
    ----------
    base_dir : str
        Base directory which we will work on.
    append : bool
        Flag to append to existing files.

    """

    base_dir: str
    append: bool | None = False


class DiskRawStorageConnector(StorageBase):
    """Connector to raw data on a local disk.

    Attributes
    ----------
    config : DiskRawStorageConnectorConfig
        configuration of the disk raw storage connector.

    """

    def __init__(self, config: DiskRawStorageConnectorConfig) -> None:
        """Create a new instance of DiskRawStorageConnector node."""
        super().__init__(
            ids=config.ids,
            base_dir=Path(config.base_dir).expanduser().as_posix(),
        )

        # --- pass input arguments ---
        self._cfg = config

    def read(self, name: str) -> bytes:
        """Read rawdata with given name from local disk.

        Parameters
        ----------
        name : str
            Name of the file to read.

        """
        file_path = Path(self.base_dir) / name

        logging.debug(f"Reading file locally from {file_path}")

        try:
            with Path(file_path).open(mode="rb") as file:
                raw_file = file.read()

        except FileNotFoundError as f_err:
            logging.error(f"File does not exist.\n {f_err}")
            raise

        except BaseException as err:
            logging.error(f"Unexpected error reading file {file_path}.\n {err}")
            raise

        return raw_file

    def write(self, obj: bytes, name: str, *, make_dirs: bool | None = True) -> bool:
        """Write rawdata with given name to local disk.

        Parameters
        ----------
        obj : bytes
            object to write to file.
        name : str
            Name of the file to write.
        make_dirs: bool
            flag to make directories.

        """
        success = False

        # --- check if object is of type bytes ---
        if not isinstance(obj, bytes):
            msg = "Could not write object. Object must be of type bytes"
            logging.error(msg)
            raise TypeError(msg)

        base_path = Path(self.base_dir)

        # --- create directory if it does not exist ---
        if make_dirs and not base_path.exists():
            base_path.mkdir(parents=True)

        file_path = base_path / name

        # get dir name of file path
        dir_name = file_path.parent

        # create directory if it does not exist
        if not dir_name.exists() and make_dirs:
            dir_name.mkdir(parents=True)

        logging.debug(f"Writing file locally to {file_path}")

        # --- write bytes to file ---
        try:
            if hasattr(self._cfg, "append") and self._cfg.append:
                with file_path.open("ab") as binary_file:
                    binary_file.write(obj)
                    success = True
            else:
                with file_path.open("wb") as binary_file:
                    binary_file.write(obj)
                    success = True

        except FileNotFoundError as f_err:
            logging.error(
                f"Directory {self.base_dir} does not exist and make_dirs="
                f"{make_dirs}.\n {f_err}",
            )

        return success

    def upload_file(
        self,
        local_file_name: str,
        name: str,
        *,
        make_dirs: bool = True,
    ) -> bool:
        """Upload local stored file with given name to local storage.

        Parameters
        ----------
        local_file_name : str
            Path to local_file.
        name : str
            Name of the file to write.
        make_dirs: bool
            flag to make directories.

        Returns
        -------
        bool
            True if file was uploaded successfully, False otherwise.

        """
        base_path = Path(self.base_dir)

        if make_dirs and not base_path.exists():
            base_path.mkdir(parents=True)

        file_path = base_path / name
        # dont move file if it is already in the correct location
        if Path(local_file_name) == file_path:
            return True
        try:
            shutil.move(local_file_name, str(file_path))
        except ConnectionError as e:
            logging.error(f"Error uploading file. {e}")
            return False
        return True

    def download_file(self, local_file_name: str, name: str) -> bool:
        """Download file with given name to local disk.

        Parameters
        ----------
        local_file_name : str
            Path to local_file.
        name : str
            Name of the file to write.

        """
        file_path = Path(self.base_dir) / name
        # dont move file if it is already in the correct location
        if Path(local_file_name) == file_path:
            return True

        try:
            shutil.move(str(file_path), local_file_name)
        except ConnectionError as e:
            logging.error(f"Error downloading file. {e}")
            return False
        return True

    def delete(self, name: str) -> None:
        """Delete file with given name from local disk.

        Parameters
        ----------
        name : str
            Name of the file to delete.

        """
        file_path = Path(self.base_dir) / name

        try:
            logging.debug(f"Deleting file {file_path}")

            file_path.unlink()

            # -- delete parent paths if empty ---
            folder_path = file_path.parent
            while (
                len(list(folder_path.iterdir())) == 0 and folder_path != self.base_dir
            ):
                folder_path.rmdir()
                folder_path = folder_path.parent

        except FileNotFoundError as f_err:
            logging.error(f"File does not exist.\n {f_err}")
            raise

        except OSError as os_err:
            logging.warning(
                f"Could not delete {file_path} or "
                f"it's potentially empty parent.\n {os_err}",
            )

        except BaseException as err:
            logging.error(f"Unexpected error deleting file {file_path}.\n {err}")
            raise

    def delete_all(self, name_suffix: str | None = "") -> None:
        """Delete all files with given name suffix from local disk.

        Parameters
        ----------
        name_suffix : str
            Suffix of the files to delete.

        """
        try:
            files_to_delete = list(Path(self.base_dir).glob("*" + name_suffix))
            for file in files_to_delete:
                file.unlink()
        except OSError as e:
            logging.error(f"Error: {e.filename} - {e.strerror}.")

    def get_url(self, file_path: str) -> str:
        """Get url for given file.

        Parameters
        ----------
        file_path: str
        Name of the file to get url.
        """
        return str(Path(self.base_dir) / file_path)

    def get_names_in_dir(
        self,
        cur_dir: str | None = None,
        filter_suffix: str | None = None,
        *,
        recursive: bool | None = True,
    ) -> Iterable:
        """Get names of all files in given directory.

        Parameters
        ----------
        cur_dir : str
            current directory.
        recursive : bool
            indicates whether to search recursively.

        """
        depth = -1 if recursive else 1

        root_location = Path(self.base_dir)

        if cur_dir is not None:
            root_location = Path(self.base_dir) / cur_dir

        return self._file_names_generator(root_location, depth, filter_suffix)

    def get_dirs(self, *, recursive: bool | None = True) -> list[Path]:
        """Get names of all directories in given directory.

        Parameters
        ----------
        recursive : bool
            indicates whether to search recursively.

        """
        directories = []
        for root, dirs, _ in os.walk(self.base_dir, topdown=True, followlinks=True):
            directories.extend(
                Path(root).joinpath(d).relative_to(self.base_dir) for d in dirs
            )
            if recursive is False:
                # In place editing of dirs will prevent walk from traversing subdirs
                dirs.clear()
        return directories

    @staticmethod  # pragma: no cover
    def _file_names_generator(
        root_location: str,
        depth: int,
        filter_suffix: str,
    ) -> typing.Generator[str]:
        root_location = Path(root_location)
        pattern = DiskRawStorageConnector._generate_glob_pattern(depth, filter_suffix)
        generator = DiskRawStorageConnector._path_generator(
            root_location,
            depth,
            pattern,
        )

        for path in generator:
            if not path.name.startswith(".") and path.is_file():
                yield str(path.relative_to(root_location))

    @staticmethod  # pragma: no cover
    def _generate_glob_pattern(depth: int, filter_suffix: str | None = None) -> str:
        """Generate glob pattern based on depth and filter_suffix.

        The pattern can be used to search for files up to a specific depth.
        For example, the pattern `*` lists all files and subdirectories in the
        current directory, while the pattern `*.jpg` only lists the jpg images.
        The pattern `*/*` lists all files, subdirectories, and directories in the
        respective subdirectories etc.

        Parameters
        ----------
        depth : int
          The depth of the glob pattern. Must be greater than -1.
        filter_suffix :
          The optional file suffix

        Returns
        -------
           The glob pattern that can be used to search for files up to a specific
           depth.
        """
        if depth < -1:
            msg = "depth must be greater than -1."
            raise ValueError(msg)

        if filter_suffix is None:
            filter_suffix = ""
        elif not filter_suffix.startswith("."):
            filter_suffix = "." + filter_suffix

        if depth == -1:
            pattern = "*" + filter_suffix
        else:
            pattern = "*" + "/*" * depth + filter_suffix

        return pattern

    @staticmethod  # pragma: no cover
    def _path_generator(root: Path, depth: int, pattern: str) -> Iterable:
        """Return either a recursive or non-recursive glob generator.

        Parameters
        ----------
        root : Path
            Root path at which to start the search
        depth: int
            depth up until which to search. If it is -1, a recursive glob generator
            will be returned.
        pattern : str
            The glob pattern to search for.

        Returns
        -------
            A generator that yields sub paths.
        """
        if depth == -1:
            return root.rglob(pattern)

        return root.glob(pattern)

    @staticmethod
    @hook_impl
    def node_register() -> None:
        """Register a disk raw storage connector."""
        ObjectBuilder.direct_register(
            id_str="DiskRawStorageConnector",
            object_type=(
                "DiskRawStorageConnector",
                "cuju_con_localstorage.p_disk_raw_storage_connector",
            ),
            config_type=(
                "DiskRawStorageConnectorConfig",
                "cuju_con_localstorage.p_disk_raw_storage_connector",
            ),
        )

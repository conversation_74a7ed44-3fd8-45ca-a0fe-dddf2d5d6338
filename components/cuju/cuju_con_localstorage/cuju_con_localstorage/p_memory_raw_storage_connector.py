"""General connector to raw data in memory.

This module provides:
- MemoryRawStorageConnector: connector to store raw data in memory.
"""

from __future__ import annotations

import typing
from pathlib import Path
from typing import TYPE_CHECKING

from imgeta.pipeline.hook_markers import hook_impl
from imgeta.plugin_base.bases.storage_base import StorageBase
from imgeta.plugin_base.builder.object_builder import ObjectBuilder
from loguru import logger as logging

if TYPE_CHECKING:
    from imgeta.plugin_base.bases.config_base import ConfigBase


class MemoryRawStorageConnector(StorageBase):
    """Connector to store raw data in memory.

    Attributes
    ----------
    config : ConfigBase
        configuration of the memory raw storage connector.

    """

    def __init__(self, config: ConfigBase) -> None:
        """Create a new instance of MemoryRawStorageConnector node."""
        super().__init__(
            ids=config.ids,
            base_dir=None,
        )

        # --- pass input arguments ---
        self._cfg = config
        self._memory_map: dict[str, bytes] = {}

    def read(self, name: str) -> bytes:
        """Read rawdata with given name from memory.

        Parameters
        ----------
        name : str
            Name of the file to read.

        """
        logging.debug(f"Reading file locally from {name}")

        if name not in self._memory_map:
            msg = f"File {name} not found in memory storage."
            logging.error(msg)
            raise KeyError(msg)

        return self._memory_map[name]

    def write(self, obj: bytes, name: str) -> bool:
        """Write rawdata with given name to memory.

        Parameters
        ----------
        obj : bytes
            object to write to file.
        name : str
            Name of the file to write.

        """
        # --- check if object is of type bytes ---
        if not isinstance(obj, bytes):
            msg = "Could not write object. Object must be of type bytes"
            logging.error(msg)
            raise TypeError(msg)

        if name in self._memory_map:
            logging.debug(
                f"File {name} already exists in memory storage."
                f"Replacing it with new data.",
            )
        self._memory_map[name] = obj
        return True

    def upload_file(
        self,
        local_file_name: str,
        name: str,
    ) -> bool:
        """Upload local stored file with given name to memory.

        Parameters
        ----------
        local_file_name : str
            Path to local_file.
        name : str
            Name of the file to write.

        Returns
        -------
        bool
            True if file was uploaded successfully, False otherwise.

        """
        local_file_path = Path(local_file_name)
        if not local_file_path.exists():
            logging.error(f"File {local_file_name} does not exist.")
            return False
        try:
            logging.debug(f"Uploading file {local_file_name} to {name}")

            self._memory_map[name] = local_file_path.read_bytes()
            local_file_path.unlink()
        except OSError as os_err:
            logging.error(f"Could not upload file {local_file_name}.\n {os_err}")
            return False

        return True

    def download_file(self, local_file_name: str, name: str) -> bool:
        """Download file with given name from memory to local storage.

        Parameters
        ----------
        local_file_name : str
            Path to local_file.
        name : str
            Name of the file to write.

        """
        if name not in self._memory_map:
            logging.error(f"File {name} not found in memory storage.")
            return False
        try:
            logging.debug(f"Downloading file {name} to {local_file_name}")

            Path(local_file_name).write_bytes(self._memory_map[name])

        except OSError as os_err:
            logging.error(f"Could not download file {name}.\n {os_err}")
            return False

        return True

    def delete(self, name: str) -> None:
        """Delete file with given name from memory.

        Parameters
        ----------
        name : str
            Name of the file to delete.

        """
        if name in self._memory_map:
            del self._memory_map[name]
        else:
            msg = f"File {name} not found in memory storage."
            logging.error(msg)
            raise KeyError(msg)

    def delete_all(self, name_suffix: str | None = "") -> None:
        """Delete all files with given name suffix from memory.

        Parameters
        ----------
        name_suffix : str
            Suffix of the files to delete.

        """
        for name in list(self._memory_map.keys()):
            if name_suffix is not None and not name.endswith(name_suffix):
                continue
            del self._memory_map[name]

    def get_url(self, file_path: str) -> str:
        """Get url for given file.

        Parameters
        ----------
        file_path: str
            Name of the file to get url.

        Raises
        ------
        NotImplementedError
            Method not implemented.
        """
        msg = "Method not implemented."
        raise NotImplementedError(msg)

    def get_names_in_dir(
        self,
        cur_dir: str | None = None,  # noqa: ARG002
        filter_suffix: str | None = None,
        *,
        recursive: bool | None = True,  # noqa: ARG002
    ) -> typing.Generator[str]:
        """Get names of all files in given directory.

        Parameters
        ----------
        cur_dir : str (unused)
            current directory.
        recursive : bool (unused)
            indicates whether to search recursively.
        filter_suffix : str
            suffix to filter files.

        """
        for name in self._memory_map:
            if filter_suffix is not None and not name.endswith(filter_suffix):
                continue
            yield name

    def get_dirs(self, *, recursive: bool | None = True) -> list[Path]:
        """Get names of all directories in given directory.

        Parameters
        ----------
        recursive : bool
            indicates whether to search recursively.

        """
        msg = "Method not implemented."
        raise NotImplementedError(msg)

    @staticmethod
    @hook_impl
    def node_register() -> None:
        """Register a memory raw storage connector."""
        ObjectBuilder.direct_register(
            id_str="MemoryRawStorageConnector",
            object_type=(
                "MemoryRawStorageConnector",
                "cuju_con_localstorage.p_memory_raw_storage_connector",
            ),
            config_type=(
                "ConfigBase",
                "imgeta.plugin_base.bases.config_base",
            ),
        )

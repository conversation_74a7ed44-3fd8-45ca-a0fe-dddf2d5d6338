"""Brightness Preserving Dynamic Histogram Equalization (BPDHE).

This plugin contains:
- BrightnessPreservingHistogramEqualizer: the brightness preserving histogram equalizer.
"""

import cv2
import numpy as np
from imgeta.pipeline.hook_markers import hook_impl
from imgeta.plugin_base.bases.config_base import ConfigBase
from imgeta.plugin_base.builder.object_builder import ObjectBuilder
from imgeta.plugin_base.transformer.i2i_transformer import I2ITransformer


class BPDHETransformerConfig(ConfigBase):
    """Configuration of a BPDHE transformer node.

    Note
    ----------
        BPDHE operates based on the algorithm of weighted
        within-class variance, which doesn't require additional settings.

    Attributes
    ----------
        No specific attributes are needed for BPDHE.
    """


class BPDHETransformer(I2ITransformer):
    """An brightness preserving histogram equalizer for transforming images."""

    def __init__(self, config: BPDHETransformerConfig, **kwargs: dict) -> None:
        """Create a new instance of an adaptive histogram equalizer (BPDHE)."""
        super().__init__(
            ids=config.ids,
            on_control_flag=config.on_control_flag,
            **kwargs,
        )
        # --- parse input parameters ---
        self._cfg = config
        self._args_process = {}

    @staticmethod
    def _convert_to_hsv(img: np.ndarray) -> tuple[np.ndarray, np.ndarray, np.ndarray]:
        img = img.astype("uint8")
        hsv = cv2.cvtColor(img, cv2.COLOR_RGB2HSV)
        h, s, v = cv2.split(hsv)
        h = h / 255.0
        s = s / 255.0
        return h, s, v

    @staticmethod
    def _calculate_histogram(v: np.ndarray) -> np.ndarray:
        ma = np.max(v)
        mi = np.min(v)
        bins = (ma - mi) + 1
        hist_i = np.histogram(v, bins=bins)
        hist = hist_i[0].reshape(1, len(hist_i[0]))
        return ma, mi, bins, hist

    @staticmethod
    def _apply_gaussian_blur(hist_i: np.ndarray) -> np.ndarray:
        gausfilter = cv2.getGaussianKernel(9, 1.0762)
        return cv2.filter2D(
            hist_i.astype("float32"),
            -1,
            gausfilter,
            borderType=cv2.BORDER_REPLICATE,
        )

    @staticmethod
    def _calculate_deriv_hist(blur_hist: np.ndarray) -> np.ndarray:
        derivfilter = np.array([[-1, 1]])
        return cv2.filter2D(
            blur_hist.astype("float32"),
            -1,
            derivfilter,
            borderType=cv2.BORDER_REPLICATE,
        )

    @staticmethod
    def _calculate_smooth_sign_hist(deriv_hist: np.ndarray) -> np.ndarray:
        sign_hist = np.sign(deriv_hist)
        meanfilter = np.array([[1 / 3, 1 / 3, 1 / 3]])
        return np.sign(
            cv2.filter2D(
                sign_hist.astype("float32"),
                -1,
                meanfilter,
                borderType=cv2.BORDER_REPLICATE,
            ),
        )

    @staticmethod
    def _calculate_index(smooth_sign_hist: np.ndarray, bins: int) -> list:
        cmpfilter = np.array([[1, 1, 1, -1, -1, -1, -1, -1]])
        index = [0]
        expected_length = 8
        for n in range(bins - 7):
            c = (smooth_sign_hist[0][n : n + 8] == cmpfilter) * 1
            if np.sum(c) == expected_length:
                index.append(n + 3)
        index.append(bins)
        return index

    @staticmethod
    def _calculate_factors_and_ranges(
        hist: np.ndarray,
        index: list,
        mi: int,
    ) -> tuple[np.ndarray, np.ndarray, np.ndarray, np.ndarray, np.ndarray]:
        factor = np.zeros(shape=(len(index) - 1, 1))
        span = factor.copy()
        sub_hist_sums = factor.copy()
        rangee = factor.copy()
        start = factor.copy()
        endd = factor.copy()
        sub_hist = []
        for i in range(len(index) - 1):
            sub_hist.append(np.array(hist[0][index[i] : index[i + 1]]))
            sub_hist_sums[i] = np.sum(sub_hist[i])
            low = mi + index[i]
            high = mi + index[i + 1] - 1
            span[i] = high - low + 1
            factor[i] = span[i] * np.log10(sub_hist_sums[i])
        factor_sum = np.sum(factor)
        for i in range(len(index) - 1):
            rangee[i] = np.round((256 - mi) * factor[i] / factor_sum)
        start[0] = mi
        endd[0] = mi + rangee[0] - 1
        for i in range(1, len(index) - 1):
            start[i] = start[i - 1] + rangee[i - 1]
            endd[i] = endd[i - 1] + rangee[i]
        return sub_hist_sums, start, endd, sub_hist

    @staticmethod
    def transform(
        img: np.ndarray,
    ) -> np.ndarray:
        """Apply BPDHE transform onto the given image."""
        img = img.astype("uint8")
        h, s, v = BPDHETransformer._convert_to_hsv(img)
        max_intensity, min_intensity, bins, hist = (
            BPDHETransformer._calculate_histogram(v)
        )
        blur_hist = BPDHETransformer._apply_gaussian_blur(hist)
        deriv_hist = BPDHETransformer._calculate_deriv_hist(blur_hist)
        smooth_sign_hist = BPDHETransformer._calculate_smooth_sign_hist(deriv_hist)
        index = BPDHETransformer._calculate_index(smooth_sign_hist, bins)
        sub_hist_sums, start, endd, sub_hist = (
            BPDHETransformer._calculate_factors_and_ranges(hist, index, min_intensity)
        )

        transformed_intensity_value = []
        accumulated_transformed_intensity = np.zeros(shape=(1, min_intensity)).tolist()
        accumulated_transformed_intensity = accumulated_transformed_intensity[0]
        for i in range(len(index) - 1):
            hist_cum = np.cumsum(sub_hist[i])
            c = hist_cum / sub_hist_sums[i]
            transformed_intensity_value.append(
                np.array(np.round(start[i] + (endd[i] - start[i]) * c)),
            )
            x = transformed_intensity_value[i].tolist()
            accumulated_transformed_intensity = accumulated_transformed_intensity + x
        intensity = np.zeros(shape=v.shape)
        for n in range(min_intensity, max_intensity + 1):
            condition = v == n
            intensity[condition] = (accumulated_transformed_intensity[n]) / 255
        hsi = cv2.merge([h, s, intensity])
        hsi = (hsi * 255).astype("uint8")
        return cv2.cvtColor(hsi, cv2.COLOR_HSV2RGB)

    @staticmethod
    @hook_impl
    def node_register() -> None:
        """Register a BPDHE transformer."""
        ObjectBuilder.direct_register(
            id_str="BPDHETransformer",
            object_type=(
                "BPDHETransformer",
                "cuju_block_image_transformer.p_bpdhe_transformer",
            ),
            config_type=(
                "BPDHETransformerConfig",
                "cuju_block_image_transformer.p_bpdhe_transformer",
            ),
        )

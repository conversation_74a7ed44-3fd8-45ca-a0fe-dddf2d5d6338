"""Contrast limited adaptive histogram equalization (CLAHE).

This plugin contains:
- CLAHETransformer: the adaptive histogram equalizer.
"""

import cv2
import numpy as np
from imgeta.pipeline.hook_markers import hook_impl
from imgeta.plugin_base.bases.config_base import ConfigBase
from imgeta.plugin_base.builder.object_builder import ObjectBuilder
from imgeta.plugin_base.transformer.i2i_transformer import I2ITransformer


class CLAHETransformerConfig(ConfigBase):
    """Configuration of a CLAHE transformer node.

    Attributes
    ----------
    clip_limit : float, optional (default=2.0)
        Threshold for contrast limiting.
    tile_grid_size : int, optional (default=8)
        Size of grid for histogram equalization. Input image will be divided into
        equally sized rectangular tiles. tile_grid_size defines the number of tiles in
        row and column.
    """

    clip_limit: float | None = 2.0
    tile_grid_size: int | None = 8


class CLAHETransformer(I2ITransformer):
    """An adaptive histogram equalizer for transforming images.

    Attributes
    ----------
    config : CLAHETransformerConfig
        configuration of the CLAHE transformer.

    """

    def __init__(self, config: CLAHETransformerConfig, **kwargs: dict) -> None:
        """Create a new instance of an adaptive histogram equalizer."""
        super().__init__(
            ids=config.ids,
            on_control_flag=config.on_control_flag,
            **kwargs,
        )
        # --- parse input parameters ---
        self._cfg = config
        self._args_process = {
            "clip_limit": self._cfg.clip_limit,
            "tile_grid_size": self._cfg.tile_grid_size,
        }

    @staticmethod
    def transform(
        img: np.ndarray,
        clip_limit: int = 2,
        tile_grid_size: int = 8,
    ) -> np.ndarray:
        """Apply CLAHE transform onto the given image."""
        lab = cv2.cvtColor(img, cv2.COLOR_BGR2LAB)
        lab_planes = list(cv2.split(lab))
        clahe = cv2.createCLAHE(
            clipLimit=clip_limit,
            tileGridSize=(tile_grid_size, tile_grid_size),
        )
        lab_planes[0] = clahe.apply(lab_planes[0])
        lab = cv2.merge(lab_planes)
        return cv2.cvtColor(lab, cv2.COLOR_LAB2BGR)

    @staticmethod
    @hook_impl
    def node_register() -> None:
        """Register a CLAHE transformer."""
        ObjectBuilder.direct_register(
            id_str="CLAHETransformer",
            object_type=(
                "CLAHETransformer",
                "cuju_block_image_transformer.p_clahe_transformer",
            ),
            config_type=(
                "CLAHETransformerConfig",
                "cuju_block_image_transformer.p_clahe_transformer",
            ),
        )

"""Cropper node that crops images and creates a new imgeta node for each detection box.

This module provides:
- DetectionBoxCropperSep: detection box cropper node;
"""

from __future__ import annotations

import copy
from typing import Annotated, Any

import numpy as np
from cuju_data_detection.bounding_box.box_base import Bounding<PERSON><PERSON>, BoxType
from cuju_data_detection.detection_result import DetectionResult
from imgeta.pipeline.hook_markers import hook_impl
from imgeta.plugin_base.bases.config_base import ConfigBase
from imgeta.plugin_base.builder.object_builder import ObjectBuilder
from imgeta.plugin_base.transformer.ir2im_generator import (
    IR2IMGenerator,
    MetaMap,
)
from pydantic import Field

BatchImageType = np.ndarray
ImageMap = dict[int, BatchImageType]

RoiType = list[int] | tuple[int] | np.ndarray
ImageShapeType = list[int] | tuple[int]
IDX_OUTPUT = 0


class DetectionBoxCropperConfig(ConfigBase):
    """Configuration class for the 'DetectionBoxCropper'.

    Attributes
    ----------
    padding : int or float, optional (default=0)
        The number of pixels to add to the detection box in all directions. In case
        the padding is provided as 'int' it is assumed to be 'absolute padding'; while
        for 'float' it is assumed to be 'relative padding'.
    squared_box: bool, optional (default=false)
        Indicates whether the cropping region should be squared.
    keep_only_largest : bool, optional (default=false)
        Indicates whether the cropping should only be applied to the largest box.
    """

    padding: Annotated[int | float, Field(0, ge=0)] | None = 0
    squared_box: bool | None = False
    keep_only_largest: bool | None = False


class DetectionBoxCropper(IR2IMGenerator):
    """A transformer node for cropping images based on detection boxes.

    This class processes images by cropping them according to the detection boxes
    provided, with optional padding and squaring of the cropping region.
    """

    def __init__(
        self,
        config: DetectionBoxCropperConfig,
        **kwargs: dict,
    ) -> None:
        """Create a new instance of a 'DetectionBoxCropper'.

        Parameters
        ----------
        config : DetectionBoxCropperConfig
            Configuration object for the 'DetectionBoxCropper'.
        kwargs : dict
            Additional keyword arguments passed to the parent class.
        """
        super().__init__(
            ids=config.ids,
            on_control_flag=config.on_control_flag,
            **kwargs,
        )
        # --- parse input parameters ---
        self._cfg = config
        self._args_process = {
            "padding": self._cfg.padding,
            "squared_box": self._cfg.squared_box,
            "keep_only_largest": self._cfg.keep_only_largest,
        }

    @staticmethod
    def generate(
        img: np.ndarray,
        state_box: Any,
        padding: float = 0,
        *,
        squared_box: bool = False,
        keep_only_largest: bool = False,
    ) -> tuple[ImageMap | None, MetaMap | None]:
        """Crop the image using the given bounding boxes.

        This method crops the input image based on the provided bounding boxes,
        with optional padding and the option to square the crop region.

        Parameters
        ----------
        img : np.ndarray
            Image to which the cropping is applied
        state_box : Any
            Object containing the detection boxes. Expected to be of type
            'DetectionResult' or 'BoundingBox'.
        padding : int or float, optional (default=0)
            Number of pixels to add to the detection box in all directions. In case
            the padding is provided as 'int' it is assumed to be 'absolute padding';
            while for 'float' it is assumed to be 'relative padding'.
        squared_box : bool, optional (default=False)
            Indicates whether the cropping region should be squared.
        keep_only_largest : bool, optional (default=False)
            Indicates whether the cropping should be performed only on the largest box.

        Returns
        -------
        tuple[ImageMap, MetaMap]
            A tuple containing the cropped images and their associated metadata.
        """
        if (
            state_box is not None
            and type(state_box) is DetectionResult
            and not state_box.is_empty()
        ):
            # --- case: type of result is of expected type ---
            boxes: list[BoundingBox] = state_box.boxes
        elif state_box is not None and isinstance(state_box, BoundingBox):
            # --- case: type of result is of expected type ---
            boxes = [state_box]
        else:
            # --- case: type of result is of not expected type ---
            return {IDX_OUTPUT: [None]}, {IDX_OUTPUT: [{}]}

        # --------------------------------------------------------------------------
        #   keep only box with maximum area
        # --------------------------------------------------------------------------
        if keep_only_largest:
            # --- find largest box ---
            area_max = 0
            box_max = []
            for box in boxes:
                area = BoundingBox.area(box)
                if area > area_max:
                    area_max = area
                    box_max = box

            boxes = [box_max]

        # --------------------------------------------------------------------------
        #  crop box and append to image node
        # --------------------------------------------------------------------------

        # --- get shape of image ---
        img_shape = img.shape
        img_map = {IDX_OUTPUT: []}
        meta_map = {IDX_OUTPUT: []}
        for box in boxes:
            roi = copy.copy(box).to(BoxType.XYXY).coords

            # --- add padding ---
            if padding > 0 and isinstance(padding, int):
                roi = DetectionBoxCropper.add_padding_abs(
                    roi=roi,
                    img_shape=img_shape,
                    padding=padding,
                )
            elif padding > 0.0 and isinstance(padding, float):
                roi = DetectionBoxCropper.add_padding_rel(
                    roi=roi,
                    img_shape=img_shape,
                    padding=padding,
                )

            # --- make ROI squared ---
            if squared_box:
                roi = DetectionBoxCropper.make_roi_squared(
                    roi=roi,
                    img_shape=img_shape,
                )

            roi = roi.astype(int)

            img_map.get(IDX_OUTPUT).append(img[roi[1] : roi[3], roi[0] : roi[2]])
            meta_map.get(IDX_OUTPUT).append(
                {
                    "box_offset": [int(roi[0]), int(roi[1])],
                    "box_id": box.box_id,
                },
            )

        return img_map, meta_map

    @staticmethod
    def make_roi_squared(
        roi: RoiType,
        img_shape: ImageShapeType,
    ) -> RoiType:
        """Make the given ROI squared.

        Adjust the region of interest (ROI) to make it square-shaped, maintaining the
        center of the ROI and adjusting its width and height as necessary.

        Parameters
        ----------
        roi : RoiType
            Region of interest to be made square.
        img_shape : ImageShapeType
            Shape of the image used for ROI cropping.

        Returns
        -------
        roi : RoiType
            The adjusted square region of interest.
        """
        roi_h = roi[3] - roi[1]
        roi_w = roi[2] - roi[0]
        roi_offset = int((np.max([roi_w, roi_h]) - np.min([roi_w, roi_h])) / 2)
        if roi_h > roi_w:
            roi[0] = np.clip(roi[0] - roi_offset, 0, img_shape[1])
            roi[2] = np.clip(roi[2] + roi_offset, 0, img_shape[1])
        else:
            roi[1] = np.clip(roi[1] - roi_offset, 0, img_shape[0])
            roi[3] = np.clip(roi[3] + roi_offset, 0, img_shape[0])

        return roi

    @staticmethod
    def add_padding_abs(
        roi: RoiType,
        img_shape: ImageShapeType,
        padding: int,
    ) -> RoiType:
        """Add padding in all directions to the region of interest.

        Increase the size of the region of interest (ROI) by adding a fixed amount of
        padding in all directions, while ensuring that the ROI does not extend beyond
        the image boundaries.

        Parameters
        ----------
        roi : RoiType
            Region of interest to add padding to.
        img_shape : ImageShape
            Shape of the image used for ROI cropping.
        padding : int
            Number of pixels to add to the region of interest.

        Returns
        -------
        roi : RoiType
            The adjusted region of interest with added padding
        """
        roi[0] = (np.clip(roi[0] - padding, 0, img_shape[1])).astype(int)
        roi[1] = (np.clip(roi[1] - padding, 0, img_shape[0])).astype(int)
        roi[2] = (np.clip(roi[2] + padding, 0, img_shape[1])).astype(int)
        roi[3] = (np.clip(roi[3] + padding, 0, img_shape[0])).astype(int)

        return roi

    @staticmethod
    def add_padding_rel(
        roi: RoiType,
        img_shape: ImageShapeType,
        padding: float,
    ) -> RoiType:
        """Add padding in all directions to the region of interest.

        Increase the size of the region of interest (ROI) by adding a relative amount of
        padding based on the ROI's dimensions, while ensuring that the ROI does not
        extend beyond the image boundaries.

        Parameters
        ----------
        roi : RoiType
            Region of interest to add padding to.
        img_shape : ImageShape
            Shape of the image used for ROI cropping
        padding : float
            Relative padding to add (as a fraction of the ROI's size).

        Returns
        -------
        roi : RoiType
            The adjusted region of interest with added padding.
        """
        roi_h = roi[3] - roi[1]
        roi_w = roi[2] - roi[0]

        pad_h = roi_h * padding / 2
        pad_w = roi_w * padding / 2

        roi[0] = (np.clip(roi[0] - pad_w, 0, img_shape[1])).astype(int)
        roi[1] = (np.clip(roi[1] - pad_h, 0, img_shape[0])).astype(int)
        roi[2] = (np.clip(roi[2] + pad_w, 0, img_shape[1])).astype(int)
        roi[3] = (np.clip(roi[3] + pad_h, 0, img_shape[0])).astype(int)

        return roi

    @property
    def config(self) -> DetectionBoxCropperConfig:
        """Return the configuration of the node."""
        return self._cfg

    @staticmethod
    @hook_impl
    def node_register() -> None:
        """Register the 'DetectionBoxCropper' node."""
        ObjectBuilder.direct_register(
            id_str="DetectionBoxCropper",
            object_type=(
                "DetectionBoxCropper",
                "cuju_block_image_transformer.p_detection_box_cropper",
            ),
            config_type=(
                "DetectionBoxCropperConfig",
                "cuju_block_image_transformer.p_detection_box_cropper",
            ),
        )

"""Image drawer node that applies a list of specified overlays directly on an image.

This module provides:
- ImageDrawer: draws overlays on an image;
"""

from __future__ import annotations

from typing import TYPE_CHECKING

from cuju_block_image_overlayer.image_overlay_base import (
    ImageOverlayBase,  # noqa: TC002
)
from imgeta.pipeline.hook_markers import hook_impl
from imgeta.plugin_base.bases.config_base import ConfigBase
from imgeta.plugin_base.builder.object_builder import ObjectBuilder
from imgeta.plugin_base.transformer.n2i_transformer import N2ITransformer
from loguru import logger as logging
from pydantic import InstanceOf  # noqa: TC002

if TYPE_CHECKING:
    import numpy as np
    from imgeta.plugin_base.imgeta_tree.imgeta_node import ImgetaNodeBase


class ImageDrawerConfig(ConfigBase):
    """Configuration of image drawer node.

    Attributes
    ----------
    image_overlays : ImageOverlayBase, optional (default=None)
        a list of image overlays to be applied on the image
    debug : bool, default=False
        debug flag
    num_worker : int, optional (default=None)
        number of worker to be used for parallel processing

    """

    image_overlays: list[InstanceOf[ImageOverlayBase]] | None = None
    debug: bool = False
    num_worker: int | None = None


class ImageDrawer(N2ITransformer):
    """Image drawer node that applies a list of specified overlays directly on an image.

    Attributes
    ----------
    config : ImageDrawerConfig
        configuration of the image drawer node

    """

    def __init__(self, config: ImageDrawerConfig, **kwargs: dict) -> None:
        """Create a new instance of image drawer node."""
        super().__init__(
            ids=config.ids,
            on_control_flag=config.on_control_flag,
            **kwargs,
        )

        # --- parse input parameters ---
        self._cfg = config
        self._args_process = {
            "image_overlays": self._cfg.image_overlays,
        }

    @staticmethod
    def transform(
        frame_node: ImgetaNodeBase,
        image_overlays: list[ImageOverlayBase] | None = None,
    ) -> np.ndarray:
        """Draw overlay onto the given frame.

        Attributes
        ----------
        frame_node : ImgetaNodeBase
            node of the frame the overlay is applied to
        image_overlays : list, optional (default=None)
            a list of image overlays to be applied on the image
        """
        if frame_node.img is None:
            logging.warning("node does not contain any image frames.")
            return None

        img_result = frame_node.img.copy()

        if image_overlays is not None:
            for oly in image_overlays:
                oly.draw(img_result, frame_node)

        return img_result

    @staticmethod
    @hook_impl
    def node_register() -> None:
        """Register an image drawer node."""
        ObjectBuilder.direct_register(
            id_str="ImageDrawer",
            object_type=(
                "ImageDrawer",
                "cuju_block_image_transformer.p_image_drawer",
            ),
            config_type=(
                "ImageDrawerConfig",
                "cuju_block_image_transformer.p_image_drawer",
            ),
        )

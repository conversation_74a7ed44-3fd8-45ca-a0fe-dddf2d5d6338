"""Base class for image transform nodes.

This plugin contains:
- ImageTransform: base class for image transform nodes;
"""

from __future__ import annotations

from os import cpu_count
from typing import TYPE_CHECKING

from imgeta.plugin_base.bases.node_base import NodeBase

if TYPE_CHECKING:
    from imgeta.plugin_base.imgeta_tree.imgeta_views import ImgetaViews


class ImageTransform(NodeBase):
    """Base class for image transform objects.

    Attributes
    ----------
    ids : str
        ID string of the object type created.
    on_control_flag : str
        indicates on which state the processing node shall be executed.
    debug : bool
        debug flag
    num_worker : int
        number of worker to be used for parallel processing

    """

    def __init__(
        self,
        ids: str | None = None,
        on_control_flag: str | None = None,
        num_worker: int | None = None,
        *,
        debug: bool | None = False,
        **kwargs: dict,
    ) -> None:
        """Initialize the image transform base object."""
        super().__init__(
            ids=ids if ids is not None else f"{type(self).__name__}".lower(),
            on_control_flag=on_control_flag,
            **kwargs,
        )
        self._debug: bool = debug
        self._num_worker: int = num_worker or cpu_count() or 1

    def _local_apply(self, imgeta_views: ImgetaViews) -> list[ImgetaViews]:
        msg = "apply() must be implemented by subclass"
        raise NotImplementedError(msg)

"""Bank of transformer that are applied to the image.

This plugin contains:
- TransformerBank: bank of transformer
"""

from __future__ import annotations

from copy import copy
from typing import TYPE_CHECKING

from imgeta.pipeline.hook_markers import hook_impl
from imgeta.plugin_base.bases.config_base import ConfigBase
from imgeta.plugin_base.builder.object_builder import ObjectBuilder
from imgeta.plugin_base.transformer.i2i_transformer import (
    I2ITransformer,
)
from pydantic import InstanceOf  # noqa: TC002

if TYPE_CHECKING:
    import numpy as np


class TransformerBankConfig(ConfigBase):
    """Configuration of a transformer bank.

    Attributes
    ----------
    bank : list[I2ITransformer]
        list of transformer that are applied onto the provided image
    inplace : bool (default=True)
        indicates whether transformation is performed inplace
    """

    bank: list[InstanceOf[I2ITransformer]] | None = None
    inplace: bool = True


class TransformerBank(I2ITransformer):
    """Bank of transformer that are applied to the image.

    Attributes
    ----------
    config : TransformerBankConfig
        configuration of transformer bank

    """

    def __init__(self, config: TransformerBankConfig, **kwargs: dict) -> None:
        """Create a new instance of an transformer bank."""
        super().__init__(
            ids=config.ids,
            on_control_flag=config.on_control_flag,
            **kwargs,
        )
        # --- parse input parameters ---
        self._cfg = config
        self._args_process = {
            "bank": self._cfg.bank,
            "inplace": self._cfg.inplace,
        }

    @staticmethod
    def transform(
        img: np.ndarray,
        bank: list[I2ITransformer],
        *,
        inplace: bool,
    ) -> np.ndarray:
        """Apply bank of transformers onto the given image."""
        for trans in bank:
            if inplace:
                img = trans.transform(img=img)
            else:
                img = trans.transform(img=copy(img))

        return img

    @staticmethod
    @hook_impl
    def node_register() -> None:
        """Register a transformer bank."""
        ObjectBuilder.direct_register(
            id_str="TransformerBank",
            object_type=(
                "TransformerBank",
                "cuju_block_image_transformer.p_transformer_bank",
            ),
            config_type=(
                "TransformerBankConfig",
                "cuju_block_image_transformer.p_transformer_bank",
            ),
        )

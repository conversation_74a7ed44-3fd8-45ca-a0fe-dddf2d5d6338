"""Image transformer plugin.

This plugin provides functionality to transform images in different ways.

Note: contains base classes, i.e., all plugins related to image transformation need
to derive from this plugin.
"""

from imgeta.pipeline.plugin_manager import get_plugin_manager

from cuju_block_image_transformer.p_bpdhe_transformer import BPDHETransformer
from cuju_block_image_transformer.p_clahe_transformer import (
    CLAHETransformer,
)
from cuju_block_image_transformer.p_detection_box_cropper import (
    DetectionBoxCropper,
)
from cuju_block_image_transformer.p_image_drawer import ImageDrawer
from cuju_block_image_transformer.p_image_drawer_inplace import ImageDrawerInplace
from cuju_block_image_transformer.p_transformer_bank import TransformerBank

pm = get_plugin_manager()

if not pm.is_registered(CLAHETransformer):
    pm.register(CLAHETransformer)

if not pm.is_registered(BPDHETransformer):
    pm.register(BPDHETransformer)

if not pm.is_registered(DetectionBoxCropper):
    pm.register(DetectionBoxCropper)

if not pm.is_registered(ImageDrawer):
    pm.register(ImageDrawer)

if not pm.is_registered(ImageDrawerInplace):
    pm.register(ImageDrawerInplace)

if not pm.is_registered(TransformerBank):
    pm.register(TransformerBank)

"""Image drawer node that applies a list of specified overlays directly on an image.

Note: No new imgeta nodes will be created, rather the image will be modified in-place.

This module provides:
- ImageDrawer: draws overlays on an image;
"""

from __future__ import annotations

from typing import TYPE_CHECKING

from cuju_block_image_overlayer.image_overlay_base import (
    ImageOverlayBase,  # noqa: TC002
)
from imgeta.pipeline.hook_markers import hook_impl
from imgeta.plugin_base.bases.config_base import ConfigBase
from imgeta.plugin_base.builder.object_builder import ObjectBuilder
from imgeta.plugin_base.transformer.n2i_transformer import N2ITransformer
from pydantic import InstanceOf  # noqa: TC002

if TYPE_CHECKING:
    import numpy as np
    from imgeta.plugin_base.imgeta_tree.imgeta_node import ImgetaNodeBase


class ImageDrawerInplaceConfig(ConfigBase):
    """Configuration of image drawer node.

    Attributes
    ----------
    image_overlays : ImageOverlayBase, optional (default=None)
        a list of image overlays to be applied on the image
    debug : bool, default=False
        debug flag
    num_worker : int, optional (default=None)
        number of worker to be used for parallel processing

    """

    image_overlays: list[InstanceOf[ImageOverlayBase]] | None = None


class ImageDrawerInplace(N2ITransformer):
    """Image drawer node that applies a list of specified overlays directly on an image.

    Attributes
    ----------
    config : ImageDrawerInplaceConfig
        configuration of the image drawer node

    Notes
    -----
    No new imgeta nodes will be created, rather the image will be modified in-place.

    """

    def __init__(self, config: ImageDrawerInplaceConfig, **kwargs: dict) -> None:
        """Create a new instance of image drawer node."""
        super().__init__(
            ids=config.ids,
            on_control_flag=config.on_control_flag,
            **kwargs,
        )

        # --- parse input parameters ---
        self._cfg = config
        self._args_process = {
            "image_overlays": self._cfg.image_overlays,
        }

    @staticmethod
    def transform(
        frame_node: ImgetaNodeBase,
        image_overlays: list[ImageOverlayBase] | None = None,
    ) -> np.ndarray:
        """Draw overlay onto the given frame.

        Attributes
        ----------
        frame_node : ImgetaNodeBase
            node of the frame the overlay is applied to
        image_overlays : list, optional (default=None)
            a list of image overlays to be applied on the image
        """
        if image_overlays is not None:
            for oly in image_overlays:
                oly.draw(frame_node.img, frame_node)

        return frame_node.img

    @staticmethod
    @hook_impl
    def node_register() -> None:
        """Register a inplace image drawer node."""
        ObjectBuilder.direct_register(
            id_str="ImageDrawerInplace",
            object_type=(
                "ImageDrawerInplace",
                "cuju_block_image_transformer.p_image_drawer_inplace",
            ),
            config_type=(
                "ImageDrawerInplaceConfig",
                "cuju_block_image_transformer.p_image_drawer_inplace",
            ),
        )

"""Configurations for the box cropper integration tests."""

from __future__ import annotations

import json
from pathlib import Path
from typing import Any

import numpy as np
import pytest
from cuju_data_detection.bounding_box.box_base import BoundingBox
from cuju_data_detection.detection_result import DetectionResult
from imgeta.pipeline.pipe_runner import <PERSON><PERSON><PERSON><PERSON><PERSON>
from imgeta.pipeline.plugin_manager import (
    delete_plugin_manager,
)

RESOURCES_PATH = Path(__file__).parent.joinpath("resources")


@pytest.fixture
def test_runner() -> PipeRunner:
    """Fixture to create and return a 'PipeRunner' instance for testing.

    This fixture sets up the 'PipeRunner' with specific parameters needed for the
    tests, including logging level, plugin configuration, and settings name.

    Returns
    -------
        An instance of 'PipeRunner' initialized with the test configuration.
    """
    params = {
        "log_level": "DEBUG",
        "plugin_toml": str(Path(RESOURCES_PATH).joinpath("test_pyproject.toml")),
        "settings_name": "settings_single",
    }

    yield PipeRunner(
        app_name="imgeta",
        params=params,
    )

    delete_plugin_manager()


@pytest.fixture
def pipe_config_no_padding() -> tuple[int, dict]:
    """Fixture to load pipeline configuration with no padding.

    This fixture reads a JSON file containing the pipeline configuration for cropping
    detection boxes without padding.
    """
    abs_padding = 0
    path_to_config = str(
        Path(RESOURCES_PATH).joinpath("pipe_detection_box_cropper_no_padding.json"),
    )
    with Path(path_to_config.strip()).open() as json_file:
        return abs_padding, json.load(json_file)


@pytest.fixture
def pipe_config_abs_padding() -> tuple[int, dict]:
    """Fixture to load pipeline configuration with absolute padding.

    This fixture reads a JSON file containing the pipeline configuration for cropping
    detection boxes with absolut padding, i.e., in each dimension 2 pixels.
    """
    abs_padding = 2
    path_to_config = str(
        Path(RESOURCES_PATH).joinpath("pipe_detection_box_cropper_abs_padding.json"),
    )
    with Path(path_to_config.strip()).open() as json_file:
        return abs_padding, json.load(json_file)


@pytest.fixture
def pipe_config_rel_padding() -> tuple[int, dict]:
    """Fixture to load pipeline configuration with relative padding.

    This fixture reads a JSON file containing the pipeline configuration for cropping
    detection boxes with relative padding, i.e., in each dimension 1 pixels. Note:
    this depends strongly on the box dimension.
    """
    abs_padding = 1
    path_to_config = str(
        Path(RESOURCES_PATH).joinpath("pipe_detection_box_cropper_rel_padding.json"),
    )
    with Path(path_to_config.strip()).open() as json_file:
        return abs_padding, json.load(json_file)


@pytest.fixture
def in_imgs_1() -> list[np.ndarray]:
    """Fixture to generate a list of random images for testing.

    This fixture generates a list containing a single random 100x100 image,
    with pixel values ranging from 0 to 255.
    """
    return [(np.random.rand(100, 100) * 255).astype(int)]


@pytest.fixture
def in_results_1() -> list[dict[str, DetectionResult]]:
    """Fixture to generate detection results for testing.

    This fixture creates a 'DetectionResult' object with predefined bounding boxes,
    labels, and scores, which is then used to crop the provided images.
    """
    res_1 = DetectionResult(
        boxes=[
            BoundingBox(coords=np.array([10, 10, 20, 20])),
            BoundingBox(coords=np.array([50, 50, 60, 60])),
        ],
        labels=["0", "0"],
        scores=[np.array(0.9), np.array(0.7)],
    )
    return [{"odet": res_1}]


@pytest.fixture
def in_results_invalid(
    request: pytest.FixtureRequest,
) -> list[dict[str, DetectionResult | None]]:
    """Fixture to generate detection results for testing.

    This fixture creates a 'DetectionResult' object with predefined bounding boxes,
    labels, and scores, which is then used to crop the provided images.
    """
    res_1 = None
    if request.param == "invalid_empty_detection":
        res_1 = DetectionResult()
    elif request.param == "invalid_not_is_instance":
        res_1 = "invalid"
    elif request.param != "invalid_none":
        msg = "Invalid parameter"
        raise ValueError(msg)
    return [{"odet": res_1}]


@pytest.fixture
def test_cases_box_cropper(
    request: pytest.FixtureRequest,
) -> dict[str, Any]:
    """Fixture to generate test cases for the box cropper integration tests.

    This fixture generates test cases for the box cropper integration tests, including
    the pipeline configuration, input images, and expected output.
    """
    test_case = request.param if hasattr(request, "param") else "pipe_config_no_padding"
    abs_padding, in_pipe_config = request.getfixturevalue(test_case)
    return {
        "in_pipe_config": in_pipe_config,
        "abs_padding": abs_padding,
    }

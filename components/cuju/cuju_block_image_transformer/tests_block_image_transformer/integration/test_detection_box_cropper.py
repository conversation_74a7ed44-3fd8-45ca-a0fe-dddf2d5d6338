"""Integration tests for 'DetectionBoxCropper'.

This module contains integration tests for the 'DetectionBoxCropper' node. It sets up
the necessary fixtures and runs tests to ensure the correct behavior of the bounding
cropping based on various pipeline configurations.

Classes
-------
    1. TestDetectionBoxCropper
        Contains integration tests for the 'DetectionBoxCropper'.
"""

from __future__ import annotations

from typing import TYPE_CHECKING, Any, NoReturn

import numpy as np
import pytest
from imgeta.plugin_basic.imgeta_dto import ImgetaDTO

if TYPE_CHECKING:
    from imgeta.pipeline.pipe_runner import PipeRunner


@pytest.mark.integration
class TestDetectionBoxCropper:
    """Integration tests for the 'DetectionBoxCropper' node.

    This class contains tests that verify the behavior of the 'DetectionBoxCropper'
    under different pipeline configurations, ensuring that the bounding boxes are
    cropped correctly according to the specified padding options.
    """

    @pytest.mark.parametrize(
        ("test_cases_box_cropper", "in_imgs_str", "in_results_str"),
        [
            ("pipe_config_no_padding", "in_imgs_1", "in_results_1"),
            ("pipe_config_abs_padding", "in_imgs_1", "in_results_1"),
            ("pipe_config_rel_padding", "in_imgs_1", "in_results_1"),
        ],
        indirect=["test_cases_box_cropper"],
    )
    def test_perform_pipeline(
        self,
        test_cases_box_cropper: dict[str, Any],
        in_imgs_str: str,
        in_results_str: str,
        test_runner: PipeRunner,
        request: pytest.FixtureRequest,
    ) -> NoReturn:
        """Test the detection box cropping with different padding configurations.

        This method tests the detection box cropping by running the 'PipeRunner' with
        various pipeline configurations (no padding, absolute padding, and relative
        padding). It then verifies that the pipeline processes the images and
        detection results correctly.

        Note: only the resulting cropped images are compared to the expected images.

        Parameters
        ----------
        request : Any
            The PyTest request object used to retrieve fixture values dynamically.
        test_runner : PipeRunner
            The 'PipeRunner' instance used to execute the pipeline.
        in_pipe_config_str : str
            The name of the fixture that provides the pipeline configuration.
        in_imgs_str : str
            The name of the fixture that provides the input images.
        in_results_str : str
            The name of the fixture that provides the detection results.
        """
        in_pipe_config = test_cases_box_cropper["in_pipe_config"]
        abs_padding = test_cases_box_cropper["abs_padding"]

        in_imgs = request.getfixturevalue(in_imgs_str)
        in_results = request.getfixturevalue(in_results_str)
        in_imgeta = ImgetaDTO(images=in_imgs, results=in_results)

        # --- perform pipeline processing ---
        out_pipe = test_runner.predict(
            inputs=in_imgeta,
            params={"pipe_cfg": in_pipe_config},
        )
        out_imgs = out_pipe.images

        # --- compare result with expected result ---
        for idx_box, in_box in enumerate(in_results[0].get("odet").boxes):
            cropped_imgs = in_imgs[0][
                in_box.coords[1] - abs_padding : in_box.coords[3] + abs_padding,
                in_box.coords[0] - abs_padding : in_box.coords[2] + abs_padding,
            ]
            if not np.array_equal(out_imgs[idx_box], cropped_imgs):
                pytest.fail("Cropped image does not match expected result.")

    @pytest.mark.parametrize(
        ("test_cases_box_cropper", "in_imgs_str", "in_results_invalid"),
        [
            ("pipe_config_no_padding", "in_imgs_1", "invalid_none"),
            ("pipe_config_no_padding", "in_imgs_1", "invalid_empty_detection"),
            ("pipe_config_no_padding", "in_imgs_1", "invalid_not_is_instance"),
        ],
        indirect=["test_cases_box_cropper", "in_results_invalid"],
    )
    def test_perform_pipeline_invalid(
        self,
        test_cases_box_cropper: dict[str, Any],
        in_imgs_str: str,
        in_results_invalid: str,
        test_runner: PipeRunner,
        request: pytest.FixtureRequest,
    ) -> NoReturn:
        """Test the detection box cropping with different padding configurations.

        This method tests the detction box cropping by running the 'PipeRunner' with
        various pipeline configurations (no padding, absolute padding, and relative
        padding). It then verifies that the pipeline processes the images and
        detection results correctly.

        Note: only the resulting cropped images are compared to the expected images.

        Parameters
        ----------
        request : Any
            The PyTest request object used to retrieve fixture values dynamically.
        test_runner : PipeRunner
            The 'PipeRunner' instance used to execute the pipeline.
        in_pipe_config_str : str
            The name of the fixture that provides the pipeline configuration.
        in_imgs_str : str
            The name of the fixture that provides the input images.
        in_results_str : str
            The name of the fixture that provides the detection results.
        """
        in_pipe_config = test_cases_box_cropper["in_pipe_config"]
        in_imgs = request.getfixturevalue(in_imgs_str)
        in_imgeta = ImgetaDTO(images=in_imgs, results=in_results_invalid)

        # --- perform pipeline processing ---
        out_pipe = test_runner.predict(
            inputs=in_imgeta,
            params={"pipe_cfg": in_pipe_config},
        )
        out_imgs = out_pipe.images
        if len(out_imgs) > 1:
            pytest.fail("Invalid input should return only none.")
        if out_imgs != [None]:
            pytest.fail("Invalid input should return only none.")
        out_meta = out_pipe.meta
        if out_meta != [{}]:
            pytest.fail("Invalid input should return empty metadata.")

"""Unit tests for BPDHETransformer class in p_bpdhe_transformer.py.

This module provides:
- TestGroup: a class with test cases covering
1. "wrong_output_shape"
2. "wrong_output_dtype"
3. "contrast_not_enhanced"
4. "node_not_registred"
"""

from copy import deepcopy

import numpy as np
import pytest
from cuju_block_image_transformer.p_bpdhe_transformer import (
    BPDHETransformer,
    BPDHETransformerConfig,
)
from imgeta.pipeline.hook_markers import PLUGIN_NAMESPACE
from imgeta.pipeline.hook_specs import HookSpecs
from imgeta.plugin_base.builder.object_builder import ObjectBuilder
from pluggy import PluginManager


@pytest.mark.unittest
class TestBPDHEImageTransform:
    """A class of BPDHETransform to test transform method."""

    @pytest.mark.parametrize(
        ("img_name", "expected_output_shape", "expected_output_dtype"),
        [
            ("img", (100, 100, 3), np.uint8),
            ("low_contrast_img", (100, 100, 3), np.uint8),
        ],
    )
    def test_transform(
        self,
        img_name: str,
        expected_output_shape: tuple[int, int, int],
        expected_output_dtype: any,
        request: pytest.FixtureRequest,
    ) -> None:
        """Test the transformation procedure."""
        bpdhe_transformer = BPDHETransformer(
            BPDHETransformerConfig(ids=""),
        )

        img = request.getfixturevalue(img_name)
        transformed_img = bpdhe_transformer.transform(
            img,
        )

        if transformed_img.shape != expected_output_shape:
            pytest.fail("Output shape should match input shape")
        if transformed_img.dtype != expected_output_dtype:
            pytest.fail("Output should be of type uint8")

    def test_bpdhe_contrast_enhancement(
        self,
        low_contrast_img: str,
    ) -> None:
        """Test if the contrast is enhanced."""
        bpdhe_transformer = BPDHETransformer(
            BPDHETransformerConfig(ids=""),
        )

        transformed_img = bpdhe_transformer.transform(
            low_contrast_img,
        )

        original_std = np.std(low_contrast_img)
        transformed_std = np.std(transformed_img)

        if transformed_std < original_std:
            pytest.fail("BPDHE does not enhance contrast")


@pytest.mark.unittest
class TestNodeRegister:
    """A test class for the 'BPDHETransformer' registration.

    This class tests the node registration for 'BPDHETransformer', ensuring that
    it is correctly registered within the plugin manager and available in the
    'ObjectBuilder' registry.
    """

    def test_node_register(self) -> None:
        """Test the registration of the 'BPDHETransformer' node.

        This test verifies that the 'BPDHETransformer' node is properly registered
        in the plugin manager and added to the 'ObjectBuilder' registry.
        """
        # --- create plugin manager from scratch ---
        registry = deepcopy(ObjectBuilder._registry)  # noqa: SLF001
        ObjectBuilder._registry = {}  # noqa: SLF001
        pm = PluginManager(PLUGIN_NAMESPACE)
        pm.add_hookspecs(HookSpecs)
        if (
            pm.is_registered(BPDHETransformer)
            or "BPDHETransformer" in ObjectBuilder._registry  # noqa: SLF001
        ):
            pytest.fail("'BPDHETransformer' already registered")

        # --- register node to plugin manager ---
        if not pm.is_registered(BPDHETransformer):
            pm.register(BPDHETransformer)
        pm.hook.node_register()

        # --- check whether registration has been successfully ---
        if (
            not pm.is_registered(BPDHETransformer)
            or "BPDHETransformer" not in ObjectBuilder._registry  # noqa: SLF001
        ):
            pytest.fail("'BPDHETransformer' could not have been registered")

        # --- check whether registered object can be actually build ---
        pipe_config = {
            "id": "bpdhe_transformer",
            "type": "BPDHETransformer",
            "param": {},
        }
        (builder_class, _) = ObjectBuilder.get_builder_class(
            "BPDHETransformer",
        )
        pipe_obj = builder_class()(pipe_config, resolve_references=False)
        if not isinstance(pipe_obj, BPDHETransformer):
            pytest.fail(
                "'BPDHETransformer' object could have been successfully built "
                "through the registered classes.",
            )
        ObjectBuilder._registry = registry  # noqa: SLF001

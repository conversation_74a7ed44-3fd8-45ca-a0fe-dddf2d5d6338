"""Unit tests for image drawer."""

from copy import deepcopy
from typing import NoReturn

import numpy as np
import pytest
from cuju_block_image_overlayer.image_overlay_base import ImageOverlayBase
from cuju_block_image_transformer.p_image_drawer import ImageDrawer, ImageDrawerConfig
from imgeta.pipeline.hook_markers import PLUGIN_NAMESPACE
from imgeta.pipeline.hook_specs import HookSpecs
from imgeta.plugin_base.builder.object_builder import ObjectBuilder
from imgeta.plugin_base.imgeta_tree.imgeta_node import ImgetaNode
from pluggy import PluginManager


@pytest.mark.unittest
class TestConstructor:
    """Unit tests for 'ImageDrawer' class construction.

    This class contains tests that verify the proper instantiation of the 'ImageDrawer'.
    """

    def test_default_constructor(self) -> NoReturn:
        """Test the default constructor of 'ImageDrawer'."""
        image_drawer = ImageDrawer(ImageDrawerConfig(ids="ImageDrawer"))

        if not isinstance(image_drawer, ImageDrawer):
            pytest.fail("type of created object is not met")

        if image_drawer._cfg.ids != "ImageDrawer":  # noqa: SLF001
            pytest.fail("ID string is not met")

    def test_test_constructor_with_config(
        self,
        request: pytest.FixtureRequest,
    ) -> NoReturn:
        """Test the constructor of 'ImageDrawer' with custom configuration."""
        # --- set parameters ---
        cfg_ids = "ImageDrawer"
        cfg_image_overlays = request.getfixturevalue("dummy_overlays")

        image_drawer = ImageDrawer(
            config=ImageDrawerConfig(ids=cfg_ids, image_overlays=cfg_image_overlays),
        )

        if not isinstance(image_drawer, ImageDrawer):
            pytest.fail("type of created object is not met")

        if image_drawer._cfg.ids != cfg_ids:  # noqa: SLF001
            pytest.fail("ID string is not met")

        for oly in image_drawer._cfg.image_overlays:  # noqa: SLF001
            if not isinstance(oly, ImageOverlayBase):
                pytest.fail("Image_overlays is not met")


@pytest.mark.unittest
class TestTransform:
    """A test class for drawing overlays on an image."""

    @pytest.mark.parametrize(
        ("dummy_overlays", "expected_output"),
        [
            ([], 0),
            ([1], 1),
            ([1, 10, 100], 111),
        ],
        indirect=["dummy_overlays"],
    )
    def test_drawing_overlays(
        self,
        mock_node: ImgetaNode,
        dummy_overlays: list,
        expected_output: int,
    ) -> NoReturn:
        """Test drawing of images with different overlays applied."""
        frame_img = mock_node.img.copy()
        frame_img[:] = expected_output
        expected_img = frame_img

        # transform image with overlays
        oly_res = ImageDrawer.transform(
            frame_node=mock_node,
            image_overlays=dummy_overlays,
        )

        # check if original node has changed
        if not np.array_equal(mock_node.img, np.zeros((720, 1280, 3))):
            pytest.fail(f"Image in the node has changed: {mock_node.img}")

        # compare before and after transforming image
        if not np.array_equal(expected_img, oly_res):
            pytest.fail(f"Expected {expected_img}, but got {oly_res}")


@pytest.mark.unittest
class TestNodeRegister:
    """A test class for the 'ImageDrawer' registration.

    This class tests the node registration for 'ImageDrawer', ensuring that
    it is correctly registered within the plugin manager and available in the
    'ObjectBuilder' registry.
    """

    def test_node_register(self) -> None:
        """Test the registration of the 'ImageDrawer' node.

        This test verifies that the 'ImageDrawer' node is properly registered
        in the plugin manager and added to the 'ObjectBuilder' registry.
        """
        # --- create plugin manager from scratch ---
        registry = deepcopy(ObjectBuilder._registry)  # noqa: SLF001
        ObjectBuilder._registry = {}  # noqa: SLF001
        pm = PluginManager(PLUGIN_NAMESPACE)
        pm.add_hookspecs(HookSpecs)
        if (
            pm.is_registered(ImageDrawer) or "ImageDrawer" in ObjectBuilder._registry  # noqa: SLF001
        ):
            pytest.fail("'ImageDrawer' already registered")

        # --- register node to plugin manager ---
        if not pm.is_registered(ImageDrawer):
            pm.register(ImageDrawer)
        pm.hook.node_register()

        # --- check whether registration has been successfully ---
        if (
            not pm.is_registered(ImageDrawer)
            or "ImageDrawer" not in ObjectBuilder._registry  # noqa: SLF001
        ):
            pytest.fail("'ImageDrawer' could not have been registered")

        # --- check whether registered object can be actually build ---
        pipe_config = {
            "id": "image_drawer",
            "type": "ImageDrawer",
            "param": {},
        }
        (builder_class, _) = ObjectBuilder.get_builder_class(
            "ImageDrawer",
        )
        pipe_obj = builder_class()(pipe_config, resolve_references=False)
        if not isinstance(pipe_obj, ImageDrawer):
            pytest.fail(
                "'ImageDrawer' object could have been successfully built "
                "through the registered classes.",
            )
        ObjectBuilder._registry = registry  # noqa: SLF001

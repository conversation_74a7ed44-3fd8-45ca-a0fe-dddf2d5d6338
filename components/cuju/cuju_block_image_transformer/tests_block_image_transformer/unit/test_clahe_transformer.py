"""Unit tests for CLAHE transformer."""

from copy import deepcopy

import pytest
from cuju_block_image_transformer import CLAHETransformer
from imgeta.pipeline.hook_markers import PLUGIN_NAMESPACE
from imgeta.pipeline.hook_specs import HookSpecs
from imgeta.plugin_base.builder.object_builder import ObjectBuilder
from pluggy import PluginManager


@pytest.mark.unittest
class TestNodeRegister:
    """A test class for the 'CLAHETransformer' registration.

    This class tests the node registration for 'CLAHETransformer', ensuring that
    it is correctly registered within the plugin manager and available in the
    'ObjectBuilder' registry.
    """

    def test_node_register(self) -> None:
        """Test the registration of the 'CLAHETransformer' node.

        This test verifies that the 'CLAHETransformer' node is properly registered
        in the plugin manager and added to the 'ObjectBuilder' registry.
        """
        # --- create plugin manager from scratch ---
        registry = deepcopy(ObjectBuilder._registry)  # noqa: SLF001
        ObjectBuilder._registry = {}  # noqa: SLF001
        pm = PluginManager(PLUGIN_NAMESPACE)
        pm.add_hookspecs(HookSpecs)
        if (
            pm.is_registered(CLAHETransformer)
            or "CLAHETransformer" in ObjectBuilder._registry  # noqa: SLF001
        ):
            pytest.fail("'CLAHETransformer' already registered")

        # --- register node to plugin manager ---
        if not pm.is_registered(CLAHETransformer):
            pm.register(CLAHETransformer)
        pm.hook.node_register()

        # --- check whether registration has been successfully ---
        if (
            not pm.is_registered(CLAHETransformer)
            or "CLAHETransformer" not in ObjectBuilder._registry  # noqa: SLF001
        ):
            pytest.fail("'CLAHETransformer' could not have been registered")

        # --- check whether registered object can be actually build ---
        pipe_config = {
            "id": "clahe_transformer",
            "type": "CLAHETransformer",
            "param": {},
        }
        (builder_class, _) = ObjectBuilder.get_builder_class(
            "CLAHETransformer",
        )
        pipe_obj = builder_class()(pipe_config, resolve_references=False)
        if not isinstance(pipe_obj, CLAHETransformer):
            pytest.fail(
                "'CLAHETransformer' object could have been successfully built "
                "through the registered classes.",
            )
        ObjectBuilder._registry = registry  # noqa: SLF001

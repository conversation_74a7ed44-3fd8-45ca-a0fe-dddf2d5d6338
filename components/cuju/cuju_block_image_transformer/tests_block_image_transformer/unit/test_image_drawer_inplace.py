"""Unit tests for image drawer inplace."""

from copy import deepcopy
from typing import NoReturn

import numpy as np
import pytest
from cuju_block_image_transformer import <PERSON><PERSON><PERSON>erInplace
from imgeta.pipeline.hook_markers import PLUGIN_NAMESPACE
from imgeta.pipeline.hook_specs import HookSpecs
from imgeta.plugin_base.builder.object_builder import ObjectBuilder
from imgeta.plugin_base.imgeta_tree.imgeta_node import ImgetaNode
from pluggy import PluginManager


@pytest.mark.unittest
class TestTransform:
    """A test class for drawing overlays on an image."""

    @pytest.mark.parametrize(
        ("dummy_overlays", "expected_output"),
        [
            ([], 0),
            ([1], 1),
            ([1, 10, 100], 111),
        ],
        indirect=["dummy_overlays"],
    )
    def test_drawing_overlays(
        self,
        mock_node: ImgetaNode,
        dummy_overlays: list,
        expected_output: int,
    ) -> NoReturn:
        """Test drawing of images with different overlays applied."""
        frame_img = mock_node.img.copy()
        frame_img[:] = expected_output
        expected_img = frame_img

        # transform image with overlays
        oly_res = ImageDrawerInplace.transform(
            frame_node=mock_node,
            image_overlays=dummy_overlays,
        )

        # check if image is modified in place
        if not np.array_equal(mock_node.img, oly_res):
            pytest.fail(f"Image was not modified in place: {mock_node.img}")

        # compare before and after transforming image
        if not np.array_equal(expected_img, oly_res):
            pytest.fail(f"Expected {expected_img}, but got {oly_res}")


@pytest.mark.unittest
class TestNodeRegister:
    """A test class for the 'ImageDrawerInplace' registration.

    This class tests the node registration for 'ImageDrawerInplace', ensuring that
    it is correctly registered within the plugin manager and available in the
    'ObjectBuilder' registry.
    """

    def test_node_register(self) -> None:
        """Test the registration of the 'ImageDrawerInplace' node.

        This test verifies that the 'ImageDrawerInplace' node is properly registered
        in the plugin manager and added to the 'ObjectBuilder' registry.
        """
        # --- create plugin manager from scratch ---
        registry = deepcopy(ObjectBuilder._registry)  # noqa: SLF001
        ObjectBuilder._registry = {}  # noqa: SLF001
        pm = PluginManager(PLUGIN_NAMESPACE)
        pm.add_hookspecs(HookSpecs)
        if (
            pm.is_registered(ImageDrawerInplace)
            or "ImageDrawerInplace" in ObjectBuilder._registry  # noqa: SLF001
        ):
            pytest.fail("'ImageDrawerInplace' already registered")

        # --- register node to plugin manager ---
        if not pm.is_registered(ImageDrawerInplace):
            pm.register(ImageDrawerInplace)
        pm.hook.node_register()

        # --- check whether registration has been successfully ---
        if (
            not pm.is_registered(ImageDrawerInplace)
            or "ImageDrawerInplace" not in ObjectBuilder._registry  # noqa: SLF001
        ):
            pytest.fail("'ImageDrawerInplace' could not have been registered")

        # --- check whether registered object can be actually build ---
        pipe_config = {
            "id": "image_drawer_inplace",
            "type": "ImageDrawerInplace",
            "param": {},
        }
        (builder_class, _) = ObjectBuilder.get_builder_class(
            "ImageDrawerInplace",
        )
        pipe_obj = builder_class()(pipe_config, resolve_references=False)
        if not isinstance(pipe_obj, ImageDrawerInplace):
            pytest.fail(
                "'ImageDrawerInplace' object could have been successfully built "
                "through the registered classes.",
            )
        ObjectBuilder._registry = registry  # noqa: SLF001

"""Unit tests for transformer bank."""

from copy import deepcopy

import pytest
from cuju_block_image_transformer import TransformerBank
from imgeta.pipeline.hook_markers import PLUGIN_NAMESPACE
from imgeta.pipeline.hook_specs import HookSpecs
from imgeta.plugin_base.builder.object_builder import ObjectBuilder
from pluggy import Plugin<PERSON>anager


@pytest.mark.unittest
class TestNodeRegister:
    """A test class for the 'TransformerBank' registration.

    This class tests the node registration for 'TransformerBank', ensuring that
    it is correctly registered within the plugin manager and available in the
    'ObjectBuilder' registry.
    """

    def test_node_register(self) -> None:
        """Test the registration of the 'TransformerBank' node.

        This test verifies that the 'TransformerBank' node is properly registered
        in the plugin manager and added to the 'ObjectBuilder' registry.
        """
        # --- create plugin manager from scratch ---
        registry = deepcopy(ObjectBuilder._registry)  # noqa: SLF001
        ObjectBuilder._registry = {}  # noqa: SLF001
        pm = PluginManager(PLUGIN_NAMESPACE)
        pm.add_hookspecs(HookSpecs)
        if (
            pm.is_registered(TransformerBank)
            or "TransformerBank" in ObjectBuilder._registry  # noqa: SLF001
        ):
            pytest.fail("'TransformerBank' already registered")

        # --- register node to plugin manager ---
        if not pm.is_registered(TransformerBank):
            pm.register(TransformerBank)
        pm.hook.node_register()

        # --- check whether registration has been successfully ---
        if (
            not pm.is_registered(TransformerBank)
            or "TransformerBank" not in ObjectBuilder._registry  # noqa: SLF001
        ):
            pytest.fail("'TransformerBank' could not have been registered")

        # --- check whether registered object can be actually build ---
        pipe_config = {
            "id": "transformer_bank",
            "type": "TransformerBank",
            "param": {},
        }
        (builder_class, _) = ObjectBuilder.get_builder_class(
            "TransformerBank",
        )
        pipe_obj = builder_class()(pipe_config, resolve_references=False)
        if not isinstance(pipe_obj, TransformerBank):
            pytest.fail(
                "'TransformerBank' object could have been successfully built "
                "through the registered classes.",
            )
        ObjectBuilder._registry = registry  # noqa: SLF001

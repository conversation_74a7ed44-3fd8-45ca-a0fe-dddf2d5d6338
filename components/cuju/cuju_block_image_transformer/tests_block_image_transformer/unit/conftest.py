"""Fixtures that are used throughout the tests."""

from unittest.mock import <PERSON><PERSON>ock

import numpy as np
import pytest
from cuju_block_image_overlayer.image_overlay_base import ImageOverlayBase
from cuju_block_image_transformer.p_detection_box_cropper import ImageShapeType, RoiType
from cuju_data_detection.bounding_box.box_base import BoundingBox
from cuju_data_detection.detection_result import Detec<PERSON>R<PERSON>ult
from imgeta.plugin_base.imgeta_tree.imgeta_node import ImgetaNode


class DummyOverlay(ImageOverlayBase):
    """Apply a dummy overlay to an image frame."""

    def __init__(
        self,
        frame_node: ImgetaNode,
        pixel_value: list[int],
        **kwargs: dict,  # noqa: ARG002
    ) -> None:
        super().__init__(ids="dummy_oly")
        self.frame_node = frame_node
        self.image = frame_node.img.copy()
        self.value = pixel_value

    def draw(
        self,
        img: np.ndarray,
        frame_node: ImgetaNode,  # noqa: ARG002
    ) -> None:
        """Apply the pixel value into the image frame array."""
        img[:] += self.value


def box1() -> BoundingBox:
    """Return a valid BoundingBox object."""
    return BoundingBox(
        coords=np.array([638.89895105, 226.44770622, 804.66440344, 447.4683094]),
    )


def box2() -> BoundingBox:
    """Return a valid BoundingBox object."""
    return BoundingBox(
        coords=np.array([1168.07688522, 273.49613953, 1216.64870071, 338.25856018]),
    )


@pytest.fixture
def image_with_zero_padding_and_squared_large_bbox() -> tuple | None:
    """Create valid image, detection results and valid padding values."""
    img = np.zeros((720, 1280, 3), dtype=np.uint8)
    box_1 = box1()
    box_2 = box2()
    state_odet = DetectionResult(
        boxes=[box_1, box_2],
        labels=["person", "person"],
        scores=[
            np.array(0.99559313, dtype=np.float32),
            np.array(0.9815291, dtype=np.float32),
        ],
    )

    img_map = {0: [np.zeros((221, 220, 3), dtype=np.uint8)]}
    meta_map = {0: [{"box_id": box_1.box_id, "box_offset": [611, 226]}]}

    return (img, state_odet, img_map, meta_map)


@pytest.fixture
def image_with_non_zero_padding_and_squared_large_bbox() -> tuple | None:
    """Create valid image, detection results and with non-zero padding values."""
    img = np.zeros((720, 1280, 3), dtype=np.uint8)

    box_1 = box1()
    box_2 = box2()
    state_odet = DetectionResult(
        boxes=[box_1, box_2],
        labels=["person", "person"],
        scores=[
            np.array(0.99559313, dtype=np.float32),
            np.array(0.9815291, dtype=np.float32),
        ],
    )

    img_map = {0: [np.zeros((720, 1280, 3), dtype=np.uint8)]}
    meta_map = {0: [{"box_id": box_1.box_id, "box_offset": [0, 0]}]}

    return (img, state_odet, img_map, meta_map)


@pytest.fixture
def image_with_negative_padding_and_squared_large_bbox() -> tuple | None:
    """Create valid image, detection results and with non-zero padding values."""
    img = np.zeros((720, 1280, 3), dtype=np.uint8)

    box_1 = box1()
    box_2 = box2()
    state_odet = DetectionResult(
        boxes=[box_1, box_2],
        labels=["person", "person"],
        scores=[
            np.array(0.99559313, dtype=np.float32),
            np.array(0.9815291, dtype=np.float32),
        ],
    )

    img_map = {0: [np.zeros((221, 220, 3), dtype=np.uint8)]}
    meta_map = {0: [{"box_id": box_1.box_id, "box_offset": [611, 226]}]}

    return (img, state_odet, img_map, meta_map)


@pytest.fixture
def image_no_padding_and_non_squared_bbox() -> tuple | None:
    """Create valid Detection results but no padding values and non-squared image."""
    img = np.zeros((720, 1280, 3), dtype=np.uint8)

    box_1 = box1()
    box_2 = box2()
    state_odet = DetectionResult(
        boxes=[box_1, box_2],
        labels=["person", "person"],
        scores=[
            np.array(0.99559313, dtype=np.float32),
            np.array(0.9815291, dtype=np.float32),
        ],
    )

    img_map = {
        0: [
            np.zeros((221, 166, 3), dtype=np.uint8),
            np.zeros((65, 48, 3), dtype=np.uint8),
        ],
    }
    meta_map = {
        0: [
            {"box_id": box_1.box_id, "box_offset": [638, 226]},
            {"box_id": box_2.box_id, "box_offset": [1168, 273]},
        ],
    }

    return (img, state_odet, img_map, meta_map)


@pytest.fixture
def image_no_detections() -> tuple | None:
    """Case with no detections in the image."""
    img = np.zeros((720, 1280, 3), dtype=np.uint8)

    state_odet = DetectionResult()

    img_map = None
    meta_map = None

    return (img, state_odet, img_map, meta_map)


@pytest.fixture
def img() -> np.ndarray:
    """High contrast image."""
    return np.random.randint(0, 256, size=(100, 100, 3), dtype=np.uint8)


@pytest.fixture
def low_contrast_img() -> np.ndarray:
    """Low contrast image."""
    return np.full((100, 100, 3), 50, dtype=np.uint8)


@pytest.fixture
def roi() -> RoiType:
    """Fixture to provide a sample region of interest (ROI) for the test."""
    return [50, 50, 150, 150]


@pytest.fixture
def roi_w50h100() -> RoiType:
    """Fixture to provide a sample region of interest (ROI) for the test."""
    return [50, 50, 100, 150]


@pytest.fixture
def roi_w100h50() -> RoiType:
    """Fixture to provide a sample region of interest (ROI) for the test."""
    return [50, 50, 150, 100]


@pytest.fixture
def roi_w100h100_1() -> RoiType:
    """Fixture to provide a sample region of interest (ROI) for the test."""
    return [25, 50, 125, 150]


@pytest.fixture
def roi_w100h100_2() -> RoiType:
    """Fixture to provide a sample region of interest (ROI) for the test."""
    return [50, 25, 150, 125]


@pytest.fixture
def img_shape() -> ImageShapeType:
    """Fixture to provide a sample image shape for the tests."""
    return [200, 200]


@pytest.fixture
def padding_zero() -> float:
    """Fixture to provide a sample padding for the tests."""
    return 0.0


@pytest.fixture
def padding_rel_01() -> float:
    """Fixture to provide a sample padding for the tests."""
    return 0.1


@pytest.fixture
def padding_rel_02() -> float:
    """Fixture to provide a sample padding for the tests."""
    return 0.2


@pytest.fixture
def padding_rel_03() -> float:
    """Fixture to provide a sample padding for the tests."""
    return 0.3


@pytest.fixture
def padding_rel_04() -> float:
    """Fixture to provide a sample padding for the tests."""
    return 0.4


@pytest.fixture
def padding_rel_05() -> float:
    """Fixture to provide a sample padding for the tests."""
    return 0.5


@pytest.fixture
def padding_rel_06() -> float:
    """Fixture to provide a sample padding for the tests."""
    return 0.6


@pytest.fixture
def padding_rel_07() -> float:
    """Fixture to provide a sample padding for the tests."""
    return 0.7


@pytest.fixture
def padding_rel_08() -> float:
    """Fixture to provide a sample padding for the tests."""
    return 0.8


@pytest.fixture
def padding_rel_09() -> float:
    """Fixture to provide a sample padding for the tests."""
    return 0.9


@pytest.fixture
def padding_rel_10() -> float:
    """Fixture to provide a sample padding for the tests."""
    return 1.0


@pytest.fixture
def padding_rel_20() -> float:
    """Fixture to provide a sample padding for the tests."""
    return 2.0


@pytest.fixture
def padding_rel_m01() -> float:
    """Fixture to provide a sample padding for the tests."""
    return -0.1


@pytest.fixture
def padding_abs_10() -> float:
    """Fixture to provide a sample padding for the tests."""
    return 10


@pytest.fixture
def padding_abs_20() -> float:
    """Fixture to provide a sample padding for the tests."""
    return 20


@pytest.fixture
def padding_abs_30() -> float:
    """Fixture to provide a sample padding for the tests."""
    return 30


@pytest.fixture
def padding_abs_40() -> float:
    """Fixture to provide a sample padding for the tests."""
    return 40


@pytest.fixture
def padding_abs_50() -> float:
    """Fixture to provide a sample padding for the tests."""
    return 50


@pytest.fixture
def padding_abs_60() -> float:
    """Fixture to provide a sample padding for the tests."""
    return 60


@pytest.fixture
def padding_abs_m10() -> float:
    """Fixture to provide a sample padding for the tests."""
    return -10


@pytest.fixture
def roi_with_padding_rel_01() -> RoiType:
    """Fixture to provide a sample region of interest (ROI) for the test."""
    return [45, 45, 155, 155]


@pytest.fixture
def roi_with_padding_rel_02() -> RoiType:
    """Fixture to provide a sample region of interest (ROI) for the test."""
    return [40, 40, 160, 160]


@pytest.fixture
def roi_with_padding_rel_03() -> RoiType:
    """Fixture to provide a sample region of interest (ROI) for the test."""
    return [35, 35, 165, 165]


@pytest.fixture
def roi_with_padding_rel_04() -> RoiType:
    """Fixture to provide a sample region of interest (ROI) for the test."""
    return [30, 30, 170, 170]


@pytest.fixture
def roi_with_padding_rel_05() -> RoiType:
    """Fixture to provide a sample region of interest (ROI) for the test."""
    return [25, 25, 175, 175]


@pytest.fixture
def roi_with_padding_rel_06() -> RoiType:
    """Fixture to provide a sample region of interest (ROI) for the test."""
    return [20, 20, 180, 180]


@pytest.fixture
def roi_with_padding_rel_07() -> RoiType:
    """Fixture to provide a sample region of interest (ROI) for the test."""
    return [15, 15, 185, 185]


@pytest.fixture
def roi_with_padding_rel_08() -> RoiType:
    """Fixture to provide a sample region of interest (ROI) for the test."""
    return [10, 10, 190, 190]


@pytest.fixture
def roi_with_padding_rel_09() -> RoiType:
    """Fixture to provide a sample region of interest (ROI) for the test."""
    return [5, 5, 195, 195]


@pytest.fixture
def roi_with_padding_rel_10() -> RoiType:
    """Fixture to provide a sample region of interest (ROI) for the test."""
    return [0, 0, 200, 200]


@pytest.fixture
def roi_with_padding_rel_20() -> RoiType:
    """Fixture to provide a sample region of interest (ROI) for the test."""
    return [0, 0, 200, 200]


@pytest.fixture
def roi_with_padding_rel_m01() -> RoiType:
    """Fixture to provide a sample region of interest (ROI) for the test."""
    return [55, 55, 145, 145]


@pytest.fixture
def roi_with_padding_abs_10() -> RoiType:
    """Fixture to provide a sample region of interest (ROI) for the test."""
    return [40, 40, 160, 160]


@pytest.fixture
def roi_with_padding_abs_20() -> RoiType:
    """Fixture to provide a sample region of interest (ROI) for the test."""
    return [30, 30, 170, 170]


@pytest.fixture
def roi_with_padding_abs_30() -> RoiType:
    """Fixture to provide a sample region of interest (ROI) for the test."""
    return [20, 20, 180, 180]


@pytest.fixture
def roi_with_padding_abs_40() -> RoiType:
    """Fixture to provide a sample region of interest (ROI) for the test."""
    return [10, 10, 190, 190]


@pytest.fixture
def roi_with_padding_abs_50() -> RoiType:
    """Fixture to provide a sample region of interest (ROI) for the test."""
    return [0, 0, 200, 200]


@pytest.fixture
def roi_with_padding_abs_60() -> RoiType:
    """Fixture to provide a sample region of interest (ROI) for the test."""
    return [0, 0, 200, 200]


@pytest.fixture
def roi_with_padding_abs_m10() -> RoiType:
    """Fixture to provide a sample region of interest (ROI) for the test."""
    return [60, 60, 140, 140]


@pytest.fixture
def image_np_array() -> np.ndarray:
    """Fixture for image numpy array."""
    return np.zeros((720, 1280, 3), dtype=np.uint8)


@pytest.fixture
def mock_node(request: pytest.FixtureRequest) -> ImgetaNode:
    """Fixture for mock node."""
    node = MagicMock(spec=ImgetaNode)
    node.img = request.getfixturevalue("image_np_array")
    return node


@pytest.fixture
def dummy_overlays(request: pytest.FixtureRequest) -> list[DummyOverlay]:
    """Fixture for multiple dummy overlays."""
    frame_node = request.getfixturevalue("mock_node")

    overlays = []
    param = request.param if hasattr(request, "param") else []
    for pixel_value in param:
        overlays.append(DummyOverlay(frame_node=frame_node, pixel_value=pixel_value))

    return overlays

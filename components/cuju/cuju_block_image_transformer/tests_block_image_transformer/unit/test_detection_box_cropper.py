"""Unit tests for the 'DetectionBoxCropper' node.

This module provides a series of test cases to validate the functionality of the
'DetectionBoxCropper' node, including tests for its construction, method behavior,
and integration with the object registry.

Classes
-------
    1. TestConstructor
        Contains unit tests for the construction of 'DetectionBoxCropper'.
    2. TestGenerate
        Contains unit tests for the 'generate' method of 'DetectionBoxCropper'.
    3. TestMakeRoiSquared
        Contains unit tests for the 'make_roi_squared' method of 'DetectionBoxCropper'.
    4. TestAddPaddingRel
        Contains unit tests for the 'add_padding_rel' method of 'DetectionBoxCropper'.
    5. TestAddPaddingAbs
        Contains unit tests for the 'add_padding_abs' method of 'DetectionBoxCropper'.
    6. TestNodeRegister
        Contains unit tests for the correct registration of 'DetectionBoxCropper'
        nodes in the object registry.
"""

from copy import copy, deepcopy
from typing import Any, NoReturn

import numpy as np
import pytest
from cuju_block_image_transformer.p_detection_box_cropper import (
    DetectionBox<PERSON>ropper,
    DetectionBoxCropperConfig,
)
from imgeta.pipeline.hook_markers import PLUGIN_NAMESPACE
from imgeta.pipeline.hook_specs import HookSpecs
from imgeta.plugin_base.builder.object_builder import ObjectBuilder
from pluggy import PluginManager


@pytest.mark.unittest
class TestConstructor:
    """Unit tests for 'DetectionBoxCropper' class construction.

    This class contains tests that verify the proper instantiation of the
    'DetectionBoxCropper' node with various configurations.
    """

    def test_default_constructor(self) -> NoReturn:
        """Test the default constructor of 'DetectionBoxCropper'."""
        detection_box_cropper = DetectionBoxCropper(
            DetectionBoxCropperConfig(ids="BoxCropper"),
        )

        if not isinstance(detection_box_cropper, DetectionBoxCropper):
            pytest.fail("type of created object is not met")

        if detection_box_cropper.config.ids != "BoxCropper":
            pytest.fail("ID string is not met")

    def test_constructor_with_config(self) -> NoReturn:
        """Test the constructor of 'DetectionBoxCropper' with custom configuration."""
        # --- set parameters ---
        cfg_ids = "BoxCropper"
        cfg_padding = 0
        cfg_squared_box = False
        cfg_keep_only_largest = False

        detection_box_cropper = DetectionBoxCropper(
            DetectionBoxCropperConfig(
                ids=cfg_ids,
                padding=cfg_padding,
                squared_box=cfg_squared_box,
                keep_only_largest=cfg_keep_only_largest,
            ),
        )

        if not isinstance(detection_box_cropper, DetectionBoxCropper):
            pytest.fail("type of created object is not met")

        if detection_box_cropper.config.ids != cfg_ids:
            pytest.fail("ID string is not met")

        if detection_box_cropper.config.padding != cfg_padding:
            pytest.fail("padding is not met")

        if detection_box_cropper.config.squared_box != cfg_squared_box:
            pytest.fail("squared_box is not met")

        if detection_box_cropper.config.keep_only_largest != cfg_keep_only_largest:
            pytest.fail("keep_only_largest is not met")


@pytest.mark.unittest
class TestGenerate:
    """A class of unit tests for the 'generate' method of 'DetectionBoxCropper'.

    This class contains parameterized tests that verify the behavior of the
    'generate' method under various conditions, including different padding values,
    squared box settings, and whether only the largest bounding box should be kept.
    """

    @pytest.mark.parametrize(
        ("input_conditions", "padding", "squared_box", "keep_only_largest"),
        [
            ("image_with_zero_padding_and_squared_large_bbox", 0, True, True),
            ("image_with_non_zero_padding_and_squared_large_bbox", 1000, True, True),
            ("image_with_negative_padding_and_squared_large_bbox", -10, True, True),
            ("image_no_padding_and_non_squared_bbox", 0, False, False),
        ],
    )
    def test_box_cropper_success(  # noqa: PLR0912
        self,
        input_conditions: str,
        request: Any,
        padding: int,
        *,
        squared_box: bool,
        keep_only_largest: bool,
    ) -> NoReturn:
        """Tests the 'generate' method for successful cropping.

        This test verifies that the 'generate' method correctly processes images and
        bounding boxes under various configurations, including different padding,
        squared box, and largest box settings.

        Parameters
        ----------
        input_conditions : str
            The name of the fixture that provides input conditions.
        request : Any
            The PyTest request object used to retrieve fixture values dynamically.
        padding : int
            The padding value to be applied around the bounding box.
        squared_box : bool
            A flag indicating whether the bounding box should be squared.
        keep_only_largest : bool
            A flag indicating whether only the largest bounding box should be kept.
        """
        # --- get input and output conditions ---
        in_image, in_state_odet, exp_image_res, exp_meta_res = request.getfixturevalue(
            input_conditions,
        )

        # --- perform box cropping ---
        act_img_res, act_meta_res = DetectionBoxCropper.generate(
            img=in_image,
            state_box=in_state_odet,
            padding=padding,
            squared_box=squared_box,
            keep_only_largest=keep_only_largest,
        )

        # --- test output with expected results ---
        if not isinstance(act_img_res, dict):
            pytest.fail("img_res_act is not an instance of dict")

        if not isinstance(act_meta_res, dict):
            pytest.fail("meta_res_act is not an instance of dict")

        for key in exp_image_res:
            if key not in act_img_res:
                """Check if keys in expected results are same as actual results."""
                pytest.fail(f"Key '{key}' is not in img_res_act")
            if not isinstance(act_img_res[key], list):
                """Check if the values of each key in actual result is a list."""
                pytest.fail(f"Value for key '{key}' in img_res_act is not a list")
            if not isinstance(exp_image_res[key], list):
                """Check if the values of each key in expected result is a list."""
                pytest.fail(f"Value for key '{key}' in img_res_exp is not a list")

            for i in range(len(exp_image_res[key])):
                """
                Check if the detection result arrays of
                actual results are same as expected results.
                """
                if not np.array_equal(act_img_res[key][i], exp_image_res[key][i]):
                    pytest.fail(
                        f"Value for key '{key}' at index {i} "
                        f"in img_res_act is not equal to img_res_exp",
                    )

        for key in exp_meta_res:
            if key not in act_meta_res:
                """Check if meta map of expected and actual results match."""
                pytest.fail("Key '{key}' is not in meta_res_act")
            if not isinstance(act_meta_res[key], list):
                """Check if the values of the actual results dict is a list."""
                pytest.fail(f"Value for key '{key}' in meta_res_act is not a list")
            if not isinstance(exp_meta_res[key], list):
                """Check if the values of the expected results dict is a list."""
                pytest.fail(f"Value for key '{key}' in meta_res_exp is not a list")

            for i in range(len(exp_meta_res[key])):
                if (
                    act_meta_res[key][i]["box_offset"]
                    != exp_meta_res[key][i]["box_offset"]
                ):
                    pytest.fail(
                        f"Value for key '{key}' at index {i} "
                        f"in meta_res_act is not equal to meta_res_exp",
                    )

    @pytest.mark.parametrize(
        ("input_conditions", "padding", "squared_box", "keep_only_largest"),
        [
            ("image_no_detections", 0, False, False),
        ],
    )
    def test_box_cropper_none(
        self,
        input_conditions: str,
        request: Any,
        padding: int,
        squared_box: bool,  # noqa: FBT001
        keep_only_largest: bool,  # noqa: FBT001
    ) -> NoReturn:
        """Tests the 'generate' method when there are no detections.

        This test verifies that the 'generate' method correctly handles cases where
        no detections are present, ensuring that the output is 'None' as expected.
        """
        # --- get input and output conditions ---
        in_image, in_state_odet, _, _ = request.getfixturevalue(
            input_conditions,
        )

        # --- perform box cropping ---
        act_image_res, act_meta_res = DetectionBoxCropper.generate(
            img=in_image,
            state_box=in_state_odet,
            padding=padding,
            squared_box=squared_box,
            keep_only_largest=keep_only_largest,
        )

        # --- test output with expected results ---
        if len(act_image_res) != 1:
            pytest.fail("Only one image should be generated")
        if len(act_image_res[0]) != 1:
            pytest.fail("Only one image should be generated")
        if act_image_res[0][0] is not None:
            pytest.fail("img_res_act should be None")
        if len(act_meta_res) != 1:
            pytest.fail("Only one meta should be generated")
        if len(act_meta_res[0]) != 1:
            pytest.fail("Only one meta should be generated")
        if act_meta_res[0][0] != {}:
            pytest.fail("meta_res_act should be empty")


@pytest.mark.unittest
class TestMakeRoiSquared:
    """A class of unit tests for the 'make_roi_squared' method of 'DetectionBoxCropper'.

    This class contains parameterized tests that check whether the 'make_roi_squared'
    method correctly adjusts the dimensions of a region of interest (ROI) to make it
    square while keeping it within the boundaries of the image.
    """

    @pytest.mark.parametrize(
        ("in_roi_str", "in_img_shape_str", "exp_roi_str"),
        [
            ("roi_w50h100", "img_shape", "roi_w100h100_1"),
            ("roi_w100h50", "img_shape", "roi_w100h100_2"),
        ],
    )
    def test_make_roi_squared(
        self,
        request: Any,
        in_roi_str: str,
        in_img_shape_str: str,
        exp_roi_str: str,
    ) -> NoReturn:
        """Tests the 'make_roi_squared' method.

        This test verifies that the method correctly adjusts an input ROI to be
        square, maintaining the central point of the ROI while adjusting its
        dimensions to be equal.

        Parameters
        ----------
        request : Any
            The PyTest request object used to retrieve fixture values dynamically.
        in_roi_str : str
            The name of the fixture representing the input region of interest (ROI).
        in_img_shape_str : str
            The name of the fixture representing the shape of the image.
        exp_roi_str : str
            The name of the fixture representing the expected output ROI.
        """
        in_roi = request.getfixturevalue(in_roi_str)
        in_img_shape = request.getfixturevalue(in_img_shape_str)
        exp_roi = request.getfixturevalue(exp_roi_str)

        # --- add padding to region of interest ---
        res_roi = DetectionBoxCropper.make_roi_squared(
            copy(in_roi),
            copy(in_img_shape),
        )

        # --- compare result with expected result ---
        if not np.array_equal(res_roi, exp_roi):
            pytest.fail(f"Expected {exp_roi}, but got {res_roi}")


@pytest.mark.unittest
class TestAddPaddingRel:
    """A class of unit tests for the 'add_padding_rel' method of 'DetectionBoxCropper'.

    This class contains parameterized tests that check whether the 'add_padding_rel'
    method correctly adds relative padding (as a percentage of the ROI's dimensions)
    to a given region of interest (ROI).
    """

    @pytest.mark.parametrize(
        ("in_roi_str", "in_img_shape_str", "in_padding_str", "exp_roi_str"),
        [
            ("roi", "img_shape", "padding_zero", "roi"),
            ("roi", "img_shape", "padding_rel_01", "roi_with_padding_rel_01"),
            ("roi", "img_shape", "padding_rel_02", "roi_with_padding_rel_02"),
            ("roi", "img_shape", "padding_rel_03", "roi_with_padding_rel_03"),
            ("roi", "img_shape", "padding_rel_04", "roi_with_padding_rel_04"),
            ("roi", "img_shape", "padding_rel_05", "roi_with_padding_rel_05"),
            ("roi", "img_shape", "padding_rel_06", "roi_with_padding_rel_06"),
            ("roi", "img_shape", "padding_rel_07", "roi_with_padding_rel_07"),
            ("roi", "img_shape", "padding_rel_08", "roi_with_padding_rel_08"),
            ("roi", "img_shape", "padding_rel_09", "roi_with_padding_rel_09"),
            ("roi", "img_shape", "padding_rel_10", "roi_with_padding_rel_10"),
            ("roi", "img_shape", "padding_rel_20", "roi_with_padding_rel_20"),
            ("roi", "img_shape", "padding_rel_m01", "roi_with_padding_rel_m01"),
        ],
    )
    def test_add_padding_rel(
        self,
        request: Any,
        in_roi_str: str,
        in_img_shape_str: str,
        in_padding_str: str,
        exp_roi_str: str,
    ) -> NoReturn:
        """Tests the 'add_padding_rel' method.

        This test verifies that the method correctly adds relative padding to an
        input ROI, expanding its boundaries by the specified percentage of its
        current dimensions.

        Parameters
        ----------
        request : Any
            The PyTest request object used to retrieve fixture values dynamically.
        in_roi_str : str
            The name of the fixture representing the input region of interest (ROI).
        in_img_shape_str : str
            The name of the fixture representing the shape of the image.
        in_padding_str : str
            The name of the fixture representing the padding percentage to be added.
        exp_roi_str : str
            The name of the fixture representing the expected output ROI after padding.
        """
        in_roi = request.getfixturevalue(in_roi_str)
        in_img_shape = request.getfixturevalue(in_img_shape_str)
        in_padding = request.getfixturevalue(in_padding_str)
        exp_roi = request.getfixturevalue(exp_roi_str)

        # --- add padding to region of interest ---
        res_roi = DetectionBoxCropper.add_padding_rel(
            copy(in_roi),
            copy(in_img_shape),
            in_padding,
        )

        # --- compare result with expected result ---
        if not np.array_equal(res_roi, exp_roi):
            pytest.fail(f"Expected {exp_roi}, but got {res_roi}")


@pytest.mark.unittest
class TestAddPaddingAbs:
    """A class of unit tests for the 'add_padding_abs' method of 'DetectionBoxCropper'.

    This class contains parameterized tests that check whether the 'add_padding_abs'
    method correctly adds absolute pixel-based padding to a given region of interest.
    """

    @pytest.mark.parametrize(
        ("in_roi_str", "in_img_shape_str", "in_padding_str", "exp_roi_str"),
        [
            ("roi", "img_shape", "padding_zero", "roi"),
            ("roi", "img_shape", "padding_abs_10", "roi_with_padding_abs_10"),
            ("roi", "img_shape", "padding_abs_20", "roi_with_padding_abs_20"),
            ("roi", "img_shape", "padding_abs_30", "roi_with_padding_abs_30"),
            ("roi", "img_shape", "padding_abs_40", "roi_with_padding_abs_40"),
            ("roi", "img_shape", "padding_abs_50", "roi_with_padding_abs_50"),
            ("roi", "img_shape", "padding_abs_60", "roi_with_padding_abs_60"),
            ("roi", "img_shape", "padding_abs_m10", "roi_with_padding_abs_m10"),
        ],
    )
    def test_add_padding_abs(
        self,
        request: Any,
        in_roi_str: str,
        in_img_shape_str: str,
        in_padding_str: str,
        exp_roi_str: str,
    ) -> NoReturn:
        """Tests the 'add_padding_abs' method.

        This test verifies that the method correctly adds absolute padding (in
        pixels) to an input ROI, expanding its boundaries by the specified number of
        pixels.

        Parameters
        ----------
        request : Any
            The PyTest request object used to retrieve fixture values dynamically.
        in_roi_str : str
            The name of the fixture representing the input region of interest (ROI).
        in_img_shape_str : str
            The name of the fixture representing the shape of the image.
        in_padding_str : str
            The name of the fixture representing the padding amount (in pixels) to be
            added.
        exp_roi_str : str
            The name of the fixture representing the expected output ROI after padding.
        """
        in_roi = request.getfixturevalue(in_roi_str)
        in_img_shape = request.getfixturevalue(in_img_shape_str)
        in_padding = request.getfixturevalue(in_padding_str)
        exp_roi = request.getfixturevalue(exp_roi_str)

        # --- add padding to region of interest ---
        res_roi = DetectionBoxCropper.add_padding_abs(
            copy(in_roi),
            copy(in_img_shape),
            in_padding,
        )

        # --- compare result with expected result ---
        if not np.array_equal(res_roi, exp_roi):
            pytest.fail(f"Expected {exp_roi}, but got {res_roi}")


@pytest.mark.unittest
class TestNodeRegister:
    """A test class for the 'DetectionBoxCropper' registration.

    This class tests the node registration for 'DetectionBoxCropper', ensuring that
    it is correctly registered within the plugin manager and available in the
    'ObjectBuilder' registry.
    """

    def test_node_register(self) -> NoReturn:
        """Test the registration of the 'DetectionBoxCropper' node.

        This test verifies that the 'DetectionBoxCropper' node is properly registered
        in the plugin manager and added to the 'ObjectBuilder' registry.
        """
        # --- create plugin manager from scratch ---
        registry = deepcopy(ObjectBuilder._registry)  # noqa: SLF001
        ObjectBuilder._registry = {}  # noqa: SLF001
        pm = PluginManager(PLUGIN_NAMESPACE)
        pm.add_hookspecs(HookSpecs)
        if (
            pm.is_registered(DetectionBoxCropper)
            or "DetectionBoxCropper" in ObjectBuilder._registry  # noqa: SLF001
        ):
            pytest.fail("'DetectionBoxCropper' already registered")

        # --- register node to plugin manager ---
        if not pm.is_registered(DetectionBoxCropper):
            pm.register(DetectionBoxCropper)
        pm.hook.node_register()

        # --- check whether registration has been successfully ---
        if (
            not pm.is_registered(DetectionBoxCropper)
            or "DetectionBoxCropper" not in ObjectBuilder._registry  # noqa: SLF001
        ):
            pytest.fail("'DetectionBoxCropper' could not have been registered")

        # --- check whether registered object can be actually build ---
        pipe_config = {
            "id": "detection_box_cropper",
            "type": "DetectionBoxCropper",
            "param": {},
        }
        (builder_class, _) = ObjectBuilder.get_builder_class(
            "DetectionBoxCropper",
        )
        pipe_obj = builder_class()(pipe_config, resolve_references=False)
        if not isinstance(pipe_obj, DetectionBoxCropper):
            pytest.fail(
                "'DetectionBoxCropper' object could have been successfully built "
                "through the registered classes.",
            )
        ObjectBuilder._registry = registry  # noqa: SLF001

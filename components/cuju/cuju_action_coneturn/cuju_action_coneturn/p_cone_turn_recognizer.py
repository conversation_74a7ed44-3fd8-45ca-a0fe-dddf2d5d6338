"""Cone Turn Action Recognizer.

High-level orchestrator for the cone turn recognition pipeline.

This module integrates scene understanding, line crossing detection,
turn sequence generation, filtering, and validation into a single
recognition process. It acts as the entry point for detecting cone turns
from video frame data, cone detections, and player keypoints.

"""

from __future__ import annotations

from typing import TYPE_CHECKING

from cuju_action_linecross.line_crossing_detector import Line<PERSON>rossingDetector
from cuju_data_scene.entities.cone import Cone
from cuju_data_scene.entities.tracked_entity import SmoothMethod
from cuju_data_scene.scene_node import SceneNode
from imgeta.pipeline.hook_markers import hook_impl
from imgeta.plugin_base.bases.config_base import ConfigBase
from imgeta.plugin_base.builder.object_builder import ObjectBuilder

from cuju_action_coneturn.line_cross_turn_detector.line_cross_turn_detector import (
    LineCrossTurnDetector,
)
from cuju_action_coneturn.line_cross_turn_detector.src.constants import (
    PersonReferencePoint,
)

if TYPE_CHECKING:
    from cuju_data_scene.actions.cone_turn_action import ConeTurnedAction
    from cuju_data_scene.p_scene_registry import SceneRegistry


class ConeTurnActionConfig(ConfigBase):
    """Configuration for ConeTurnAction.

    Attributes
    ----------
    smooth_method : SmoothMethod
        The method used for smoothing trajectories.
    smooth_window_size : int
        The size of the rolling window for smoothing trajectories.
    position_selection : PositionSelectionConfig
        The configuration for selecting the position of the
        person.
    """

    smooth_method: SmoothMethod = SmoothMethod.SAVGOL
    smooth_window_size: int = 20
    person_position: PersonReferencePoint = PersonReferencePoint.BBOX


class ConeTurnActionRecognizer(SceneNode):
    """Orchestrates cone turn recognition from scene data.

    Responsibilities:
    - Understanding the scene layout
    - Detecting line crossings
    - Generating candidate turn sequences
    - Filtering invalid or duplicate detections
    - Validating turns according to configuration and strategy rules
    """

    def __init__(self, config: ConeTurnActionConfig, **kwargs: dict) -> None:
        """Initialize ConeTurnAction Recognizer."""
        super().__init__(
            ids=config.ids,
            on_control_flag=config.on_control_flag,
            state_storage=config.state_storage,
            **kwargs,
        )
        self._cfg = config
        self._line_cross_detector = LineCrossingDetector()
        self._detector = LineCrossTurnDetector(
            person_position=self._cfg.person_position,
            smooth_window_size=self._cfg.smooth_window_size,
            smooth_method=self._cfg.smooth_method,
        )

    def recognize_turns(
        self,
        scene_registry: SceneRegistry,
        exercise_id: str,
        frame_shape: tuple[int, int] = (720, 1280),
        frame_rate: float = 30.0,
    ) -> list[ConeTurnedAction]:
        """Recognize turns for each moving entity.

        This method retrieves all cone entities from the scene registry and uses the
        LineCrossTurnDetector to detect turns around each cone individually. It
        returns a list of ConeTurnedAction objects representing the detected turns.

        Parameters
        ----------
        scene_register : SceneRegistry
            The entity registry containing all entities.
        exercise_id : str
            The exercise ID.

        Returns
        -------
        list[ConeTurnedAction]
            A list of ConeTurnedAction objects representing the detected turns.

        """
        cone_entities = scene_registry.retrieve_all_entities(
            entity_type=Cone,
        )
        cone_turn_actions = []
        for cone in cone_entities:
            cone_turn_actions.extend(
                self._detector.detect_turns(
                    cone=cone,
                    scene_registry=scene_registry,
                    exercise_id=exercise_id,
                    frame_shape=frame_shape,
                    frame_rate=frame_rate,
                )
            )
        self._detector.shutdown()
        return list(filter(lambda a: a is not None, cone_turn_actions))

    def _understand_scene(self, scene_registry: SceneRegistry) -> None:
        """Apply the ConeTurnAction recognizer."""
        if self._state_storage is not None:
            exercise_id = self._state_storage.get_state("exercise_id")
            frame_shape = self._state_storage.get_state("frame_size")[:-1]
            frame_rate = self._state_storage.get_state("frame_rate")
        else:
            msg = "No global store provided to ConeTurnRecognizer."
            raise RuntimeError(msg)

        cone_turn_actions = self.recognize_turns(
            scene_registry, exercise_id, frame_shape, frame_rate=frame_rate
        )
        for action in cone_turn_actions:
            scene_registry.upsert_action(action)

    @staticmethod
    @hook_impl
    def node_register() -> None:
        """Register a ConeTurnActionRecognizer."""
        ObjectBuilder.direct_register(
            id_str="ConeTurnActionRecognizer",
            object_type=(
                "ConeTurnActionRecognizer",
                "cuju_action_coneturn.p_cone_turn_recognizer",
            ),
            config_type=(
                "ConeTurnActionConfig",
                "cuju_action_coneturn.p_cone_turn_recognizer",
            ),
        )

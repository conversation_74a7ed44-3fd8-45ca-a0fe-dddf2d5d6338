"""Conftest for cuju_action_coneturn unit tests."""

from __future__ import annotations

from datetime import timed<PERSON><PERSON>
from enum import Enum
from unittest.mock import MagicMock

import numpy as np
import pytest
from cuju_action_coneturn.line_cross_turn_detector.src.constants import (
    PersonReferencePoint,
)
from cuju_action_coneturn.p_cone_turn_recognizer import (
    LineCrossTurnDetector,
)
from cuju_data_detection.bounding_box.box_base import BoundingBox
from cuju_data_detection.bounding_box.utils import BoxType
from cuju_data_pose.pose2d.pose_base import Pose
from cuju_data_scene.actions.cone_turn_action import ConeTurnedAction, TurningAngle
from cuju_data_scene.constants import EntityRole
from cuju_data_scene.entities.cone import Cone
from cuju_data_scene.entities.direction import Direction
from cuju_data_scene.entities.person import Person
from cuju_data_scene.p_scene_registry import MatchingCriteria, SceneRegistry
from imgeta.plugin_base.imgeta_tree.frame_time import FrameTime


def generate_trajectory(
    start_pos: tuple[float, float],
    interval: float,
    turn_angle: int | None = None,
    end_pos: tuple[float, float] | None = None,
    duration: float | None = None,
) -> np.ndarray:
    """
    Generate a trajectory based on the provided parameters.

    Parameters
    ----------
    start_pos (tuple): Starting position (x, y).
    interval (float): Distance/time interval between points in the trajectory.
    turn_angle (int, optional): Turning angle for circular trajectories -
        must be 90 or 180 degrees.
    end_pos (tuple, optional): Ending position for straight-line trajectories.
    duration (float, optional): Duration to maintain position for standing trajectories.

    Returns
    -------
    numpy.ndarray: Array of trajectory points with shape (n, 2).

    Usage:
    - For circular turn: generate_trajectory(start_pos, interval, turn_angle=90)
    - For straight line: generate_trajectory(start_pos, interval, end_pos=(x, y))
    - For standing: generate_trajectory(start_pos, interval, duration=5.0)
    """
    # Determine which type of trajectory to generate
    if turn_angle is not None:
        # Validate turn_angle
        if turn_angle not in [90, 180]:
            msg = "Turn angle must be either 90 or 180 degrees"
            raise ValueError(msg)

        # Calculate radius based on interval
        radius = interval / (
            2 * np.sin(np.radians(turn_angle / (2 * (360 / turn_angle))))
        )

        # Number of points needed for the turn
        num_points = int(
            np.ceil(turn_angle / (2 * np.arcsin(interval / (2 * radius)) * 180 / np.pi))
        )

        # Calculate angle step
        angle_step = np.radians(turn_angle) / num_points

        # Initialize trajectory array
        trajectory = np.zeros((num_points + 1, 2))
        trajectory[0] = start_pos

        # Calculate center of turn circle
        # For a clockwise turn, the center is to the right of start position
        center = (start_pos[0], start_pos[1] - radius)

        # Starting angle (pointing upward)
        start_angle = np.pi / 2

        # Generate trajectory points
        for i in range(1, num_points + 1):
            current_angle = start_angle - i * angle_step
            x = center[0] + radius * np.cos(current_angle)
            y = center[1] + radius * np.sin(current_angle)
            trajectory[i] = (x, y)

        return trajectory

    if end_pos is not None:
        # Generate straight-line trajectory
        start_pos_array = np.array(start_pos)
        end_pos_array = np.array(end_pos)

        # Calculate total distance
        total_distance = np.linalg.norm(end_pos_array - start_pos_array)

        # Calculate number of points
        num_points = int(np.ceil(total_distance / interval))

        # Generate trajectory points with equal spacing
        trajectory = np.zeros((num_points + 1, 2))

        for i in range(num_points + 1):
            t = i / num_points  # Interpolation parameter [0, 1]
            trajectory[i] = start_pos_array * (1 - t) + end_pos_array * t

        return trajectory

    if duration is not None:
        # Generate standing trajectory
        num_points = int(np.ceil(duration / interval))

        # Create trajectory with all points at the same position
        trajectory = np.zeros((num_points + 1, 2))
        trajectory[:] = start_pos

        return trajectory

    msg = "Must provide either turn_angle, end_pos, or duration"
    raise ValueError(msg)


def combine_trajectories(trajectories: list[np.ndarray]) -> np.ndarray:
    """
    Combine multiple trajectory segments into a single continuous trajectory.

    Parameters
    ----------
    trajectories (List[np.ndarray]): List of trajectory segments to combine.

    Returns
    -------
    numpy.ndarray: Combined trajectory with duplicates at join points removed.
    """
    if not trajectories:
        return np.zeros((0, 2))

    # Start with the first trajectory
    combined = trajectories[0]

    # Add each subsequent trajectory, skipping the first point (to avoid duplicates)
    for traj in trajectories[1:]:
        combined = np.vstack((combined, traj[1:]))

    return combined


def clock_wise_90_trajectory(
    starting_position: tuple[int, int],
    interval: float,
    move_length: int,
) -> np.ndarray:
    """Return a 90-degree clockwise turn trajectory."""
    traj1 = generate_trajectory(
        start_pos=starting_position, interval=interval, duration=2.0
    )
    traj2 = generate_trajectory(
        start_pos=starting_position,
        interval=interval,
        end_pos=(starting_position[0] + move_length, starting_position[1]),
    )
    traj3 = generate_trajectory(
        start_pos=(starting_position[0] + move_length, starting_position[1]),
        interval=interval,
        end_pos=(
            starting_position[0] + move_length,
            starting_position[1] + move_length,
        ),
    )
    return combine_trajectories([traj1, traj2, traj3])


def clock_wise_180_trajectory(
    starting_position: tuple[int, int],
    interval: float,
    move_length: int,
) -> np.ndarray:
    """Return a 180-degree clockwise turn trajectory."""
    traj1 = generate_trajectory(
        start_pos=starting_position, interval=interval, duration=2.0
    )
    traj2 = generate_trajectory(
        start_pos=starting_position,
        interval=interval,
        end_pos=(starting_position[0] + move_length, starting_position[1]),
    )
    traj3 = generate_trajectory(
        start_pos=(starting_position[0] + move_length, starting_position[1]),
        interval=interval,
        end_pos=(
            starting_position[0] + move_length,
            starting_position[1] + move_length,
        ),
    )
    traj4 = generate_trajectory(
        start_pos=(
            starting_position[0] + move_length,
            starting_position[1] + move_length,
        ),
        interval=interval,
        end_pos=(starting_position[0], starting_position[1] + move_length),
    )
    return combine_trajectories([traj1, traj2, traj3, traj4])


@pytest.fixture
def cone_turn_detector() -> LineCrossTurnDetector:
    """Return a LineCrossTurnDetector."""
    return LineCrossTurnDetector(
        person_position=PersonReferencePoint.BBOX,
        smooth_window_size=5,
    )


class MovingDirection(Enum):
    """Enum for moving directions."""

    RIGHT = 0  # Moving along positive x
    DOWN = 90  # Moving along negative y
    LEFT = 180  # Moving along negative x
    UP = 270  # Moving along positive y


def bounding_box(marker: tuple[float, float]) -> BoundingBox:
    """Create a simple bounding box."""
    return BoundingBox(coords=np.array([*marker, 1, 1]), box_type=BoxType.CXCYWH)


def pose(marker: tuple[float, float]) -> Pose:
    """Create a pose from marker (marker will be left hip)."""
    coords = np.random.randn(17, 2)
    coords[11, :] = marker  # Left hip
    return Pose(coords=coords)


def create_scene_registry(
    track: list[tuple[float, float]],
) -> SceneRegistry:
    """Create a scene registry with a run simulation over a given number of frames."""
    scene_registry = SceneRegistry()
    boxes, poses, tids, frame_times = [], [], [], []
    for i, marker in enumerate(track):
        tids.append("track-0")
        boxes.append(
            bounding_box(marker),
        )
        frame_times.append(
            FrameTime(
                frame_number=i + 1,
                frame_time=timedelta(seconds=i),
                time_since_start=timedelta(seconds=i),
            )
        )
        poses.append(pose(marker))

    scene_registry.upsert_entity(
        Person(
            id="person-0",
            boxes=boxes,
            poses=poses,
            track_ids=tids,
            frame_time=frame_times,
            role=EntityRole.MAIN_PERSON,
        ),
        matching_criteria=MatchingCriteria.ID,
    )
    cone_entity = cone_mock(len(track))
    scene_registry.upsert_entity(
        cone_entity,
        matching_criteria=MatchingCriteria.ID,
    )
    return scene_registry


@pytest.fixture
def no_cone_turned_standing() -> tuple[SceneRegistry, ConeTurnedAction]:
    """Fixture for recognize_cone_turned, where the person is just standing."""
    track = [(0, 0)] * 10
    registry = create_scene_registry(track)
    return registry, None


@pytest.fixture
def no_cone_turned() -> tuple[SceneRegistry, ConeTurnedAction]:
    """Fixture for recognize_cone_turned, where the person is just standing."""
    track = [(0, 0), (0, 10), (10, 10), (10, 0), (0, 0)]
    registry = create_scene_registry(track)
    return registry, None


@pytest.fixture
def cone_turned_detected_90() -> tuple[SceneRegistry, ConeTurnedAction]:
    """Fixture for recognize_cone_turned, where running onset should be detected."""
    frame_time = FrameTime(
        frame_number=8,
        frame_time=timedelta(seconds=1),
        time_since_start=timedelta(seconds=1),
    )
    track = clock_wise_90_trajectory(
        starting_position=(20, 20),
        interval=2,
        move_length=20,
    )
    registry = create_scene_registry(track)
    expected_action = ConeTurnedAction(
        id="cone_turned_action",
        time=frame_time,
        entity_id="person-0",
        cone_id="track-0",
        direction=Direction.CCW,
        position=(39.48978545434635, 30.0),
        angle=TurningAngle.ANGLE_90,
    )

    return registry, expected_action


@pytest.fixture
def cone_turned_detected_180() -> tuple[SceneRegistry, ConeTurnedAction]:
    """Fixture for recognize_cone_turned, where running onset should be detected."""
    frame_time = FrameTime(
        frame_number=8,
        frame_time=timedelta(seconds=1),
        time_since_start=timedelta(seconds=1),
    )
    track = clock_wise_180_trajectory(
        starting_position=(20, 20),
        interval=2,
        move_length=20,
    )
    registry = create_scene_registry(track)

    expected_action = ConeTurnedAction(
        id="cone_turned_action",
        time=frame_time,
        entity_id="person-0",
        cone_id="track-0",
        direction=Direction.CCW,
        position=(30.0, 40.492769858106016),
        angle=TurningAngle.ANGLE_180,
    )

    return registry, expected_action


def cone_mock(num_frames: int) -> Cone:
    """Return a Cone."""
    box = BoundingBox(
        coords=np.array([30.0, 30.0, 22.0, 22.0]), box_type=BoxType.CXCYWH
    )
    return Cone(
        role=EntityRole.CONE_1,
        track_ids=["0"] * num_frames,
        initial_position=box,
        boxes=[box] * num_frames,
        frame_time=[
            FrameTime(
                frame_number=i,
                frame_time=timedelta(seconds=i),
                time_since_start=timedelta(seconds=i),
            )
            for i in range(1, num_frames + 1)
        ],
    )


@pytest.fixture
def mock_cone_detector() -> MagicMock:
    """Return a mock cone detector."""
    return MagicMock(spec=LineCrossTurnDetector)

"""Consolidated conftest for cuju_action_coneturn unit tests."""

from __future__ import annotations

from datetime import timed<PERSON>ta
from unittest.mock import MagicMock

import numpy as np
import pytest
from cuju_action_coneturn.line_cross_turn_detector.src.cone_line_cross_detector import (
    ConeLine<PERSON>rossDetector,
    ConeLineCrossDetectorConfig,
)
from cuju_action_coneturn.line_cross_turn_detector.src.constants import (
    LineCreationParams,
    PersonReferencePoint,
    PositionValidationEntityParams,
    PositionValidationParams,
    ProjectionType,
)
from cuju_data_detection.bounding_box.box_base import BoundingBox
from cuju_data_detection.bounding_box.utils import BoxType
from cuju_data_pose.pose2d.keypoint_topology_base import CocoTopology
from cuju_data_pose.pose2d.pose_base import Pose
from cuju_data_scene.actions.cone_turn_action import (
    ConeCrossingLine,
    ConeTurnedAction,
    TurningAngle,
)
from cuju_data_scene.actions.line_crossed_action import Line<PERSON>rossedAction
from cuju_data_scene.constants import Entity<PERSON>ole
from cuju_data_scene.entities.cone import Cone
from cuju_data_scene.entities.direction import Direction
from cuju_data_scene.entities.line import ImageEdge, Line, LineEntity
from cuju_data_scene.entities.person import Person
from cuju_data_scene.entities.tracked_entity import SmoothMethod, ToPointMethod
from cuju_data_scene.p_scene_registry import MatchingCriteria, SceneRegistry
from imgeta.plugin_base.imgeta_tree.frame_time import FrameTime

# ═══════════════════════════════════════════════════════════════════
# CORE CONFIGURATION FIXTURES
# ═══════════════════════════════════════════════════════════════════


@pytest.fixture
def n_frames() -> int:
    """Return number of frames for testing."""
    return 5


@pytest.fixture
def detector_config() -> ConeLineCrossDetectorConfig:
    """Create a configuration for the ConeLineCrossDetector."""
    return ConeLineCrossDetectorConfig(
        person_position="bbox",
        smooth_method=SmoothMethod.NONE,
        smooth_window_size=1,
        frame_shape=(720, 1280),
        frame_buffer_duplicate_crossings=1,
        frame_offset_person_motion=1,
        resolve_line_crossing_conflicts=False,
    )


@pytest.fixture
def line_creation_params() -> LineCreationParams:
    """Create line creation parameters for testing."""
    return LineCreationParams(
        angle_to_line_length={0: 100, 90: 100},
        angle_to_image_edge={0: ImageEdge.RIGHT, 90: ImageEdge.TOP},
        vertical_line_name="vertical_line",
        offset_to_centre=True,
        project_to_bottom=ProjectionType.NONE,
    )


@pytest.fixture
def position_validation_params() -> PositionValidationParams:
    """Create default position validation parameters."""
    return PositionValidationParams(
        validate_first_direction=True,
        validate_second_position=True,
        validate_third_position=True,
        reference_cone_position=np.array([100, 100]),
        validate_intersection=True,
        tolerance_px_below_cam=10.0,
        tolerance_px_away_cam=10.0,
        tolerance_above_ref_cone=2.0,
        validate_turns_from_ref_cone=False,
    )


# ═══════════════════════════════════════════════════════════════════
# ENTITY FIXTURES
# ═══════════════════════════════════════════════════════════════════


@pytest.fixture
def test_cone(n_frames: int) -> Cone:
    """Create a standard cone entity for testing."""
    return Cone(
        id="cone1",
        role=EntityRole.CONE_1,
        initial_position=BoundingBox(
            coords=np.array([500, 300, 90, 90]), box_type=BoxType.XYWH
        ),
        track_ids=["t1"] * n_frames,
        boxes=[
            BoundingBox(coords=np.array([500, 300, 90, 90]), box_type=BoxType.XYWH)
            for _ in range(n_frames)
        ],
        frame_time=[
            FrameTime(
                frame_number=100 + i,
                timestamp=0.04 * i,
                frame_time=timedelta(seconds=0.04 * i),
                time_since_start=timedelta(seconds=0.04 * i),
            )
            for i in range(n_frames)
        ],
        default_conversion_method=ToPointMethod.BBOX_CENTER,
        name="cone1",
    )


@pytest.fixture
def test_person(n_frames: int) -> Person:
    """Create a standard person entity for testing."""
    return Person(
        id="person1",
        track_ids=["p1"] * n_frames,
        boxes=[
            BoundingBox(
                coords=np.array([100 + i * 10, 200 + i * 5, 50, 50]),
                box_type=BoxType.XYWH,
            )
            for i in range(n_frames)
        ],
        frame_time=[
            FrameTime(
                frame_number=100 + i,
                timestamp=0.04 * i,
                frame_time=timedelta(seconds=0.04 * i),
                time_since_start=timedelta(seconds=0.04 * i),
            )
            for i in range(n_frames)
        ],
        default_conversion_method=ToPointMethod.BBOX_CENTER,
        name="person1",
    )


@pytest.fixture
def person_with_pose() -> Person:
    """Create a Person with pose data for motion vector testing."""
    # Create coords with correct shape (17, 2) for COCO
    kp0 = np.zeros((17, 2), dtype=np.float32)
    kp3 = np.zeros((17, 2), dtype=np.float32)

    # "left_ankle" index in COCO = 15
    kp0[15] = [100.0, 200.0]
    kp3[15] = [106.0, 208.0]

    boxes = [
        BoundingBox(coords=np.array([100.0, 200.0, 50.0, 50.0]), box_type=BoxType.XYWH),
        BoundingBox(coords=np.array([106.0, 208.0, 50.0, 50.0]), box_type=BoxType.XYWH),
    ]
    poses = [
        Pose(coords=kp0, topology=CocoTopology.default()),
        Pose(coords=kp3, topology=CocoTopology.default()),
    ]

    return Person(
        entity_id="person_1",
        poses=poses,
        frame_time=[
            FrameTime(
                frame_number=0,
                frame_time=timedelta(seconds=0),
                time_since_start=timedelta(seconds=0),
            ),
            FrameTime(
                frame_number=3,
                frame_time=timedelta(seconds=1),
                time_since_start=timedelta(seconds=1),
            ),
        ],
        boxes=boxes,
        conversion_keypoints=["left_ankle"],
    )


# Mock entities for position validation tests
@pytest.fixture
def mock_cone() -> Cone:
    """Create a mock Cone entity for position validation testing."""

    class MockCone(Cone):
        def to_point(self, method: ToPointMethod, frame: int) -> tuple[float, float]:  # noqa: ARG002
            return (100.0, 100.0)

    return MockCone(
        name="cone1",
        bboxes={},
        frame_numbers=list(range(200)),
        initial_position=BoundingBox(
            coords=np.array([90, 90, 110, 110]), box_type=BoxType.XYXY
        ),
    )


@pytest.fixture
def mock_person() -> Person:
    """Create a mock Person entity for position validation testing."""

    class MockPerson(Person):
        def to_point(
            self,
            method: ToPointMethod,  # noqa: ARG002
            frame: int,  # noqa: ARG002
            keypoints: np.ndarray,  # noqa: ARG002
            *,
            project: bool = True,  # noqa: ARG002
        ) -> tuple[float, float]:
            return (80.0, 90.0)

    return MockPerson(
        entity_id="p1",
        frame_time=[
            FrameTime(
                frame_number=i,
                frame_time=timedelta(milliseconds=0.3333),
                time_since_start=timedelta(seconds=i * 0.3333),
            )
            for i in range(200)
        ],
    )


# ═══════════════════════════════════════════════════════════════════
# LINE ENTITY FIXTURES
# ═══════════════════════════════════════════════════════════════════


@pytest.fixture
def test_line_entity(n_frames: int) -> LineEntity:
    """Create a standard LineEntity for testing."""

    class TestLine(Line):
        def get_crossing_direction_vector(self, direction: Direction) -> np.ndarray:
            return (
                np.array([1.0, 0.0])
                if direction == Direction.LTR
                else np.array([-1.0, 0.0])
            )

    line = TestLine(p1=np.array([0, 0]), p2=np.array([10, 0]))
    return LineEntity(
        name="line-1",
        lines=[line] * n_frames,
        frame_time=[
            FrameTime(
                frame_number=100 + i,
                frame_time=timedelta(milliseconds=0.3333),
                time_since_start=timedelta(seconds=(100 + i) * 0.3333),
            )
            for i in range(n_frames)
        ],
    )


@pytest.fixture
def horizontal_line_entity() -> LineEntity:
    """Create a horizontal line entity for position validation testing."""

    class HorizontalLine(Line):
        def get_line_direction_vector(self) -> np.ndarray:
            return np.array([1.0, -1.0])

        def get_x_for_y(self, y: float) -> float:  # noqa: ARG002
            return 100.0

        def angle_image(self) -> float:
            return 0.0

    dummy_line = HorizontalLine(p1=np.array([0, 0]), p2=np.array([10, 0]))
    return LineEntity(
        name="horizontal",
        lines=[dummy_line] * 200,
        frame_time=[
            FrameTime(
                frame_number=i,
                frame_time=timedelta(milliseconds=0.3333),
                time_since_start=timedelta(seconds=i * 0.3333),
            )
            for i in range(200)
        ],
    )


@pytest.fixture
def scene_registry_with_intersecting_lines() -> SceneRegistry:
    """Create a scene registry with intersecting vertical and horizontal lines."""
    # Vertical line at x = 100
    line_a = Line(p1=np.array([100, 0]), p2=np.array([100, 200]))
    # Horizontal line at y = 100
    line_b = Line(p1=np.array([0, 100]), p2=np.array([200, 100]))

    entity_a = LineEntity(
        name="line-a",
        lines=[line_a] * 30,
        frame_time=[
            FrameTime(
                frame_number=i,
                frame_time=timedelta(milliseconds=0.3333),
                time_since_start=timedelta(seconds=i * 0.3333),
            )
            for i in range(30)
        ],
    )
    entity_b = LineEntity(
        name="line-b",
        lines=[line_b] * 30,
        frame_time=[
            FrameTime(
                frame_number=i,
                frame_time=timedelta(milliseconds=0.3333),
                time_since_start=timedelta(seconds=i * 0.3333),
            )
            for i in range(30)
        ],
    )
    scene_registry = SceneRegistry()
    scene_registry.upsert_entity(entity_a, MatchingCriteria.NAME)
    scene_registry.upsert_entity(entity_b, MatchingCriteria.NAME)
    return scene_registry


# ═══════════════════════════════════════════════════════════════════
# COMPOSITE FIXTURES
# ═══════════════════════════════════════════════════════════════════


@pytest.fixture
def test_registry(test_line_entity: LineEntity) -> SceneRegistry:
    """Create a test SceneRegistry with standard line entity."""

    class TestSceneRegistry(SceneRegistry):
        def retrieve_entity(
            self,
            criteria,  # noqa: ANN001, ARG002
            matching_criteria,  # noqa: ANN001, ARG002
            entity_type,  # noqa: ANN001, ARG002
        ) -> LineEntity:
            return test_line_entity

    return TestSceneRegistry()


@pytest.fixture
def position_validation_entities(
    mock_person: Person, mock_cone: Cone, horizontal_line_entity: LineEntity
) -> PositionValidationEntityParams:
    """Create position validation entity parameters for testing."""
    return PositionValidationEntityParams(
        person=mock_person,
        cone=mock_cone,
        center_horizontal_line_entity=horizontal_line_entity,
    )


@pytest.fixture
def standard_validation_entities(
    test_person: Person,
    test_cone: Cone,
    test_line_entity: LineEntity,
) -> PositionValidationEntityParams:
    """Create standard position validation entity parameters."""
    return PositionValidationEntityParams(
        person=test_person,
        cone=test_cone,
        center_horizontal_line_entity=test_line_entity,
    )


@pytest.fixture
def cone_line_detector() -> ConeLineCrossDetector:
    """Create a ConeLineCrossDetector for testing."""
    return ConeLineCrossDetector(
        config=ConeLineCrossDetectorConfig(
            person_position=PersonReferencePoint.BBOX,
        ),
        cone=MagicMock(spec=Cone),
    )


# ═══════════════════════════════════════════════════════════════════
# ACTION FACTORY FIXTURES
# ═══════════════════════════════════════════════════════════════════


@pytest.fixture
def line_crossed_action_factory() -> callable[
    [int, str, str | None], LineCrossedAction
]:
    """Create LineCrossedAction instances."""

    def _create_action(
        frame: int,
        entity_id: str,
        line_id: str | None = None,
    ) -> LineCrossedAction:
        """Create a LineCrossedAction with given frame."""
        return LineCrossedAction(
            detected_frame=frame,
            time=FrameTime(
                frame_number=frame,
                frame_rate=30.0,
                frame_time=timedelta(seconds=frame / 30.0),
                time_since_start=timedelta(seconds=frame / 30.0),
            ),
            entity_id=entity_id,
            line_id=line_id or f"line_{entity_id}",
            crossing_direction=Direction.LTR,
            observed_point="bb_center_center",
            position=(100.0, 100.0),
            crossing_trajectory=(90.0, 90.0, 110.0, 110.0),
        )

    return _create_action


@pytest.fixture
def cone_turned_action_factory() -> callable[
    [str, int, int, str, str], ConeTurnedAction
]:
    """Create ConeTurnedAction instances."""

    def _create_turn(
        cone_id: str,
        start_frame: int,
        end_frame: int,
        entity_id: str = "person1",
        turn_type: str = "full",
    ) -> ConeTurnedAction:
        """Create a ConeTurnedAction with given parameters."""
        return ConeTurnedAction(
            time=FrameTime(
                frame_number=start_frame,
                frame_rate=30.0,
                frame_time=timedelta(seconds=start_frame / 30.0),
                time_since_start=timedelta(seconds=start_frame / 30.0),
            ),
            entity_id=entity_id,
            cone_id=cone_id,
            direction=Direction.LTR if turn_type == "full" else Direction.CCW,
            angle=TurningAngle.ANGLE_180
            if turn_type == "full"
            else TurningAngle.ANGLE_90,
            meta={"start_frame": start_frame, "end_frame": end_frame},
            position=(100.0, 100.0),
        )

    return _create_turn


@pytest.fixture
def standard_cone_turn_action() -> ConeTurnedAction:
    """Create a standard ConeTurnedAction for testing."""
    return ConeTurnedAction(
        entity_id="p1",
        cone_id="c1",
        direction=Direction.CW,
        angle=TurningAngle.ANGLE_180,
        position=(100, 100),
        entering_line=ConeCrossingLine.HORIZONTAL,
        time=FrameTime(
            frame_number=100,
            frame_rate=30.0,
            frame_time=timedelta(seconds=100 / 30.0),
            time_since_start=timedelta(seconds=100 / 30.0),
        ),
        meta={
            "crossing_positions": [(90, 100), (100, 100), (110, 100)],
            "crossing_directions": [Direction.LTR, Direction.LTR, Direction.RTL],
            "person_position": ["bbox_center_bottom"] * 3,
            "project": [False] * 3,
        },
    )


# ═══════════════════════════════════════════════════════════════════
# TEST DATA FIXTURES
# ═══════════════════════════════════════════════════════════════════


@pytest.fixture
def filter_turns_test_cases(
    cone_turned_action_factory: callable[[str, int, int, str, str], ConeTurnedAction],
) -> dict:
    """Create test cases for filter_contained_turns function."""
    # Case 1: Nested intervals
    case1_turns = [
        cone_turned_action_factory(
            "cone1", 10, 50, "person1", "full"
        ),  # A (should keep)
        cone_turned_action_factory(
            "cone1", 20, 40, "person1", "full"
        ),  # B (should filter out)
        cone_turned_action_factory(
            "cone2", 60, 100, "person1", "full"
        ),  # C (should keep)
    ]
    case1_expected = ["cone1_10_50", "cone2_60_100"]

    # Case 3: Empty and None values
    case3_turns = [None, cone_turned_action_factory("cone4", 5, 25, "person3", "full")]
    case3_expected = ["cone4_5_25"]

    # Case 4: Equal intervals
    case4_turns = [
        cone_turned_action_factory("cone5", 10, 30, "person1", "full"),
        cone_turned_action_factory("cone6", 10, 30, "person2", "full"),
    ]
    case4_expected = ["cone5_10_30", "cone6_10_30"]

    return {
        "nested": {"turns": case1_turns, "expected_ids": case1_expected},
        "with_none": {"turns": case3_turns, "expected_ids": case3_expected},
        "equal": {"turns": case4_turns, "expected_ids": case4_expected},
    }


@pytest.fixture
def standard_test_actions(
    line_crossed_action_factory: callable[[int, str, str | None], LineCrossedAction],
) -> dict:
    """Create a standard set of actions for testing."""
    return {
        # First list actions
        "a1": line_crossed_action_factory(10, "person1", "line1"),
        "a2": line_crossed_action_factory(20, "person1", "line2"),
        # Second list actions
        "b1": line_crossed_action_factory(
            5, "person1", "line3"
        ),  # Before a1 (will be filtered)
        "b2": line_crossed_action_factory(15, "person1", "line4"),  # Between a1 and a2
        "b3": line_crossed_action_factory(25, "person1", "line5"),  # After a2
        # Third list actions
        "c1": line_crossed_action_factory(
            12, "person1", "line6"
        ),  # Before b2 (invalid sequence)
        "c2": line_crossed_action_factory(30, "person1", "line7"),  # Valid for triplets
        "c3": line_crossed_action_factory(40, "person1", "line8"),  # Valid for triplets
    }


@pytest.fixture
def full_turn_test_case(
    standard_test_actions: dict,
) -> tuple[list[list[LineCrossedAction]], list[str]]:
    """Create test case for full turn scenarios."""
    return [
        [standard_test_actions["a1"], standard_test_actions["a2"]],
        [
            standard_test_actions["b1"],
            standard_test_actions["b2"],
            standard_test_actions["b3"],
        ],
        [
            standard_test_actions["c1"],
            standard_test_actions["c2"],
            standard_test_actions["c3"],
        ],
    ], [
        # Expected valid triplets after filtering
        ["line1", "line4", "line7"],
        ["line1", "line4", "line8"],
        ["line1", "line5", "line7"],
        ["line1", "line5", "line8"],
        ["line2", "line5", "line7"],
        ["line2", "line5", "line8"],
    ]


@pytest.fixture
def half_turn_test_case(
    standard_test_actions: dict,
) -> tuple[list[list[LineCrossedAction]], list[str]]:
    """Create test case for half turn scenarios."""
    return [
        [standard_test_actions["a1"], standard_test_actions["a2"]],
        [
            standard_test_actions["b1"],
            standard_test_actions["b2"],
            standard_test_actions["b3"],
        ],
    ], [["line1", "line4"], ["line1", "line5"], ["line2", "line5"]]

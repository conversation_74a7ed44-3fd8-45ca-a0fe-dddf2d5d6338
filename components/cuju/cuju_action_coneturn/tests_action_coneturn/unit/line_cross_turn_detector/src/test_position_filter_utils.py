"""Tests for position filter utilities in the line cross turn detector."""

from datetime import <PERSON><PERSON><PERSON>
from typing import Any
from unittest.mock import <PERSON><PERSON><PERSON>, patch

import numpy as np
import pandas as pd
import pytest
from cuju_action_coneturn.line_cross_turn_detector.src.constants import (
    PositionValidationEntityParams,
    PositionValidationParams,
)
from cuju_action_coneturn.line_cross_turn_detector.src.position_filter_utils import (
    ConeCoords,
    _get_person_y_position,
    check_nearness_to_cone,
    extend_line,
    is_above_below_cone,
    is_first_action_valid,
    is_horizontal_crossing_valid,
    is_next_position_valid,
    is_opposite_sides_of_intersection,
    is_person_above_cone,
)
from cuju_data_detection.bounding_box.box_base import BoundingBox
from cuju_data_pose.pose2d.keypoint_topology_base import CocoTopology
from cuju_data_pose.pose2d.pose_base import Pose
from cuju_data_scene.actions.cone_turn_action import (
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    ConeTurnedAction,
    TurningAngle,
)
from cuju_data_scene.constants import ExerciseId
from cuju_data_scene.entities.direction import Direction
from cuju_data_scene.entities.line import Line
from cuju_data_scene.entities.person import Person
from cuju_data_scene.entities.tracked_entity import ToPointMethod
from cuju_data_scene.p_scene_registry import SceneRegistry
from imgeta.plugin_base.imgeta_tree.frame_time import FrameTime

# ──────────────────────────────────────────────────
# ACTION FACTORY FUNCTION
# ──────────────────────────────────────────────────


def make_person_with_boxes(
    frame_numbers: list[int],
    xyxy_boxes: list[list[float]],
) -> Person:
    """Create a Person with given frame_numbers and XYXY boxes.

    Each item of xyxy_boxes is [x1, y1, x2, y2] for the corresponding frame.
    """
    boxes = [BoundingBox(coords=np.array(b, dtype=float)) for b in xyxy_boxes]

    # --- MINIMAL Pose mocks that satisfy validation ---
    poses: list[Pose] = []
    for _ in xyxy_boxes:
        p = MagicMock(spec=Pose)
        p.coords = np.zeros((17, 2), dtype=float)
        p.visibility = np.ones((17,), dtype=bool)
        p.topology = CocoTopology.default()
        p.get_coords_by_keypoint_name.return_value = np.array([0.0, 0.0], dtype=float)

        poses.append(p)
    # --------------------------------------------------

    frame_time = [
        FrameTime(
            frame_number=n, frame_time=timedelta(0), time_since_start=timedelta(0)
        )
        for n in frame_numbers
    ]
    return Person(boxes=boxes, frame_time=frame_time, poses=poses)


def make_cone_turn_action(  # noqa: PLR0913
    crossing_positions: list[tuple[float, float]],
    crossing_directions: list[Direction],
    direction: Direction,
    entering_line: ConeCrossingLine = ConeCrossingLine.VERTICAL,
    frame_number: int = 50,
    angle: TurningAngle = TurningAngle.ANGLE_180,
    position: tuple[float, float] = (100.0, 100.0),
    lines: list[str] | None = None,
) -> ConeTurnedAction:
    """Create a mock ConeTurnedAction for testing."""
    return ConeTurnedAction(
        entity_id="p1",
        cone_id="cone1",
        direction=direction,
        angle=angle,
        position=position,
        entering_line=entering_line,
        meta={
            "crossing_positions": crossing_positions,
            "crossing_directions": crossing_directions,
            "person_position": [
                "kpt_center_left_ankle_right_ankle"
                for _ in range(len(crossing_positions))
            ],
            "project": [False] * len(crossing_positions),
            "start_frame": frame_number,
            "mid_frame": frame_number + 5,
            "lines": lines
            if lines is not None
            else [
                "vertical"
                if entering_line == ConeCrossingLine.VERTICAL
                else "horizontal"
                for _ in range(len(crossing_positions))
            ],
        },
        time=FrameTime(
            frame_number=frame_number,
            frame_time=timedelta(milliseconds=0.3333),
            time_since_start=timedelta(seconds=frame_number * 0.3333),
        ),
    )


# ──────────────────────────────
# TEST CLASS
# ──────────────────────────────


@pytest.mark.unittest
class TestPositionValidation:
    """Tests for position validation utilities in the line cross turn detector."""

    @pytest.mark.parametrize(
        ("crossing_dir", "direction", "expected"),
        [
            (Direction.RTL, Direction.CW, True),
            (Direction.RTL, Direction.CCW, False),
            (Direction.LTR, Direction.CW, False),
            (Direction.LTR, Direction.CCW, True),
        ],
    )
    def test_is_first_action_valid(
        self,
        position_validation_entities: PositionValidationEntityParams,
        position_validation_params: PositionValidationParams,
        crossing_dir: Direction,
        direction: Direction,
        *,
        expected: bool,
    ) -> None:
        """Test if the first action is valid based on crossing and turn direction."""
        action = make_cone_turn_action(
            crossing_positions=[(80.0, 90.0), (80.0, 90.0)],
            crossing_directions=[crossing_dir],
            direction=direction,
        )
        assert (
            is_first_action_valid(
                action, position_validation_entities, position_validation_params
            )
            == expected
        )

    @pytest.mark.parametrize(
        ("positions", "dirs", "direction", "expected"),
        [
            ([(80, 90), (82, 88)], [Direction.RTL, Direction.LTR], Direction.CW, True),
            ([(80, 90), (82, 98)], [Direction.RTL, Direction.LTR], Direction.CW, False),
            (
                [(120, 90), (118, 88)],
                [Direction.RTL, Direction.LTR],
                Direction.CCW,
                True,
            ),
            (
                [(102, 120), (102, 80)],
                [Direction.LTR, Direction.LTR],
                Direction.CCW,
                False,
            ),
        ],
    )
    def test_is_next_position_valid(  # noqa: PLR0913
        self,
        position_validation_entities: PositionValidationEntityParams,
        position_validation_params: PositionValidationParams,
        positions: list[tuple[float, float]],
        dirs: list[Direction],
        direction: Direction,
        *,
        expected: bool,
    ) -> None:
        """Test if the next position is valid using positions and directions."""
        action = make_cone_turn_action(
            crossing_positions=positions,
            crossing_directions=dirs,
            direction=direction,
        )
        assert (
            is_next_position_valid(
                action, position_validation_entities, position_validation_params
            )
            == expected
        )

    @pytest.mark.parametrize(
        ("cone_pos", "point", "dir_vec", "base_tol", "expected"),
        [
            ((100, 100), (100, 90), (0, -1), 5, "above"),
            ((100, 100), (100, 110), (0, -1), 5, "below"),
            ((100, 100), (100, 97), (0, -1), 5, "near"),
        ],
    )
    def test_is_above_below_cone(
        self,
        cone_pos: tuple[float, float],
        point: tuple[float, float],
        dir_vec: tuple[float, float],
        base_tol: float,
        expected: str,
    ) -> None:
        """Test if a point is above, below, or near the cone."""
        assert is_above_below_cone(cone_pos, point, dir_vec, base_tol) == expected

    @pytest.mark.parametrize(
        ("p1", "p2", "scale", "min_ext", "expected_len"),
        [
            ((0, 0), (10, 0), 3.0, 100.0, 210.0),
            ((0, 0), (40, 0), 3.0, 10.0, 280.0),
            ((0, 0), (0, 0), 3.0, 100.0, 0),
        ],
    )
    def test_extend_line(
        self,
        p1: tuple[float, float],
        p2: tuple[float, float],
        scale: float,
        min_ext: float,
        expected_len: float,
    ) -> None:
        """Test the extend_line function."""
        line = Line(p1=np.array(p1), p2=np.array(p2))
        extended = extend_line(line, scale=scale, min_extension=min_ext)
        length = np.linalg.norm(extended.p2 - extended.p1)
        assert pytest.approx(length, abs=1.0) == expected_len

    def test_horizontal_crossing_valid_expected_right_shift(
        self,
        position_validation_entities: PositionValidationEntityParams,
        position_validation_params: PositionValidationParams,
    ) -> None:
        """Test if horizontal crossing is valid with expected right shift."""
        action = make_cone_turn_action(
            crossing_positions=[(100, 95), (120, 95)],
            crossing_directions=[Direction.LTR, Direction.LTR],
            direction=Direction.CW,
            entering_line=ConeCrossingLine.HORIZONTAL,
        )
        coords = ConeCoords(x=100, y=100, adjusted_x=100)
        assert is_horizontal_crossing_valid(
            action, 0, coords, position_validation_entities, position_validation_params
        )

    @pytest.mark.parametrize(
        ("entering_line", "first_pos", "last_pos", "expected"),
        [
            (ConeCrossingLine.VERTICAL, (80, 100), (120, 100), True),  # Opp X
            (ConeCrossingLine.VERTICAL, (90, 100), (110, 100), True),  # Still opposite
            (ConeCrossingLine.VERTICAL, (95, 100), (105, 100), True),
            (ConeCrossingLine.VERTICAL, (90, 100), (95, 100), False),  # Same side
            (ConeCrossingLine.HORIZONTAL, (100, 80), (100, 120), True),  # Oppo Y sides
            (ConeCrossingLine.HORIZONTAL, (100, 90), (100, 110), True),
            (ConeCrossingLine.HORIZONTAL, (100, 95), (100, 96), False),  # Same side
        ],
    )
    def test_opposite_sides_detection(  # noqa: PLR0913
        self,
        entering_line: ConeCrossingLine,
        first_pos: tuple[float, float],
        last_pos: tuple[float, float],
        *,
        expected: bool,
        scene_registry_with_intersecting_lines: SceneRegistry,
        position_validation_params: PositionValidationParams,
    ) -> None:
        """Test if the action is on opposite sides of the intersection."""
        action = make_cone_turn_action(
            crossing_positions=[first_pos, last_pos],
            crossing_directions=[Direction.LTR, Direction.RTL],
            direction=Direction.CW,
            entering_line=entering_line,
            frame_number=20,
            lines=["line-a", "line-b"],
        )
        result = is_opposite_sides_of_intersection(
            action,
            scene_registry_with_intersecting_lines,
            position_validation_params=position_validation_params,
        )
        assert result == expected

    @pytest.mark.parametrize(
        ("y_values", "ref_y", "expected"),
        [
            ([80, 85, 90], 100, True),  # all positions above cone
            ([100, 101, 102], 100, False),  # all at/below cone
            ([99, 100], 100, False),  # one position at cone
            ([], 100, True),  # empty trajectory = valid by default
        ],
    )
    def test_is_person_above_cone_behavior(
        self,
        person_with_pose: Person,
        y_values: list[float],
        ref_y: float,
        *,
        expected: bool,
    ) -> None:
        """Test person being above the cone in all trajectory frames."""
        # Minimal valid action
        action = ConeTurnedAction(
            entity_id="p1",
            cone_id="cone1",
            direction=Direction.CW,
            angle=TurningAngle.ANGLE_180,
            position=(100, 100),
            entering_line=None,
            meta={
                "start_frame": 10,
                "end_frame": 20,
            },
            time=FrameTime(
                frame_number=10,
                frame_time=timedelta(milliseconds=0.3333),
                time_since_start=timedelta(seconds=10 * 0.3333),
            ),
        )

        # Build synthetic DataFrame for trajectory with Y values
        df = pd.DataFrame({"x": [0] * len(y_values), "y": y_values})

        with patch.object(Person, "get_points_as_df", return_value=df):
            result = is_person_above_cone(
                exercise_id=ExerciseId.T_TEST,
                action=action,
                person=person_with_pose,
                reference_cone_position=np.array([50.0, ref_y]),
                tolerance_above_ref_cone=1,
            )

        assert result == expected

    @pytest.mark.parametrize(
        (
            "angle_image",
            "person_x",
            "person_y",
            "cone_y",
            "ref_cone_pos",
            "tolerance",
            "expected",
        ),
        [
            # : Angled line, person closer to reference cone
            (45.0, 90, 90, 100, (80, 100), 15.0, True),
            # : Negative angle, person within tolerance
            (-30.0, 95, 85, 100, (90, 100), 20.0, True),
        ],
    )
    def test_check_nearness_to_cone_various_angles(  # noqa: PLR0913
        self,
        angle_image: float,
        person_x: int,
        person_y: int,
        cone_y: int,
        ref_cone_pos: tuple[float, float],
        tolerance: float,
        *,
        expected: bool,
    ) -> None:
        """Test nearness check with various angles and positions."""
        validation_params = PositionValidationParams(
            reference_cone_position=np.array(ref_cone_pos),
            tolerance_px_away_cam=tolerance,
        )
        result = check_nearness_to_cone(
            angle_image=angle_image,
            position_validation_params=validation_params,
            x=person_x,
            projected_y_position=person_y,
            cone_y=cone_y,
        )

        assert result == expected

    def test_get_person_y_position_project_true(self) -> None:
        """Test that project=True uses crossing_positions directly.

        When project[crossing_idx] is True, the function should read y directly
        from action.meta['crossing_positions'][idx][1] and not call keypoint logic.
        """
        # Person with a bbox, frames 10..12 (values don't matter for project=True)
        person = make_person_with_boxes(
            frame_numbers=[10, 11, 12],
            xyxy_boxes=[
                [0.0, 0.0, 10.0, 20.0],  # bottom y = 20.0 (not used here)
                [5.0, 5.0, 15.0, 25.0],
                [6.0, 6.0, 16.0, 26.0],
            ],
        )

        # action meta: project=True at idx=1, so we use crossing_positions[1][1]
        action = type("Action", (), {})()
        action.frame_number = 11
        action.meta = {
            "project": [False, True, False],
            "crossing_positions": [(100.0, 111.0), (200.0, 222.0), (300.0, 333.0)],
            "start_frame": 10,
            "mid_frame": 11,
            "end_frame": 12,
            # person_position wont be used due to project=True
        }

        y = _get_person_y_position(person, action, crossing_idx=1)
        assert y == 222.0

    @pytest.mark.parametrize(
        ("crossing_idx", "expected_y"),
        [
            (0, 40.0),
            (1, 80.0),
            (2, 120.0),
        ],
    )
    def test_get_person_y_position_project_false(
        self, monkeypatch: pytest.MonkeyPatch, crossing_idx: int, expected_y: float
    ) -> None:
        """Test that project=False and person_position uses KPT_CENTER.

        When project[crossing_idx] is False and person_position uses KPT_CENTER,
        the function should call Person.to_point via keypoint conversion and
        return its y.
        """
        # Person with frames 10,20,30; bboxes present (not directly used in this branch)
        person = make_person_with_boxes(
            frame_numbers=[10, 20, 30],
            xyxy_boxes=[
                [0.0, 0.0, 10.0, 40.0],
                [0.0, 0.0, 20.0, 80.0],
                [0.0, 0.0, 30.0, 120.0],
            ],
        )

        # Monkeypatch the internal keypoint converter used by Person.to_point
        def fake_convert(
            method: ToPointMethod,  # noqa: ARG001
            pose: Pose,  # noqa: ARG001
            keypoints: np.ndarray,  # noqa: ARG001
        ) -> np.ndarray:
            # Return deterministic (x, y) so we can assert y
            return np.array([123.0, expected_y], dtype=float)

        monkeypatch.setattr(
            Person, "_convert_to_point_using_keypoints", staticmethod(fake_convert)
        )

        action = type("Action", (), {})()
        action.frame_number = 0  # not used directly for frame selection
        action.meta = {
            "project": [False, False, False],
            "person_position": [
                "kpt_center_left_ankle_right_ankle",
                "kpt_center_left_ankle_right_ankle",
                "kpt_center_left_ankle_right_ankle",
            ],
            "start_frame": 10,
            "mid_frame": 20,
            "end_frame": 30,
            "crossing_positions": [(0.0, 111.0), (0.0, 222.0), (0.0, 333.0)],
        }

        y = _get_person_y_position(person, action, crossing_idx=crossing_idx)
        assert y == expected_y

    def test_get_person_y_position_missing_frame_triggers_interpolation(
        self, monkeypatch: pytest.MonkeyPatch
    ) -> None:
        """Test missing frame interpolation.

        If the chosen frame_number is not in person.frame_numbers,
        _get_person_y_position should call interpolate_points_missing_frame()
        on the real Person and return its y.
        """
        # Person with frames only at 0 and 20, so frame 10 is "missing"
        person = make_person_with_boxes(
            frame_numbers=[0, 20],
            xyxy_boxes=[
                [0.0, 0.0, 10.0, 40.0],
                [0.0, 0.0, 20.0, 80.0],
            ],
        )

        # Fake convert is still needed to satisfy Person.to_point when KPT is used
        def fake_convert(
            method: ToPointMethod,  # noqa: ARG001
            pose: Pose,  # noqa: ARG001
            keypoints: np.ndarray,  # noqa: ARG001
        ) -> np.ndarray:
            # not used in this branch because interpolate is called instead of to_point
            return np.array([0.0, 0.0], dtype=float)

        monkeypatch.setattr(
            Person, "_convert_to_point_using_keypoints", staticmethod(fake_convert)
        )

        # Monkeypatch interpolate_points_missing_frame to return a controlled point
        expected_y = 55.0

        def fake_interpolate_points_missing_frame(
            self: Person,  # noqa: ARG001
            frame: int,  # noqa: ARG001
            **point_kwargs: dict[str, Any],  # noqa: ARG001
        ) -> np.ndarray:
            # The method will be either KPT_CENTER or KPT_NAME depending on meta
            return np.array([123.0, expected_y], dtype=float)

        monkeypatch.setattr(
            Person,
            "interpolate_points_missing_frame",
            fake_interpolate_points_missing_frame,
        )

        # crossing_idx=1 -> use "mid_frame" which we set to 10 (missing)
        action = type("Action", (), {})()
        action.frame_number = 0
        action.meta = {
            "project": [False, False, False],
            "person_position": [
                "kpt_center_left_ankle_right_ankle",  # idx=0
                "kpt_name_left_ankle",  # idx=1 -> single keypoint
                "kpt_center_left_knee_right_knee",  # idx=2
            ],
            "start_frame": 0,
            "mid_frame": 10,  # Missing from person.frame_numbers
            "end_frame": 20,
            "crossing_positions": [(0.0, 111.0), (0.0, 222.0), (0.0, 333.0)],
        }

        y = _get_person_y_position(person, action, crossing_idx=1)
        assert y == expected_y

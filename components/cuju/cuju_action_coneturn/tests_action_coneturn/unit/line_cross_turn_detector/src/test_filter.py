"""Unit tests for the filter module."""

from __future__ import annotations

from datetime import timed<PERSON><PERSON>
from typing import TYPE_CHECKING
from unittest.mock import MagicMock, patch

import numpy as np
import pandas as pd
import pytest
from cuju_action_coneturn.line_cross_turn_detector.src.constants import (
    FrameParamsConflictResolver,
    PositionValidationEntityParams,
    PositionValidationParams,
)
from cuju_action_coneturn.line_cross_turn_detector.src.filter import (
    check_move_turn_final_direction_cone,
    filter_actions_by_position,
    filter_and_order_crossings,
    filter_contained_turns,
    filter_first_direction,
    find_matching_pairs,
    get_person_motion_vector,
    group_conflicting_items,
    is_person_moving_to_cone,
    is_valid_all_crossing_positions,
    is_valid_cone_turn_vertical_entering,
    resolve_conflicting_overlapping_turns,
    resolve_line_crossing_conflicts,
    select_best_crossing_by_motion,
)
from cuju_data_pose.pose2d.keypoint_topology_base import CocoTopology
from cuju_data_pose.pose2d.pose_base import Pose
from cuju_data_scene.actions.cone_turn_action import (
    Cone<PERSON>rossing<PERSON><PERSON>,
    ConeTurnedAction,
    TurningAngle,
)
from cuju_data_scene.actions.line_crossed_action import LineCrossedAction
from cuju_data_scene.constants import ExerciseId
from cuju_data_scene.entities.cone import Cone
from cuju_data_scene.entities.direction import Direction
from cuju_data_scene.entities.line import LineEntity
from cuju_data_scene.entities.person import Person
from cuju_data_scene.entities.tracked_entity import ToPointMethod
from imgeta.plugin_base.imgeta_tree.frame_time import FrameTime

if TYPE_CHECKING:
    from cuju_data_scene.p_scene_registry import SceneRegistry


# ────────────────────────────────────────────────
# Helper to create ConeTurnedAction with duration
# ────────────────────────────────────────────────


def make_turn(
    start: int,
    end: int,
    direction: Direction,
    label: str = "",
    turn_id: str = "turn1",
) -> ConeTurnedAction:
    """Create a ConeTurnedAction with specified start, end, and direction."""
    return ConeTurnedAction(
        id=turn_id,
        entity_id="p1",
        cone_id="c1",
        direction=direction,
        angle=TurningAngle.ANGLE_180,
        position=(100.0, 100.0),
        entering_line=ConeCrossingLine.HORIZONTAL,
        meta={
            "start_frame": start,
            "end_frame": end,
            "crossing_positions": [(100, 100)],
            "crossing_directions": [Direction.RTL],
            "person_position": ["bbox_center_bottom"],
            "project": [False],
            "label": label,
        },
        time=FrameTime(
            frame_number=start,
            frame_time=timedelta(milliseconds=0.3333),
            time_since_start=timedelta(seconds=start * 0.3333),
        ),
    )


@pytest.mark.unittest
class TestFindMatchingPairs:
    """Test suite for find_matching_pairs function."""

    @pytest.mark.parametrize(
        ("cone_actions", "expected_pairs"),
        [
            pytest.param([], [], id="empty_list"),
            pytest.param(
                [make_turn(10, 20, Direction.CW, turn_id="a1")],
                [],
                id="single_action",
            ),
            pytest.param(
                [
                    make_turn(10, 20, Direction.CW, turn_id="a1"),
                    make_turn(15, 25, Direction.CW, turn_id="a2"),
                    make_turn(30, 40, Direction.CCW, turn_id="a3"),
                ],
                [("a1", "a2")],
                id="mixed_directions",
            ),
            pytest.param(
                [
                    make_turn(10, 20, Direction.CW, turn_id="a1"),
                    make_turn(15, 25, Direction.CW, turn_id="a2"),
                    make_turn(30, 40, Direction.CW, turn_id="a3"),
                ],
                [("a1", "a2"), ("a1", "a3"), ("a2", "a3")],
                id="all_matching",
            ),
        ],
    )
    def test_find_matching_pairs_conditions(
        self,
        cone_actions: list[ConeTurnedAction],
        expected_pairs: list[tuple[str, str]],
    ) -> None:
        """Test basic functionality and edge cases."""

        def same_direction(a1: ConeTurnedAction, a2: ConeTurnedAction) -> bool:
            return a1.direction == a2.direction

        result = find_matching_pairs(cone_actions, same_direction)
        result_ids = [(p[0].id, p[1].id) for p in result]

        assert result_ids == expected_pairs
        # Verify no duplicates and ordering (i < j)
        assert len(result_ids) == len(set(result_ids))
        for pair in result:
            assert cone_actions.index(pair[0]) < cone_actions.index(pair[1])


@pytest.mark.unittest
class TestCheckMoveTurnFinalDirectionCone:
    """Test suite for check_move_turn_final_direction_cone function."""

    @pytest.mark.parametrize(
        ("exercise_id", "action_direction", "closest_cone", "expected"),
        [
            (ExerciseId.NAVETTE, Direction.CW, "cw", True),  # Non T-Test always True
            (ExerciseId.WALL_BALL_WB, Direction.CCW, "ccw", True),
            (ExerciseId.T_TEST, Direction.CW, "cw", True),  # T-Test: direction matches
            (ExerciseId.T_TEST, Direction.CCW, "ccw", True),
            (ExerciseId.T_TEST, Direction.CW, "ccw", False),  # T-Test: mismatch
            (ExerciseId.T_TEST_WB, Direction.CCW, "cw", False),
        ],
    )
    def test_direction_validation_by_exercise(
        self,
        exercise_id: ExerciseId,
        action_direction: Direction,
        closest_cone: str,
        *,
        expected: bool,
    ) -> None:
        """Test direction validation: non-T-Test always passes, T-Test validates."""
        # Mock person with DataFrame - return real DataFrame for apply() operations
        person = MagicMock(spec=Person)
        # Create DataFrame with sample position data
        if closest_cone == "cw":
            # Closer to CW cone at (100, 50)
            df = pd.DataFrame({"x": [100], "y": [60]})
        else:
            # Closer to CCW cone at (100, 150)
            df = pd.DataFrame({"x": [100], "y": [140]})

        person.get_points_as_df.return_value = df

        action = ConeTurnedAction(
            entity_id="p1",
            cone_id="c1",
            direction=action_direction,
            angle=TurningAngle.ANGLE_180,
            position=(100, 100),
            time=FrameTime(
                frame_number=100,
                frame_time=timedelta(milliseconds=33),
                time_since_start=timedelta(seconds=3.3),
            ),
            entering_line=ConeCrossingLine.HORIZONTAL,
            meta={"end_frame": 110},
        )

        position_registry = PositionValidationEntityParams(
            person=person,
            cone=MagicMock(spec=Cone),
            center_horizontal_line_entity=MagicMock(spec=LineEntity),
        )

        position_validation = PositionValidationParams(
            cone_position_clockwise=np.array([100, 50]),
            cone_position_counter_clockwise=np.array([100, 150]),
        )

        result = check_move_turn_final_direction_cone(
            exercise_id=exercise_id,
            action=action,
            position_registry_params=position_registry,
            position_validation_params=position_validation,
        )

        assert result is expected


@pytest.mark.unittest
class TestIsPersonMovingToCone:
    """Test suite for is_person_moving_to_cone function."""

    @pytest.mark.parametrize(
        ("exercise_id", "motion_vec", "dir_vec", "threshold", "expected"),
        [
            # Non T-Test always returns True
            (ExerciseId.NAVETTE, np.array([10, 5]), np.array([1, 0]), 0.5, True),
            (ExerciseId.WALL_BALL_WB, np.array([0, 0]), np.array([1, 0]), 0.5, True),
            # T-Test: motion aligned with direction (high similarity)
            (ExerciseId.T_TEST, np.array([10, 0]), np.array([1, 0]), 0.8, True),
            (ExerciseId.T_TEST_WB, np.array([5, 5]), np.array([1, 1]), 0.8, True),
            # T-Test: motion opposite to direction (low similarity after adjustment)
            (ExerciseId.T_TEST, np.array([-10, 0]), np.array([1, 0]), 0.8, True),
            # T-Test: perpendicular motion (low similarity)
            (ExerciseId.T_TEST, np.array([0, 10]), np.array([1, 0]), 0.8, False),
            # T-Test: motion below threshold
            (ExerciseId.T_TEST_WB, np.array([1, 10]), np.array([1, 0]), 0.9, False),
        ],
    )
    def test_motion_validation_by_exercise(
        self,
        exercise_id: ExerciseId,
        motion_vec: np.ndarray,
        dir_vec: np.ndarray,
        threshold: float,
        *,
        expected: bool,
    ) -> None:
        """Test motion validation: non-T-Test passes, T-Test validates cosine."""
        # Mock cone
        cone = MagicMock(spec=Cone)
        cone.to_point.return_value = np.array([100, 100])

        # Mock person with motion vector
        person = MagicMock(spec=Person)

        action = ConeTurnedAction(
            entity_id="p1",
            cone_id="c1",
            direction=Direction.CW,
            angle=TurningAngle.ANGLE_180,
            position=(100, 100),
            time=FrameTime(
                frame_number=100,
                frame_time=timedelta(milliseconds=33),
                time_since_start=timedelta(seconds=3.3),
            ),
            entering_line=ConeCrossingLine.HORIZONTAL,
            meta={"end_frame": 110},
        )

        position_registry = PositionValidationEntityParams(
            person=person,
            cone=cone,
            center_horizontal_line_entity=MagicMock(spec=LineEntity),
        )

        # Reference cone position to calculate direction vector
        ref_cone_pos = np.array([100, 100]) + dir_vec * 50

        position_validation = PositionValidationParams(
            reference_cone_position=ref_cone_pos,
            person_motion_cosine_similarity_threshold=threshold,
        )

        # Mock get_person_motion_vector to return our test motion vector
        with patch(
            "cuju_action_coneturn.line_cross_turn_detector.src.filter.get_person_motion_vector",
            return_value=motion_vec.copy(),
        ):
            result = is_person_moving_to_cone(
                exercise_id=exercise_id,
                action=action,
                position_registry_params=position_registry,
                position_validation_params=position_validation,
            )

        assert result == expected


@pytest.mark.unittest
class TestIsValidConeTurnVerticalEntering:
    """Test suite for is_valid_cone_turn_vertical_entering function."""

    @pytest.mark.parametrize(
        (
            "exercise_id",
            "validate_final_motion",
            "filter_turns_above",
            "validate_turns_to_ref",
            "final_motion_result",
            "above_cone_result",
            "moving_to_cone_result",
            "expected",
        ),
        [
            # All validations pass
            (ExerciseId.T_TEST, True, True, True, True, True, True, True),
            # Final motion validation fails
            (ExerciseId.T_TEST, True, True, True, False, True, True, False),
            # Above cone validation fails
            (ExerciseId.T_TEST, True, True, True, True, False, True, False),
            # Moving to cone validation fails
            (ExerciseId.T_TEST, True, True, True, True, True, False, False),
            # No validations enabled - always pass
            (ExerciseId.T_TEST, False, False, False, False, False, False, True),
            # Only final motion enabled and passes
            (ExerciseId.T_TEST, True, False, False, True, False, False, True),
            # Only above cone enabled and passes
            (ExerciseId.T_TEST, False, True, False, False, True, False, True),
            # Only moving to cone enabled and passes
            (ExerciseId.T_TEST, False, False, True, False, False, True, True),
            # T_TEST_WB exercise
            (ExerciseId.T_TEST_WB, True, False, False, False, False, False, False),
        ],
    )
    def test_validation_combinations(  # noqa: PLR0913
        self,
        exercise_id: ExerciseId,
        *,
        validate_final_motion: bool,
        filter_turns_above: bool,
        validate_turns_to_ref: bool,
        final_motion_result: bool,
        above_cone_result: bool,
        moving_to_cone_result: bool,
        expected: bool,
    ) -> None:
        """Test various combinations of validation flags and results."""
        # Mock entities
        person = MagicMock(spec=Person)
        cone = MagicMock(spec=Cone)

        action = ConeTurnedAction(
            entity_id="p1",
            cone_id="c1",
            direction=Direction.CW,
            angle=TurningAngle.ANGLE_180,
            position=(100, 100),
            time=FrameTime(
                frame_number=100,
                frame_time=timedelta(milliseconds=33),
                time_since_start=timedelta(seconds=3.3),
            ),
            entering_line=ConeCrossingLine.VERTICAL,
            meta={"end_frame": 110},
        )

        position_registry = PositionValidationEntityParams(
            person=person,
            cone=cone,
            center_horizontal_line_entity=MagicMock(spec=LineEntity),
        )

        position_validation = PositionValidationParams(
            validate_final_motion=validate_final_motion,
            filter_turns_above_ref_cone=filter_turns_above,
            validate_turns_to_ref_cone=validate_turns_to_ref,
            reference_cone_position=np.array([100, 100]),
        )

        # Mock the validation functions
        with (
            patch(
                "cuju_action_coneturn.line_cross_turn_detector.src.filter.check_move_turn_final_direction_cone",
                return_value=final_motion_result,
            ),
            patch(
                "cuju_action_coneturn.line_cross_turn_detector.src.filter.is_person_above_cone",
                return_value=above_cone_result,
            ),
            patch(
                "cuju_action_coneturn.line_cross_turn_detector.src.filter.is_person_moving_to_cone",
                return_value=moving_to_cone_result,
            ),
        ):
            result = is_valid_cone_turn_vertical_entering(
                action=action,
                position_registry_params=position_registry,
                position_validation_params=position_validation,
                exercise_id=exercise_id,
            )

        assert result == expected


@pytest.mark.unittest
class TestSelectBestCrossingByMotion:
    """Test suite for select_best_crossing_by_motion function."""

    def make_action(self, frame: int, line_name: str = "line1") -> MagicMock:
        """Create a mock LineCrossedAction for testing."""
        action = MagicMock()
        action.frame_number = frame
        action.line_name = line_name
        action.observed_point = "KPT_CENTER_ankle"
        action.crossing_direction = "down"
        return action

    def test_selects_highest_similarity_action(self) -> None:
        """Should select action with highest cosine similarity to motion vector."""
        action1 = self.make_action(100)
        action2 = self.make_action(105)
        group = [action1, action2]

        person = MagicMock()
        line_entity = MagicMock()
        line_entity.get_frame_index.return_value = 0
        line_entity.lines = [MagicMock(), MagicMock()]
        # Two different direction vectors
        line_entity.lines[0].get_crossing_direction_vector.side_effect = [
            np.array([1, 0]),  # for action1
            np.array([0, 1]),  # for action2
        ]

        # Patch motion vector return values to control scoring
        with patch(
            "cuju_action_coneturn.line_cross_turn_detector.src.filter.get_person_motion_vector"
        ) as mock_motion_vec:
            mock_motion_vec.side_effect = [
                np.array([0.9, 0.1]),  # Similar to [1, 0]
                np.array([0.1, 0.9]),  # Similar to [0, 1]
            ]

            selected = select_best_crossing_by_motion(
                group=group,
                entity=person,
                line_entity=line_entity,
                frame_offset=3,
                method=ToPointMethod.KPT_CENTER,
            )

        # First motion vector aligns better with first line direction
        assert selected == action1

    def test_get_motion_vector_from_pose_coords(self, person_with_pose: Person) -> None:
        """Test that motion vector is correctly calculated from pose coordinates."""
        person = person_with_pose

        vec = get_person_motion_vector(
            person=person,
            method=ToPointMethod.KPT_NAME,
            keypoints=["left_ankle"],
            frame=2,
            frame_offset=3,
        )

        assert isinstance(vec, np.ndarray)
        assert np.allclose(vec, [6.0, 8.0])

    def test_motion_vector_fails_if_keypoint_name_invalid(self) -> None:
        """Test that KeyError is raised if keypoint name is invalid."""
        pose = Pose(
            coords=np.zeros((17, 2)),  # valid shape
            topology=CocoTopology.default(),
        )

        person = Person(
            entity_id="p2",
            poses=[pose, pose],
            frame_time=[
                FrameTime(
                    frame_number=0,
                    frame_time=timedelta(seconds=0),
                    time_since_start=timedelta(seconds=0),
                ),
                FrameTime(
                    frame_number=3,
                    frame_time=timedelta(seconds=1),
                    time_since_start=timedelta(seconds=1),
                ),
            ],
            conversion_keypoints=["left_ankle"],
        )

        # Wrong keypoint name
        with pytest.raises(KeyError):
            get_person_motion_vector(
                person,
                method=ToPointMethod.KPT_NAME,
                keypoints=["non_existent_kp"],
                frame=3,
            )


@pytest.mark.unittest
class TestFilterModule:
    """Test suite for the filter module."""

    # -------------------------- MOCK HELPERS -------------------------- #

    def make_action(
        self,
        frame: int,
        line_id: str,
        person_id: str = "p1",
        line_name: str | None = None,
    ) -> LineCrossedAction:
        """Create a mock LineCrossedAction for testing."""
        time = MagicMock(spec=FrameTime)
        time.frame_number = frame
        action = MagicMock(spec=LineCrossedAction)
        action.time = time
        action.entity_id = person_id
        action.line_id = line_id
        action.line_name = line_name or line_id
        action.meta = {"frame": frame}
        action.crossing_direction = "down"
        action.observed_point = "KPT_CENTER_ankle"
        action.is_opposite_direction_action = (
            lambda other, frame_buffer=1: abs(
                action.time.frame_number - other.time.frame_number
            )
            <= frame_buffer
        )
        return action

    def make_line_entity(self, name: str) -> MagicMock:
        """Create a mock LineEntity for testing."""
        entity = MagicMock(spec=LineEntity)
        entity.name = name
        return entity

    def make_person(self, entity_id: str) -> MagicMock:
        """Create a mock Person for testing."""
        person = MagicMock()
        person.id = entity_id
        return person

    # ------------------ resolve_line_crossing_conflicts ------------------ #

    def test_find_opposite_groups_mocked(self) -> None:
        """Test finding opposite action groups."""
        (a1, a2, a3) = (
            MagicMock(spec=LineCrossedAction, time=MagicMock(frame_number=100)),
            MagicMock(spec=LineCrossedAction, time=MagicMock(frame_number=102)),
            MagicMock(spec=LineCrossedAction, time=MagicMock(frame_number=103)),
        )
        a1.is_opposite_direction_action = lambda other, frame_buffer=1: (
            other is a2 and a2.time.frame_number - a1.time.frame_number <= frame_buffer
        )
        a2.is_opposite_direction_action = lambda other, frame_buffer=1: (
            other is a1 and a1.time.frame_number - a2.time.frame_number <= frame_buffer
        )
        a3.is_opposite_direction_action = lambda other, frame_buffer=1: False  # noqa: ARG005

        result_unique, result_groups = group_conflicting_items(
            [a1, a2, a3],
            is_conflicting_pair=lambda a1, a2: a1.is_opposite_direction_action(
                a2, frame_buffer=3
            ),
        )
        assert len(result_groups) == 1
        assert set(result_groups[0]) == {a1, a2}
        assert result_unique == [a3]

    @pytest.mark.parametrize(
        ("frames", "expected_count"),
        [
            ((100, 103), 1),  # within frame_buffer
            ((100, 120), 2),  # outside frame_buffer
        ],
    )
    def test_conflict_resolution_frame_buffer(
        self,
        monkeypatch: pytest.FixtureRequest,
        frames: tuple[int, int],
        expected_count: int,
    ) -> None:
        """Should retain one or both depending on frame_buffer."""
        monkeypatch.setattr(
            "cuju_action_coneturn.line_cross_turn_detector.src.filter.select_best_crossing_by_motion",
            lambda *args, **kwargs: kwargs["group"][0],  # noqa: ARG005
        )
        a1 = self.make_action(frames[0], "line1")
        a2 = self.make_action(frames[1], "line2")

        person = self.make_person("p1")
        line_entities = [self.make_line_entity("line1"), self.make_line_entity("line2")]

        resolved = resolve_line_crossing_conflicts(
            actions=[a1, a2],
            track_entities=[person],
            line_entities=line_entities,
            method=ToPointMethod.BBOX_CENTER,
            frame_params=FrameParamsConflictResolver(
                frame_buffer=5,
                frame_offset_person_motion=2,
            ),
        )
        assert len(resolved) == expected_count

    # ------------------ filter_and_order_crossings: positive cases ------------------ #

    @pytest.mark.parametrize(
        ("input_sets", "expected_group_size", "expected_len"),
        [
            # Two full turns
            (
                [
                    [
                        MagicMock(
                            spec=LineCrossedAction,
                            time=MagicMock(frame_number=100),
                            line_id="L1",
                            project=True,
                        ),
                        MagicMock(
                            spec=LineCrossedAction,
                            time=MagicMock(frame_number=200),
                            line_id="L1",
                            project=True,
                        ),
                    ],
                    [
                        MagicMock(
                            spec=LineCrossedAction,
                            time=MagicMock(frame_number=110),
                            line_id="L2",
                            project=True,
                        ),
                        MagicMock(
                            spec=LineCrossedAction,
                            time=MagicMock(frame_number=215),
                            line_id="L2",
                            project=True,
                        ),
                    ],
                    [
                        MagicMock(
                            spec=LineCrossedAction,
                            time=MagicMock(frame_number=115),
                            line_id="L3",
                            project=True,
                        ),
                        MagicMock(
                            spec=LineCrossedAction,
                            time=MagicMock(frame_number=250),
                            line_id="L3",
                            project=True,
                        ),
                    ],
                ],
                3,
                2,
            ),
            # Two half turns
            (
                [
                    [
                        MagicMock(
                            spec=LineCrossedAction,
                            time=MagicMock(frame_number=100),
                            line_id="L1",
                            project=True,
                        ),
                        MagicMock(
                            spec=LineCrossedAction,
                            time=MagicMock(frame_number=300),
                            line_id="L1",
                            project=True,
                        ),
                    ],
                    [
                        MagicMock(
                            spec=LineCrossedAction,
                            time=MagicMock(frame_number=120),
                            line_id="L2",
                            project=True,
                        ),
                        MagicMock(
                            spec=LineCrossedAction,
                            time=MagicMock(frame_number=310),
                            line_id="L2",
                            project=True,
                        ),
                    ],
                ],
                2,
                2,
            ),
        ],
    )
    def test_valid_turn_detections(
        self,
        input_sets: list[list[LineCrossedAction]],
        expected_group_size: int,
        expected_len: int,
    ) -> None:
        """Test valid turn detections."""
        result = filter_and_order_crossings(input_sets)
        assert len(result) == expected_len
        for group in result:
            assert len(group) == expected_group_size
            frame_numbers = [a.time.frame_number for a in group]
            assert sorted(frame_numbers) == frame_numbers
            if expected_group_size == 3:
                assert frame_numbers[-1] - frame_numbers[0] <= 60

    # ------------------ filter_and_order_crossings: negative cases ------------------ #

    @pytest.mark.parametrize(
        "input_lists",
        [
            [
                [MagicMock(time=MagicMock(frame_number=150), line_id="L1")],
                [MagicMock(time=MagicMock(frame_number=140), line_id="L2")],
                [MagicMock(time=MagicMock(frame_number=160), line_id="L3")],
            ],
            [
                [MagicMock(time=MagicMock(frame_number=100), line_id="L1")],
                [MagicMock(time=MagicMock(frame_number=110), line_id="L2")],
                [MagicMock(time=MagicMock(frame_number=200), line_id="L3")],
            ],
        ],
    )
    def test_invalid_turn_sequences(
        self, input_lists: list[list[LineCrossedAction]]
    ) -> None:
        """Test that invalid turn sequences are filtered out."""
        assert filter_and_order_crossings(input_lists) == []

    def test_empty_input_lists(self) -> None:
        """Test that empty input lists return an empty result."""
        with pytest.raises(IndexError):
            filter_and_order_crossings([[], [], []])

    # ------------------ filter_contained_turns ------------------ #

    @pytest.mark.parametrize("test_case_name", ["nested", "with_none", "equal"])
    def test_filter_contained_turns(
        self, filter_turns_test_cases: dict, test_case_name: str
    ) -> None:
        """Test filtering of contained turns."""
        test_case = filter_turns_test_cases[test_case_name]
        input_turns = test_case["turns"]
        expected_ids = test_case["expected_ids"]

        result = filter_contained_turns(input_turns)
        assert len(result) == len(expected_ids)

        result_ids = [
            f"{turn.cone_id}_{turn.meta['start_frame']}_{turn.meta['end_frame']}"
            for turn in result
        ]

        for expected_id in expected_ids:
            assert expected_id in result_ids
        for result_id in result_ids:
            assert result_id in expected_ids

        start_frames = [turn.meta["start_frame"] for turn in result]
        assert start_frames == sorted(start_frames)

        for i, t1 in enumerate(result):
            i1 = (t1.meta["start_frame"], t1.meta["end_frame"])
            for j, t2 in enumerate(result):
                if i == j:
                    continue
                i2 = (t2.meta["start_frame"], t2.meta["end_frame"])
                if i2[0] <= i1[0] and i1[1] <= i2[1] and i1 != i2:
                    pytest.fail(f"Turn {result_ids[i]} contained in {result_ids[j]}")

    @pytest.mark.parametrize(
        ("test_case_fixture", "expected_length", "expected_group_size"),
        [
            ("full_turn_test_case", 6, 3),
            ("half_turn_test_case", 3, 2),
        ],
    )
    def test_sequence_generation_from_fixture(
        self,
        test_case_fixture: str,
        expected_length: int,
        expected_group_size: int,
        request: pytest.FixtureRequest,
    ) -> None:
        """Test sequence generation from fixture data."""
        test_case = request.getfixturevalue(test_case_fixture)
        input_lists, expected_patterns = test_case

        result = filter_and_order_crossings(input_lists)
        assert len(result) == expected_length

        for sequence in result:
            assert len(sequence) == expected_group_size
            frame_numbers = [a.time.frame_number for a in sequence]
            assert sorted(frame_numbers) == frame_numbers
            if expected_group_size == 3:
                assert frame_numbers[-1] - frame_numbers[0] <= 60

        result_patterns = [[a.line_id for a in seq] for seq in result]
        for pattern in expected_patterns:
            assert pattern in result_patterns

    @pytest.mark.parametrize(
        ("ref_cone_pos", "crossing_dir", "should_pass"),
        [
            (np.array([50.0, 100.0]), Direction.LTR, True),  # Rightward → LTR
            (np.array([150.0, 100.0]), Direction.RTL, False),  # Leftward → RTL
        ],
    )
    def test_filter_first_direction_matches_vector(  # noqa: PLR0913
        self,
        test_cone: Cone,
        test_person: Person,
        test_registry: SceneRegistry,
        ref_cone_pos: np.ndarray,
        crossing_dir: Direction,
        n_frames: int,
        *,
        should_pass: bool,
    ) -> None:
        """Test filtering first direction matches crossing vector."""
        action = ConeTurnedAction(
            entity_id="p1",
            cone_id="c1",
            direction=Direction.CW,
            angle=TurningAngle.ANGLE_180,
            position=(100, 100),
            time=FrameTime(
                frame_number=n_frames - 2 + 100,
                frame_time=timedelta(milliseconds=0.3333),
                time_since_start=timedelta(seconds=n_frames - 2 + 100 * 0.3333),
            ),
            entering_line=ConeCrossingLine.HORIZONTAL,
            meta={
                "lines": ["line-1"],
                "crossing_directions": [crossing_dir],
            },
        )

        position_registry = PositionValidationEntityParams(
            person=test_person,
            cone=test_cone,
            center_horizontal_line_entity=MagicMock(spec=LineEntity),
        )

        position_validation = PositionValidationParams(
            reference_cone_position=ref_cone_pos, validate_first_direction=True
        )

        result = filter_first_direction(
            action=action,
            scene_registry=test_registry,
            position_registry_params=position_registry,
            position_validation_params=position_validation,
        )

        assert result is should_pass

    @patch(
        "cuju_action_coneturn.line_cross_turn_detector.src.filter.is_first_action_valid"
    )
    @patch(
        "cuju_action_coneturn.line_cross_turn_detector.src.filter.is_next_position_valid"
    )
    @pytest.mark.parametrize(
        ("first", "second", "third", "validate_2nd", "validate_3rd", "expected"),
        [
            (True, True, True, True, True, True),
            (True, True, False, True, True, False),
            (True, True, False, True, False, True),  # third check skipped
            (True, False, True, True, True, False),  # second failed
            (False, True, True, True, True, False),  # first failed
            (True, True, True, False, False, True),  # only first checked
        ],
    )
    def test_is_valid_all_crossing_positions_combinations(  # noqa: PLR0913
        self,
        mock_next_valid: MagicMock,
        mock_first_valid: MagicMock,
        standard_cone_turn_action: ConeTurnedAction,
        standard_validation_entities: PositionValidationEntityParams,
        *,
        first: bool,
        second: bool,
        third: bool,
        validate_2nd: bool,
        validate_3rd: bool,
        expected: bool,
    ) -> None:
        """Test all combinations of first, second, and third position validity."""
        mock_first_valid.return_value = first
        mock_next_valid.side_effect = [second, third]  # second then third call

        validation_params = PositionValidationParams(
            validate_second_position=validate_2nd,
            validate_third_position=validate_3rd,
            tolerance_px_below_cam=10.0,
            tolerance_px_away_cam=10.0,
        )

        result = is_valid_all_crossing_positions(
            action=standard_cone_turn_action,
            position_registry_params=standard_validation_entities,
            position_validation_params=validation_params,
        )
        assert result == expected

    @patch(
        "cuju_action_coneturn.line_cross_turn_detector.src.filter.filter_first_direction"
    )
    @patch(
        "cuju_action_coneturn.line_cross_turn_detector.src.filter.is_valid_all_crossing_positions"
    )
    @patch(
        "cuju_action_coneturn.line_cross_turn_detector.src.filter.is_valid_cone_turn_vertical_entering"
    )
    @patch(
        "cuju_action_coneturn.line_cross_turn_detector.src.filter.is_opposite_sides_of_intersection"
    )
    @pytest.mark.parametrize(
        (
            "first_valid",
            "crossings_valid",
            "vertical_entering_valid",
            "intersection_valid",
            "angle",
            "validate_first",
            "validate_intersection",
            "expected_len",
        ),
        [
            (True, True, True, True, TurningAngle.ANGLE_180, True, True, 1),
            (
                False,
                True,
                True,
                True,
                TurningAngle.ANGLE_180,
                True,
                True,
                0,
            ),  # fails first
            (
                True,
                False,
                True,
                True,
                TurningAngle.ANGLE_180,
                True,
                True,
                0,
            ),  # fails crossings
            (
                True,
                True,
                False,
                True,
                TurningAngle.ANGLE_180,
                True,
                True,
                0,
            ),  # fails position
            (
                True,
                True,
                True,
                False,
                TurningAngle.ANGLE_180,
                True,
                True,
                0,
            ),  # fails intersection
            (
                True,
                True,
                True,
                True,
                TurningAngle.ANGLE_90,
                True,
                True,
                1,
            ),  # skip intersection
            (
                True,
                True,
                True,
                True,
                TurningAngle.ANGLE_180,
                False,
                True,
                1,
            ),  # skip first
            (
                True,
                True,
                True,
                True,
                TurningAngle.ANGLE_180,
                True,
                False,
                1,
            ),  # skip intersection
        ],
    )
    def test_filter_actions_by_position_conditions(  # noqa: PLR0913
        self,
        mock_intersection: MagicMock,
        mock_position: MagicMock,
        mock_crossings: MagicMock,
        mock_first: MagicMock,
        test_registry: SceneRegistry,
        standard_validation_entities: PositionValidationEntityParams,
        position_validation_params: PositionValidationParams,
        *,
        first_valid: bool,
        crossings_valid: bool,
        vertical_entering_valid: bool,
        intersection_valid: bool,
        angle: TurningAngle,
        validate_first: bool,
        validate_intersection: bool,
        expected_len: int,
    ) -> None:
        """Test conditional combinations for filtering cone turn actions."""
        action = ConeTurnedAction(
            entity_id="p1",
            cone_id="c1",
            direction=Direction.CW,
            angle=angle,
            entering_line=ConeCrossingLine.VERTICAL,
            position=(100, 100),
            meta={
                "lines": ["line1_vertical", "line2_vertical", "line3_vertical"],
                "crossing_directions": [Direction.RTL, Direction.RTL, Direction.LTR],
                "crossing_positions": [
                    np.array([90, 100]),
                    np.array([100, 100]),
                    np.array([110, 100]),
                ],
                "person_position": ["bbox_center_bottom"] * 3,
                "project": [False] * 3,
            },
            time=FrameTime(
                frame_number=100,
                frame_time=timedelta(milliseconds=0.3333),
                time_since_start=timedelta(seconds=100 * 0.3333),
            ),
        )

        position_validation_params.validate_first_direction = validate_first
        position_validation_params.validate_intersection = validate_intersection

        mock_first.return_value = first_valid
        mock_crossings.return_value = crossings_valid
        mock_position.return_value = vertical_entering_valid
        mock_intersection.return_value = intersection_valid

        result = filter_actions_by_position(
            actions=[action],
            scene_registry=test_registry,
            position_registry_params=standard_validation_entities,
            position_validation_params=position_validation_params,
            exercise_id=ExerciseId.T_TEST,
        )

        assert len(result) == expected_len
        if expected_len == 1:
            assert result[0] == action

    @pytest.mark.parametrize(
        ("actions", "expected"),
        [
            pytest.param(
                # Overlap but SAME direction -> NOT conflicting -> keep both
                [
                    make_turn(10, 20, Direction.CW, turn_id="a1"),
                    make_turn(15, 25, Direction.CW, turn_id="a2"),
                ],
                [
                    make_turn(10, 20, Direction.CW, turn_id="a1"),
                    make_turn(15, 25, Direction.CW, turn_id="a2"),
                ],
                id="overlap_same_direction_kept",
            ),
            pytest.param(
                [
                    make_turn(10, 20, Direction.CW, turn_id="b1"),
                    make_turn(20, 30, Direction.CCW, turn_id="b2"),
                ],
                [
                    make_turn(10, 20, Direction.CW, turn_id="b1"),
                    make_turn(20, 30, Direction.CCW, turn_id="b2"),
                ],
                id="boundary_touch_opposite_directions_conflict_group_but_both_unique_after_resolution",
            ),
            pytest.param(
                [
                    make_turn(5, 25, Direction.CW, turn_id="c1"),
                    make_turn(5, 25, Direction.CCW, turn_id="c2"),
                ],
                [
                    make_turn(5, 25, Direction.CW, turn_id="c1"),
                    make_turn(5, 25, Direction.CCW, turn_id="c2"),
                ],
                id="identical_intervals_opposite_keep_both",
            ),
            pytest.param(
                [
                    make_turn(0, 40, Direction.CW, turn_id="d1"),
                    make_turn(2, 38, Direction.CCW, turn_id="d2"),  # 36f
                    make_turn(4, 36, Direction.CCW, turn_id="d3"),  # 32f
                ],
                [
                    make_turn(0, 40, Direction.CW, turn_id="d1"),
                    make_turn(2, 38, Direction.CCW, turn_id="d2"),
                    make_turn(4, 36, Direction.CCW, turn_id="d3"),
                ],
                id="multi_conflict_keep_within_default_tolerance",
            ),
            pytest.param(
                [
                    make_turn(0, 20, Direction.CW, turn_id="e1"),
                    make_turn(5, 18, Direction.CCW, turn_id="e2"),
                    make_turn(30, 50, Direction.CCW, turn_id="e3"),
                    make_turn(35, 49, Direction.CW, turn_id="e4"),
                    make_turn(60, 70, Direction.CW, turn_id="e5"),
                ],
                [
                    make_turn(0, 20, Direction.CW, turn_id="e1"),
                    make_turn(30, 50, Direction.CCW, turn_id="e3"),
                    make_turn(60, 70, Direction.CW, turn_id="e5"),
                ],
                id="two_conflict_groups_plus_unique",
            ),
            pytest.param(
                [],
                [],
                id="empty_input",
            ),
        ],
    )
    def test_resolve_conflicting_overlapping_turns_more_cases(
        self,
        actions: list[ConeTurnedAction],
        expected: list[ConeTurnedAction],
    ) -> None:
        """Additional coverage for resolve_conflicting_overlapping_turns."""
        result = resolve_conflicting_overlapping_turns(actions)

        def by_key(x: ConeTurnedAction) -> tuple[int, int, Direction]:
            return (x.meta["start_frame"], x.meta["end_frame"], x.direction)

        assert sorted(result, key=by_key) == sorted(expected, key=by_key)

    @pytest.mark.parametrize(
        ("tolerance", "expected_ids"),
        [
            (0.05, ["f1"]),
            (0.15, ["f1", "f2"]),
            (0.25, ["f1", "f2", "f3"]),
        ],
    )
    def test_resolve_conflicting_overlapping_turns_tolerance(
        self, tolerance: float, expected_ids: list[str]
    ) -> None:
        """Test overlapping turn resolution with varying tolerance levels.

        - Best = f1: 0..40 (duration 40)
        - f2: 2..38  (duration 36)  -> 10% short
        - f3: 5..35  (duration 30)  -> 25% short
        All opposite direction and overlapping -> single conflict group.
        """
        actions = [
            make_turn(0, 40, Direction.CW, turn_id="f1"),
            make_turn(2, 38, Direction.CCW, turn_id="f2"),
            make_turn(5, 35, Direction.CCW, turn_id="f3"),
        ]
        result = resolve_conflicting_overlapping_turns(
            actions, longest_tolerance_pt=tolerance
        )
        got_ids = sorted([t.id for t in result])
        assert got_ids == sorted(expected_ids)

"""Test ConeLineGenerator class."""

from collections.abc import Iterable
from unittest.mock import MagicMock, patch

import pytest
from cuju_action_coneturn.line_cross_turn_detector.src.cone_line_cross_detector import (
    ConeLineCrossDetector,
    ConeLineCrossDetectorConfig,
    get_line_name,
)
from cuju_action_coneturn.line_cross_turn_detector.src.constants import (
    ConeCrossingLine,
    LineCreationParams,
    ValidationMode,
)
from cuju_action_coneturn.line_cross_turn_detector.src.perspective_handler import (
    AdvancedPerspectiveHandler,
    BasicPerspectiveHandler,
    PerspectiveHandler,
)
from cuju_data_scene.actions.filter import SceneActionFilter
from cuju_data_scene.actions.line_crossed_action import LineCrossedAction
from cuju_data_scene.constants import EntityRole
from cuju_data_scene.entities.cone import Cone
from cuju_data_scene.entities.line import Line, LineEntity
from cuju_data_scene.entities.person import Person
from cuju_data_scene.p_scene_registry import MatchingCrite<PERSON>, SceneRegistry
from imgeta.plugin_base.imgeta_tree.frame_time import FrameTime


@pytest.mark.unittest
class TestConeLineGenerator:
    """Test cases for ConeLineGenerator class."""

    def test_perspective_handler_uninitialized_raises(
        self, cone_line_detector: ConeLineCrossDetector
    ) -> None:
        """Test that perspective_handler raises an error if not initialized."""
        with pytest.raises(ValueError, match="Perspective handler is not initialized"):
            _ = cone_line_detector.perspective_handler

    @pytest.mark.parametrize(
        ("registry_return", "expected"),
        [
            (
                (MagicMock(spec=LineEntity), MagicMock(spec=LineEntity)),
                AdvancedPerspectiveHandler,
            ),
            ((None, None), BasicPerspectiveHandler),
        ],
    )
    def test_perspective_handler_initialized(
        self,
        cone_line_detector: ConeLineCrossDetector,
        registry_return: Iterable,
        expected: PerspectiveHandler,
    ) -> None:
        """Test that perspective_handler is initialized correctly."""
        registry = MagicMock(spec=SceneRegistry)
        registry.retrieve.side_effect = registry_return
        handler = cone_line_detector._initialize_perspective_handler(registry)  # noqa: SLF001
        if not isinstance(handler, expected):
            pytest.fail(f"Expected {expected} but got {type(handler)}")

    def test_upsert_bidirectional_line_entity(self) -> None:
        """Test that bidirectional line entity is upserted correctly."""
        registry = MagicMock(spec=SceneRegistry)
        cone = MagicMock()
        cone.name = "testcone"
        cone.role = EntityRole.CONE_1
        cone.frame_time = MagicMock(spec=FrameTime)
        endpoint = MagicMock()
        method = "kpt"
        direction = ConeCrossingLine.HORIZONTAL

        cone_line_cross_detector = ConeLineCrossDetector(
            cone=cone,
            config=ConeLineCrossDetectorConfig(
                frame_shape=(
                    720,
                    1280,
                )
            ),
        )
        with patch.object(
            Line,
            "extend_to_edge",
            return_value=MagicMock(spec=Line),
        ):
            line = cone_line_cross_detector._create_line(  # noqa: SLF001
                cone_at=cone,
                endpoint=endpoint,
                frame_shape=None,
            )
            cone_line_cross_detector._upsert_bidirectional_line_entity(  # noqa: SLF001
                scene_registry=registry,
                line=line,
                cone_at=cone,
                line_name=get_line_name(
                    cone_name=cone.name,
                    direction=direction.value,
                    method=method,
                ),
            )

        registry.upsert_entity.assert_called_once()
        call_args = registry.upsert_entity.call_args[0][0]
        if not isinstance(call_args, LineEntity):
            pytest.fail("Expected a LineEntity to be upserted")

        if direction.value not in call_args.name:
            pytest.fail("Expected direction in line entity name")

    def test_transform_cone_to_lines(
        self,
        test_cone: Cone,
        detector_config: ConeLineCrossDetectorConfig,
        line_creation_params: LineCreationParams,
    ) -> None:
        """Test the transformation of a cone to lines."""
        detector = ConeLineCrossDetector(test_cone, config=detector_config)
        scene = SceneRegistry()
        out_scene = SceneRegistry()

        lines = detector._transform_cone_to_lines(  # noqa: SLF001
            registry=scene,
            output_registry=out_scene,
            params=line_creation_params,
            validation_mode=ValidationMode.LOOSE,
        )

        assert isinstance(lines, list)
        assert all(isinstance(line, LineEntity) for line in lines)
        assert all(line.lines for line in lines)

    def test_get_line_cross_actions(
        self,
        test_cone: Cone,
        test_person: Person,
        detector_config: ConeLineCrossDetectorConfig,
        line_creation_params: LineCreationParams,
    ) -> None:
        """Test the retrieval of line cross actions."""
        detector = ConeLineCrossDetector(test_cone, config=detector_config)
        scene = SceneRegistry()
        scene.upsert_entity(test_cone, matching_criteria=MatchingCriteria.ID)

        out_scene = detector.get_line_cross_actions(
            person_entities=[test_person],
            scene_registry=scene,
            params=line_creation_params,
            validation_mode=ValidationMode.LOOSE,
        )

        assert isinstance(out_scene, SceneRegistry)
        actions = out_scene.retrieve_actions(
            SceneActionFilter(action_type=LineCrossedAction)
        )
        assert isinstance(actions, list)
        if actions:
            assert all(isinstance(a, LineCrossedAction) for a in actions)

    def test_get_cone_positions(
        self,
        test_cone: Cone,
        detector_config: ConeLineCrossDetectorConfig,
        line_creation_params: LineCreationParams,
    ) -> None:
        """Test the retrieval of cone positions."""
        # Mock the SceneRegistry to avoid dependency on external state
        detector = ConeLineCrossDetector(test_cone, config=detector_config)
        scene = SceneRegistry()
        out_scene = SceneRegistry()

        detector._line_entities = detector._transform_cone_to_lines(  # noqa: SLF001
            registry=scene,
            output_registry=out_scene,
            params=line_creation_params,
            validation_mode=ValidationMode.LOOSE,
        )

        result = detector.get_cone_positions()
        assert isinstance(result, dict)
        assert any(k for k in result)

    def test_get_horizontal_center_line_entity(
        self,
        test_cone: Cone,
        detector_config: ConeLineCrossDetectorConfig,
        line_creation_params: LineCreationParams,
    ) -> None:
        """Test retrieval of horizontal center line entity."""
        detector = ConeLineCrossDetector(test_cone, config=detector_config)
        scene = SceneRegistry()
        out_scene = SceneRegistry()

        detector._line_entities = detector._transform_cone_to_lines(  # noqa: SLF001
            registry=scene,
            output_registry=out_scene,
            params=line_creation_params,
            validation_mode=ValidationMode.LOOSE,
        )

        entity = detector.get_horizontal_center_line_entity()
        assert entity is None or isinstance(entity, LineEntity)

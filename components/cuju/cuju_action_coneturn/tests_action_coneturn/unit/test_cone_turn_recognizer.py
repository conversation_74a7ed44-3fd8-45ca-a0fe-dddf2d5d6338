"""Test Cases for p_cone_turn_recognizer."""

from copy import deepcopy
from unittest.mock import MagicMock, patch

import pytest
from cuju_action_coneturn.p_cone_turn_recognizer import (
    ConeTurnActionConfig,
    ConeTurnActionRecognizer,
)
from cuju_data_scene.actions.cone_turn_action import ConeTurnedAction
from cuju_data_scene.constants import ExerciseId
from cuju_data_scene.p_scene_registry import SceneRegistry
from imgeta.pipeline.hook_markers import PLUGIN_NAMESPACE
from imgeta.pipeline.hook_specs import HookSpecs
from imgeta.plugin_base.builder.object_builder import ObjectBuilder
from pluggy import PluginManager


@pytest.mark.unittest
class TestConeTurnActionRecognizer:
    """Test cases for ConeTurnActionRecognizer class."""

    def test_recognize_cone_turn(
        self,
        valid_config_1: ConeTurnActionConfig,
        no_cone_turned_standing: tuple[SceneRegistry, ConeTurnedAction],
    ) -> None:
        """Test regognize_cone_turn method."""
        config = valid_config_1
        scene_registry, _ = no_cone_turned_standing
        with patch(
            "cuju_action_coneturn.p_cone_turn_recognizer.LineCrossTurnDetector"
        ) as mock_detector_class:
            mock_detector_instance = MagicMock()
            mock_detector_class.return_value = mock_detector_instance
            mock_action = MagicMock(spec=ConeTurnedAction)
            mock_detector_instance.detect_turns.return_value = [mock_action]

            recognizer = ConeTurnActionRecognizer(config)
            action = recognizer.recognize_turns(
                scene_registry, exercise_id=ExerciseId.T_TEST
            )

        if mock_detector_instance.detect_turns.call_count != 1:
            pytest.fail("detect_turns method not called.")
        if not action:
            pytest.fail("No action returned.")
        if not isinstance(action[0], ConeTurnedAction):
            pytest.fail("Action is not of type ConeTurnedAction.")


@pytest.mark.unittest
class TestNodeRegister:
    """A test class for the 'ConeTurnActionRecognizer' registration.

    This class tests the node registration for 'ConeTurnActionRecognizer', ensuring that
    it is correctly registered within the plugin manager and available in the
    'ObjectBuilder' registry.
    """

    def test_node_register(self) -> None:
        """Test the registration of the 'ConeTurnActionRecognizer' node.

        This test verifies that the 'ConeTurnActionRecognizer' node is properly
        registered in the plugin manager and added to the 'ObjectBuilder' registry.
        """
        # --- create plugin manager from scratch ---
        registry = deepcopy(ObjectBuilder._registry)  # noqa: SLF001
        ObjectBuilder._registry = {}  # noqa: SLF001
        pm = PluginManager(PLUGIN_NAMESPACE)
        pm.add_hookspecs(HookSpecs)
        if (
            pm.is_registered(ConeTurnActionRecognizer)
            or "ConeTurnActionRecognizer" in ObjectBuilder._registry  # noqa: SLF001
        ):
            pytest.fail("'ConeTurnActionRecognizer' already registered")

        # --- register node to plugin manager ---
        if not pm.is_registered(ConeTurnActionRecognizer):
            pm.register(ConeTurnActionRecognizer)
        pm.hook.node_register()

        # --- check whether registration has been successfully ---
        if (
            not pm.is_registered(ConeTurnActionRecognizer)
            or "ConeTurnActionRecognizer" not in ObjectBuilder._registry  # noqa: SLF001
        ):
            pytest.fail("'ConeTurnActionRecognizer' could not have been registered")

        # --- check whether registered object can be actually build ---
        pipe_config = {
            "id": "app_recognize_cone_turn",
            "type": "ConeTurnActionRecognizer",
            "param": {},
        }
        (builder_class, _) = ObjectBuilder.get_builder_class(
            "ConeTurnActionRecognizer",
        )
        pipe_obj = builder_class()(pipe_config, resolve_references=False)
        if not isinstance(pipe_obj, ConeTurnActionRecognizer):
            pytest.fail(
                "'ConeTurnActionRecognizer' object could have been successfully built "
                "through the registered classes.",
            )
        ObjectBuilder._registry = registry  # noqa: SLF001

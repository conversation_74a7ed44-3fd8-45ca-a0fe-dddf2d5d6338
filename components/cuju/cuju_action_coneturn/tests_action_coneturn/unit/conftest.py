"""Fixtures for run start recognizer."""

from datetime import timedel<PERSON>

import numpy as np
import pytest
from cuju_action_coneturn.line_cross_turn_detector.src.constants import (
    PersonReferencePoint,
)
from cuju_action_coneturn.p_cone_turn_recognizer import (
    ConeTurnActionConfig,
)
from cuju_data_detection.bounding_box.box_base import BoundingBox
from cuju_data_detection.bounding_box.utils import BoxType
from cuju_data_pose.pose2d.pose_base import Pose
from cuju_data_scene.actions.cone_turn_action import ConeTurnedAction
from cuju_data_scene.constants import EntityRole
from cuju_data_scene.entities.cone import Cone
from cuju_data_scene.entities.person import Person
from cuju_data_scene.p_scene_registry import MatchingCriteria, SceneRegistry
from imgeta.plugin_base.imgeta_tree.frame_time import FrameTime


@pytest.fixture
def valid_config_1() -> ConeTurnActionConfig:
    """Return a valid configuration for run start recognizer."""
    return ConeTurnActionConfig(
        ids="some_ids", person_position=PersonReferencePoint.BBOX
    )


def cone_mock(num_frames: int) -> Cone:
    """Return a Cone."""
    box = BoundingBox(
        coords=np.array([30.0, 30.0, 22.0, 22.0]), box_type=BoxType.CXCYWH
    )
    return Cone(
        role=EntityRole.CONE_1,
        track_ids=["0"] * num_frames,
        initial_position=box,
        boxes=[box] * num_frames,
        frame_time=[
            FrameTime(
                frame_number=i,
                frame_time=timedelta(seconds=i),
                time_since_start=timedelta(seconds=i),
            )
            for i in range(1, num_frames + 1)
        ],
    )


def pose(marker: tuple[float, float]) -> Pose:
    """Create a pose from marker (marker will be left hip)."""
    coords = np.random.randn(17, 2)
    coords[11, :] = marker  # Left hip
    return Pose(coords=coords)


def create_scene_registry(
    track: list[tuple[float, float]],
) -> SceneRegistry:
    """Create a scene registry with a run simulation over a given number of frames."""
    scene_registry = SceneRegistry()
    boxes, poses, tids, frame_times = [], [], [], []
    for i, marker in enumerate(track):
        tids.append("track-0")
        boxes.append(
            BoundingBox(coords=np.array([*marker, 1, 1]), box_type=BoxType.CXCYWH)
        )
        frame_times.append(
            FrameTime(
                frame_number=i + 1,
                frame_time=timedelta(seconds=i),
                time_since_start=timedelta(seconds=i),
            )
        )
        poses.append(pose(marker))

    scene_registry.upsert_entity(
        Person(
            id="person-0",
            boxes=boxes,
            poses=poses,
            track_ids=tids,
            frame_time=frame_times,
            role=EntityRole.MAIN_PERSON,
        ),
        matching_criteria=MatchingCriteria.ID,
    )
    cone_entity = cone_mock(len(track))
    scene_registry.upsert_entity(
        cone_entity,
        matching_criteria=MatchingCriteria.ID,
    )
    return scene_registry


@pytest.fixture
def no_cone_turned_standing() -> tuple[SceneRegistry, ConeTurnedAction]:
    """Fixture for recognize_cone_turned, where the person is just standing."""
    track = [(0, 0)] * 10
    registry = create_scene_registry(track)
    return registry, None

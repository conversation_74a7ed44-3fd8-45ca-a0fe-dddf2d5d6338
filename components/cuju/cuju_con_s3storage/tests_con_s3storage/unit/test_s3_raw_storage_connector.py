"""Test S3 Storage Connector.

This module contains tests for the S3 Storage Connector.
The following tests are performed:
    - Write a non-bytes object.
    - Write a bytes object.
    - Read a non-existent file.
    - Upload a file.
    - Get URL of a file.
    - Get names in a directory.
"""

from copy import deepcopy
from pathlib import Path
from typing import NoReturn
from unittest import mock

import pytest
from cuju_con_s3storage.p_s3_raw_storage_connector import (
    S3RawStorageConnector,
    S3RawStorageConnectorConfig,
)
from imgeta.pipeline.hook_markers import PLUGIN_NAMESPACE
from imgeta.pipeline.hook_specs import HookSpecs
from imgeta.plugin_base.builder.object_builder import ObjectBuilder
from pluggy import PluginManager

LIST_OF_ENDINGS = ["txt", "csv", "json"]


def _generate_sample_files(
    folder: str | Path,
    num_files: int,
    file_name: str = "test",
    file_ending: str = "txt",
    file_content: str = b"Test text.",
) -> list[Path]:
    if type(folder) is str:
        folder = Path(folder)

    file_paths = []
    for i in range(num_files):
        file_path = folder.joinpath(f"{file_name}_{i}.{file_ending}")
        file_path.write_bytes(file_content)
        file_paths.append(file_path)
    return file_paths


@pytest.mark.unittest
class TestRawStorageConnector:
    """Test cases for the RawStorageConnector Node."""

    def test_write_non_bytes_object(
        self,
        s3_connector_config: S3RawStorageConnectorConfig,
        mock_s3_client: mock.Mock,
    ) -> None:
        """Test writing a non byte object."""
        obj = "non-bytes object"
        name = "test_file.txt"
        with mock.patch.object(
            S3RawStorageConnector, "_create_s3_client", return_value=mock_s3_client
        ):
            connector = S3RawStorageConnector(s3_connector_config)
            with pytest.raises(
                TypeError,
                match=r"Could not write object. Object must be of type bytes",
            ):
                connector.write(obj=obj, name=name)

    def test_write_successful(
        self,
        s3_connector_config: S3RawStorageConnectorConfig,
        mock_s3_client: mock.Mock,
        test_folder: str,
    ) -> None:
        """Test a successful write."""
        obj = b"test bytes"
        name = "test_file.txt"
        with mock.patch.object(
            S3RawStorageConnector, "_create_s3_client", return_value=mock_s3_client
        ):
            s3_connector_config.base_dir = str(test_folder)
            connector = S3RawStorageConnector(s3_connector_config)
        success = connector.write(obj=obj, name=name)

        if success is not True:
            pytest.fail("Expected write to be successful.")
        if not Path(connector.base_dir).joinpath(name).exists():
            pytest.fail("File was not written to disk.")
        if not mock_s3_client.upload_fileobj.called:
            pytest.fail("Expected upload_file to be called.")

    def test_read_non_existent_file(
        self,
        s3_connector_config: S3RawStorageConnectorConfig,
        mock_s3_client: mock.Mock,
    ) -> None:
        """Test reading of non-existent file."""
        with mock.patch.object(
            S3RawStorageConnector, "_create_s3_client", return_value=mock_s3_client
        ):
            connector = S3RawStorageConnector(s3_connector_config)
        with pytest.raises(ConnectionError):
            connector.read("non_existent_file.txt")

    def test_read_successful(
        self,
        s3_connector_config: S3RawStorageConnectorConfig,
        mock_s3_client: mock.Mock,
        test_folder: str,
        file_name: str,
        file_content: bytes,
    ) -> None:
        """Test successful read."""
        with mock.patch.object(
            S3RawStorageConnector, "_create_s3_client", return_value=mock_s3_client
        ):
            s3_connector_config.base_dir = str(test_folder)
            connector = S3RawStorageConnector(s3_connector_config)

        file_content_read = connector.read(file_name)

        if file_content_read != file_content:
            pytest.fail("Expected file content to be 'Test text.'.")
        if not mock_s3_client.download_fileobj.called:
            pytest.fail("Expected download_fileobj to be called.")

    def test_read_with_cache(
        self,
        s3_connector_config: S3RawStorageConnectorConfig,
        mock_s3_client: mock.Mock,
        test_folder: str,
        file_name: str,
        file_content: bytes,
    ) -> None:
        """Test successful read with cache."""
        with mock.patch.object(
            S3RawStorageConnector, "_create_s3_client", return_value=mock_s3_client
        ):
            s3_connector_config.cache_dir = str(test_folder)
            s3_connector_config.base_dir = str(test_folder)
            s3_connector = S3RawStorageConnector(s3_connector_config)

        for _ in range(2):
            red_file_content = s3_connector.read_with_caching(file_name)
            assert red_file_content == file_content

        mock_s3_client.download_file.assert_called_once()

    def test_successful_file_upload(
        self,
        test_folder: str,
        s3_connector_config: S3RawStorageConnectorConfig,
        mock_s3_client: mock.Mock,
        file_name: str,
    ) -> bool:
        """Test file upload functionality."""
        with mock.patch.object(
            S3RawStorageConnector, "_create_s3_client", return_value=mock_s3_client
        ):
            s3_connector_config.base_dir = str(test_folder)
            connector = S3RawStorageConnector(s3_connector_config)
        name = "moved_file.txt"
        success = connector.upload_file(test_folder / file_name, name)

        if not success:
            pytest.fail(f"Expected file upload of {name} to succeed.")
        if not Path(connector.base_dir).joinpath(name).exists():
            pytest.fail(f"Expected {name} to exist in the storage.")
        if not mock_s3_client.upload_file.called:
            pytest.fail("Expected upload_file to be called.")

    def test_unsuccessful_file_upload(
        self,
        test_folder: str,
        s3_connector_config: S3RawStorageConnectorConfig,
        mock_s3_client: mock.Mock,
    ) -> None:
        """Test unsuccessful file upload."""
        with mock.patch.object(
            S3RawStorageConnector, "_create_s3_client", return_value=mock_s3_client
        ):
            s3_connector_config.base_dir = str(test_folder)
            connector = S3RawStorageConnector(s3_connector_config)
        name = "moved_file.txt"
        if connector.upload_file(test_folder / "non_existent_file.txt", name):
            pytest.fail("Expected file upload to fail.")
        if not mock_s3_client.upload_file.called:
            pytest.fail("Expected upload_file to be called.")

    def test_successful_file_download(
        self,
        test_folder: str,
        s3_connector_config: S3RawStorageConnectorConfig,
        mock_s3_client: mock.Mock,
        file_name: str,
        file_content: bytes,
    ) -> None:
        """Test successful file download."""
        with mock.patch.object(
            S3RawStorageConnector, "_create_s3_client", return_value=mock_s3_client
        ):
            s3_connector_config.base_dir = str(test_folder)
            connector = S3RawStorageConnector(s3_connector_config)

        success = connector.download_file(
            test_folder / "downloaded_file.txt",
            file_name,
        )

        if not success:
            pytest.fail("Expected file download to succeed.")
        if not Path(test_folder).joinpath("downloaded_file.txt").exists():
            pytest.fail("Expected downloaded file to exist.")
        file_content_download = Path(test_folder).joinpath(file_name).read_bytes()
        if file_content_download != file_content:
            pytest.fail("Expected downloaded file content to be 'Test text.'.")
        if not mock_s3_client.download_file.called:
            pytest.fail("Expected download_file to be called.")

    def test_unsuccessful_file_download(
        self,
        test_folder: str,
        s3_connector_config: S3RawStorageConnectorConfig,
        mock_s3_client: mock.Mock,
    ) -> None:
        """Test unsuccessful file download."""
        with mock.patch.object(
            S3RawStorageConnector, "_create_s3_client", return_value=mock_s3_client
        ):
            s3_connector_config.base_dir = str(test_folder)
            connector = S3RawStorageConnector(s3_connector_config)

        if connector.download_file(
            test_folder / "downloaded_file.txt",
            "non_existent_file.txt",
        ):
            pytest.fail("Expected file download to fail.")
        if not mock_s3_client.download_file.called:
            pytest.fail("Expected download_file to be called.")

    def test_get_url(
        self,
        test_folder: Path,
        s3_connector_config: S3RawStorageConnectorConfig,
        file_name: str,
        mock_s3_client: mock.Mock,
    ) -> None:
        """Test get_url function of connector."""
        with mock.patch.object(
            S3RawStorageConnector, "_create_s3_client", return_value=mock_s3_client
        ):
            s3_connector_config.base_dir = str(test_folder)
            connector = S3RawStorageConnector(s3_connector_config)
        if connector.get_url(
            bucket_name="mock_bucket",
            file_path=str(test_folder / file_name),
        ) != str(Path(connector.base_dir).joinpath(file_name).absolute()):
            pytest.fail(f"Expected {file_name} to be accessible via get_url.")
        if not mock_s3_client.generate_presigned_url.called:
            pytest.fail("Expected generate_presigned_url to be called.")

    def test_get_names_in_dir_no_suffix(
        self,
        test_folder: Path,
        s3_connector_config: S3RawStorageConnectorConfig,
        mock_s3_client: mock.Mock,
        mock_s3_resource: mock.MagicMock,
        file_name: str,
    ) -> None:
        """Test get_names_in_dir function of connector."""
        file_paths = _generate_sample_files(test_folder, num_files=10)
        file_names = [f.name for f in file_paths] + [file_name]

        with mock.patch.object(
            S3RawStorageConnector, "_create_s3_client", return_value=mock_s3_client
        ):
            s3_connector_config.base_dir = str(test_folder)
            connector = S3RawStorageConnector(s3_connector_config)

        for file_path in file_paths:
            file_path.touch()
        # patch the _get_s3_bucket method of connector
        connector._get_s3_bucket = mock.MagicMock(  # noqa: SLF001
            return_value=mock_s3_resource,
        )
        files_default = connector.get_names_in_dir()
        if not all(map(Path.exists, file_paths)):
            pytest.fail(f"Expected all files to exist in {connector.base_dir}")
        if set(files_default) != set(file_names):
            pytest.fail("Expected all created files to be returned.")
        if not mock_s3_resource.objects.filter.called:
            pytest.fail("Expected list_objects to be called.")

    @pytest.mark.parametrize("file_ending", LIST_OF_ENDINGS)
    def test_get_names_in_dir(  # noqa: PLR0913
        self,
        test_folder: Path,
        s3_connector_config: S3RawStorageConnectorConfig,
        mock_s3_client: mock.Mock,
        mock_s3_resource: mock.MagicMock,
        file_name: str,
        file_ending: str,
    ) -> None:
        """Test get_names_in_dir function of connector."""
        # --- create sample files ---
        all_file_names = {}
        all_file_paths = []

        for file_ending_temp in LIST_OF_ENDINGS:
            file_paths = _generate_sample_files(
                test_folder,
                num_files=10,
                file_ending=file_ending_temp,
            )
            file_names = [f.name for f in file_paths]

            for file_path in file_paths:
                file_path.touch()
            all_file_names[file_ending_temp] = file_names
            all_file_paths.extend(file_paths)

        all_file_paths += [Path(test_folder).joinpath(file_name)]
        all_file_names["txt"] += [file_name]
        # --- test get_names_in_dir ---
        with mock.patch.object(
            S3RawStorageConnector, "_create_s3_client", return_value=mock_s3_client
        ):
            s3_connector_config.base_dir = str(test_folder)
            connector = S3RawStorageConnector(s3_connector_config)

        # patch the _get_s3_bucket method of connector
        connector._get_s3_bucket = mock.MagicMock(  # noqa: SLF001
            return_value=mock_s3_resource,
        )
        files_default = connector.get_names_in_dir(filter_suffix=file_ending)

        if not all(map(Path.exists, all_file_paths)):
            pytest.fail("Expected files to exist in the directory.")
        if set(files_default) != set(all_file_names[file_ending]):
            pytest.fail("Expected all created files to be returned.")
        files_not_in = []
        for file_ending_temp in LIST_OF_ENDINGS:
            if file_ending_temp != file_ending:
                files_not_in.extend(all_file_names[file_ending_temp])
        if set(files_default) & set(files_not_in):
            pytest.fail("Expected only files with the correct suffix to be returned.")
        if not mock_s3_resource.objects.filter.called:
            pytest.fail("Expected list_objects to be called.")

    @pytest.mark.parametrize("func", ["delete", "delete_all"])
    def test_not_implemented(
        self,
        s3_connector_config: S3RawStorageConnectorConfig,
        mock_s3_client: mock.Mock,
        func: str,
    ) -> None:
        """Test not implemented functions."""
        with mock.patch.object(
            S3RawStorageConnector, "_create_s3_client", return_value=mock_s3_client
        ):
            connector = S3RawStorageConnector(s3_connector_config)
            with pytest.raises(NotImplementedError):
                getattr(connector, func)("test")


@pytest.mark.unittest
class TestNodeRegister:
    """A test class for the 'S3RawStorageConnector' registration.

    This class tests the node registration for 'S3RawStorageConnector', ensuring that
    it is correctly registered within the plugin manager and available in the
    'ObjectBuilder' registry.
    """

    def test_node_register(self) -> NoReturn:
        """Test the registration of the 'S3RawStorageConnector' node.

        This test verifies that the 'S3RawStorageConnector' node is properly registered
        in the plugin manager and added to the 'ObjectBuilder' registry.
        """
        # --- create plugin manager from scratch ---
        registry = deepcopy(ObjectBuilder._registry)  # noqa: SLF001
        ObjectBuilder._registry = {}  # noqa: SLF001
        pm = PluginManager(PLUGIN_NAMESPACE)
        pm.add_hookspecs(HookSpecs)
        if (
            pm.is_registered(S3RawStorageConnector)
            or "S3RawStorageConnector" in ObjectBuilder._registry  # noqa: SLF001
        ):
            pytest.fail("'S3RawStorageConnector' already registered")

        # --- register node to plugin manager ---
        if not pm.is_registered(S3RawStorageConnector):
            pm.register(S3RawStorageConnector)
        pm.hook.node_register()

        # --- check whether registration has been successfully ---
        if (
            not pm.is_registered(S3RawStorageConnector)
            or "S3RawStorageConnector" not in ObjectBuilder._registry  # noqa: SLF001
        ):
            pytest.fail("'S3RawStorageConnector' could not have been registered")

        # --- check whether registered object can be actually build ---
        pipe_config = {
            "id": "s3_connector",
            "type": "S3RawStorageConnector",
            "param": {"bucket_name": "test_bucket", "base_dir": ""},
        }
        (builder_class, _) = ObjectBuilder.get_builder_class(
            "S3RawStorageConnector",
        )
        pipe_obj = builder_class()(pipe_config, resolve_references=False)
        if not isinstance(pipe_obj, S3RawStorageConnector):
            pytest.fail(
                "'S3RawStorageConnector' object could have been successfully built "
                "through the registered classes.",
            )
        ObjectBuilder._registry = registry  # noqa: SLF001

"""General connector to raw data on AWS S3 storage.

This module provides:
- S3RawStorageConnector: connector to raw data on AWS S3 storage;
"""

from __future__ import annotations

import hashlib
import io
import typing
from pathlib import Path
from typing import TYPE_CHECKING

import boto3
import boto3.s3
from boto3.s3.transfer import TransferConfig
from botocore.exceptions import ClientError
from imgeta.pipeline.hook_markers import hook_impl
from imgeta.plugin_base.bases.config_base import ConfigBase
from imgeta.plugin_base.bases.storage_base import StorageBase
from imgeta.plugin_base.builder.object_builder import ObjectBuilder
from loguru import logger as logging

from cuju_con_s3storage.role_assumer import RoleAssumer

if TYPE_CHECKING:
    from botocore.client import BaseClient

MSG_NOT_IMPLEMENTED = "This functionality is not implemented yet."


class S3RawStorageConnectorConfig(ConfigBase):
    """Configuration for a AWS S3 raw storage connector.

    Attributes
    ----------
    base_dir : str
        Base directory which we will work on.
    bucket_name : str
        Name of the bucket.
    cache_dir : str | None
        Directory that can be used for caching.
    assumed_role: str | None
        Name of the role to be assumed.
    """

    base_dir: str
    bucket_name: str
    cache_dir: str | None = None
    assumed_role: str | None = None


class S3RawStorageConnector(StorageBase):
    """Connector to raw data on AWS S3 storage."""

    def __init__(self, config: S3RawStorageConnectorConfig) -> None:
        """Create a new instance of a s3 raw storage connector.

        Parameters
        ----------
        config : S3RawStorageConnectorConfig
            Configuration of the AWS S3 raw storage connector.
        """
        super().__init__(
            ids=config.ids,
            base_dir=config.base_dir,
        )

        # --- pass input arguments ---
        self._cfg = config
        self._role_assumer = RoleAssumer(self._cfg.assumed_role)

        try:
            self._client = self._create_s3_client()
            self._transfer_config = TransferConfig(
                multipart_threshold=64 * 1024 * 1024,
                multipart_chunksize=64 * 1024 * 1024,
            )
        except ValueError as exc:
            logging.error(exc)
            raise ValueError(exc) from exc

    def _create_s3_client(self) -> BaseClient:
        """Create an S3 client with automatically refreshing credentials."""
        return self._role_assumer.create_s3_client()

    def _get_s3_bucket(self) -> boto3.s3.Bucket:
        """Get S3 bucket object.

        Returns
        -------
        boto3.s3.Bucket
            S3 bucket object.

        """
        return boto3.resource("s3").Bucket(self._cfg.bucket_name)

    def read(self, name: str) -> bytes:
        """Read rawdata with given name from S3 storage.

        Parameters
        ----------
        name : str
            Name of the file to be read.

        """
        if self._cfg.cache_dir:
            # Perform cached downloading
            return self.read_with_caching(name)
        # Perform direct downloading
        return self.read_direct(name)

    def read_direct(self, name: str) -> bytes:
        """Read rawdata with given name from S3 storage.

        This method provides a direct way to read raw data from the S3 storage
        without any caching mechanism. It directly downloads the file from the
        S3 storage and returns the file content as a bytes object.

        Parameters
        ----------
        name : str
            Name of the file to be read.

        """
        file_path = (Path(self.base_dir) / name).as_posix()
        logging.debug(f"Reading file {file_path} from S3 storage")

        # Directly download from S3 storage
        file_content = io.BytesIO()
        self._client.download_fileobj(
            Bucket=self._cfg.bucket_name,
            Key=file_path,
            Fileobj=file_content,
            Config=self._transfer_config,
        )
        file_content.seek(0)

        return file_content.read()

    def read_with_caching(self, name: str) -> bytes:
        """Read rawdata with given name from S3 storage.

        This method provides a way to read raw data from the S3 storage while
        everaging caching to improve performance. It ensures that the file is
        only downloaded from the S3 storage if it doesn't already exist in the
        cache. This can significantly reduce the number of requests made to the
        S3 storage, especially when dealing with large files or frequent access
        to the same files.

        Parameters
        ----------
        name : str
            Name of the file to be read.

        """
        file_path = (Path(self.base_dir) / name).as_posix()
        logging.debug(f"Reading file {file_path} from S3 storage")

        # Create hash based on file path
        hash_obj = hashlib.sha256(file_path.encode()).hexdigest()

        # expand "~" to user's home directory
        cache_dir_path = Path(self._cfg.cache_dir).expanduser()
        cache_dir_path.mkdir(parents=True, exist_ok=True)
        cache_file = cache_dir_path / (hash_obj + ".data")

        if not cache_file.exists():
            # Download file from S3 to cache
            self._client.download_file(
                Bucket=self._cfg.bucket_name,
                Key=file_path,
                Filename=str(cache_file),
            )

        # Read from cache file
        with cache_file.open(mode="rb") as file:
            raw_file = file.read()

        return raw_file  # noqa: RET504

    def write(self, obj: bytes, name: str) -> bool:
        """Write rawdata with given name to S3 storage.

        Parameters
        ----------
        obj : bytes
            Object to write to file.
        name : str
            Name of the file to write.

        Raises
        ------
        ValueError
            If object is not of type bytes.
        """
        # --- check if object is of type bytes ---
        if not isinstance(obj, bytes):
            msg = "Could not write object. Object must be of type bytes"
            logging.error(msg)
            raise TypeError(msg)

        file_path = Path(self.base_dir) / name
        logging.debug(
            f"Writing file to s3 storage to location: {file_path.as_posix()}",
        )

        file_content = io.BytesIO(obj)
        file_content.seek(0)

        try:
            self._client.upload_fileobj(
                Bucket=self._cfg.bucket_name,
                Key=file_path.as_posix(),
                Fileobj=file_content,
                Config=self._transfer_config,
                ExtraArgs={"ACL": "bucket-owner-full-control"},
            )
        except ConnectionError as e:
            logging.error(f"Error uploading file object. {e}")

        logging.debug(
            f"uploading file to s3 storage as {file_path.as_posix()} in bucket"
            f" {self._cfg.bucket_name}",
        )

        return True

    def upload_file(self, local_file_name: str, name: str) -> bool:
        """Upload local stored file with given name to S3 storage.

        Parameters
        ----------
        local_file_name : str
            Path to local_file.
        name : str
            Name of the file to write.

        """
        file_path = Path(self.base_dir) / name
        try:
            self._client.upload_file(
                local_file_name,
                self._cfg.bucket_name,
                file_path.as_posix(),
                ExtraArgs={"ACL": "bucket-owner-full-control"},
            )
        except ConnectionError as e:
            logging.error(f"Error uploading file. {e}")
            return False
        return True

    def download_file(self, local_file_name: str, name: str) -> bool:
        """Download file with given name from S3 storage to local storage.

        Parameters
        ----------
        local_file_name : str
            Path to local_file.
        name : str
            Name of the file to download.

        """
        file_path = Path(self.base_dir) / name
        try:
            self._client.download_file(
                self._cfg.bucket_name,
                file_path.as_posix(),
                local_file_name,
            )
        except ConnectionError as e:
            logging.error(f"Error downloading file. {e}")
            return False
        return True

    def get_url(self, bucket_name: str | None, file_path: str) -> str | None:
        """Get url for given file.

        Parameters
        ----------
        bucket_name : str
            bucket name of stored file.
        file_path : str
            file path of stored file.

        """
        try:
            return self._client.generate_presigned_url(
                ClientMethod="get_object",
                Params={
                    "Bucket": bucket_name or self._cfg.bucket_name,
                    "Key": file_path,
                },
            )
        except ClientError:
            logging.error("Presigned url generation failed.")
            return None

    def get_names_in_dir(
        self,
        filter_suffix: str | None = None,
        *,
        recursive: bool = True,
    ) -> None:
        """Get names of all files in a given directory.

        Parameters
        ----------
        recursive : bool
            Flag to recursively search for files.

        """
        depth = -1 if recursive else 1

        return self._s3_paths_generator(self.base_dir, depth, filter_suffix)

    def get_dirs(self, *, recursive: bool = True) -> None:
        """Get names of all directories in a given directory.

        Parameters
        ----------
        recursive : bool
            Flag to recursively search for directories.

        """
        raise NotImplementedError(MSG_NOT_IMPLEMENTED)

    def delete(self, name: str) -> None:
        """Delete file with given name from S3 storage.

        Parameters
        ----------
        name : str
            Name of the file to delete.

        """
        raise NotImplementedError(MSG_NOT_IMPLEMENTED)

    def delete_all(self, name_suffix: str | None = None) -> None:
        """Delete all files with given suffix from S3 storage.

        Parameters
        ----------
        name_suffix : str
            Suffix of the files to delete.

        """
        raise NotImplementedError(MSG_NOT_IMPLEMENTED)

    def _s3_paths_generator(
        self,
        root_location: str,
        depth: int,
        filter_suffix: str | None,
    ) -> typing.Generator[str]:
        # --- remove last slash "/"
        root_location = root_location.removesuffix("/")

        root_depth = root_location.count("/")

        s3_bucket = self._get_s3_bucket()
        for content in s3_bucket.objects.filter(
            Bucket=self._cfg.bucket_name,
            Prefix=root_location,
        ).all():
            if filter_suffix is not None and not content.key.endswith(filter_suffix):
                continue
            if content.size > 0:
                # -- go up to specified depth --
                content_depth = content.key.count("/")
                if depth == -1 or content_depth - root_depth <= depth:
                    last_modified = content.last_modified
                    logging.debug(
                        f"Key last modified {last_modified:%Y-%m-%d %H:%M:%S}",
                    )
                    yield content.key.replace(f"{root_location}/", "")

    @staticmethod
    @hook_impl
    def node_register() -> None:
        """Register a S3 raw storage connector."""
        ObjectBuilder.direct_register(
            id_str="S3RawStorageConnector",
            object_type=(
                "S3RawStorageConnector",
                "cuju_con_s3storage.p_s3_raw_storage_connector",
            ),
            config_type=(
                "S3RawStorageConnectorConfig",
                "cuju_con_s3storage.p_s3_raw_storage_connector",
            ),
        )

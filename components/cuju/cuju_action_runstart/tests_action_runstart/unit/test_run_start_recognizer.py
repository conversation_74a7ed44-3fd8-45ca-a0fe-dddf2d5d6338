"""Test Cases for p_run_start_recognizer."""

from copy import deepcopy
from datetime import timedelta
from unittest.mock import patch

import numpy as np
import pandas as pd
import pytest
from cuju_action_runstart.p_run_start_recognizer import (
    DEFAULT_KEYPOINTS,
    RunStartActionConfig,
    RunStartActionRecognizer,
)
from cuju_data_detection.bounding_box.box_base import Bounding<PERSON>ox
from cuju_data_detection.bounding_box.utils import BoxType
from cuju_data_pose.pose2d.pose_base import Pose
from cuju_data_scene.actions.filter import SceneActionFilter
from cuju_data_scene.actions.run_start_action import RunStartAction
from cuju_data_scene.entities.cone import Cone
from cuju_data_scene.entities.person import Person
from cuju_data_scene.p_scene_registry import SceneRegistry
from cuju_data_scene.scene_exceptions import InvalidSetupError
from cuju_data_scene.scene_result import MeasurementUnit
from imgeta.pipeline.hook_markers import PLUGIN_NAMESPACE
from imgeta.pipeline.hook_specs import HookSpecs
from imgeta.plugin_base.builder.object_builder import ObjectBuilder
from imgeta.plugin_base.imgeta_tree.frame_time import FrameTime
from pluggy import PluginManager


@pytest.mark.unittest
class TestRunStartActionRecognizer:
    """Test cases for RunStartActionRecognizer class."""

    @pytest.mark.parametrize(
        ("positions", "expected_positions"),
        [
            pytest.param(
                [np.array([[10, 5.0]]), np.array([[20, 8.0]])],
                pd.DataFrame(
                    [[0, 121, 871, 466, 130], [1, 121, 871, 466, 130]],
                    columns=[
                        "frame",
                        "left_shoulder",
                        "left_hip",
                        "right_shoulder",
                        "right_hip",
                    ],
                ),
            ),
        ],
    )
    def test_extract_positions_from_persons(
        self,
        positions: list[np.array],
        expected_positions: list[np.array],
    ) -> None:
        """Test extract_positions_from_persons method."""
        np.random.seed(42)  # For reproducibility
        boxes, poses, track_ids, frame_time = [], [], [], []
        height, width = 720, 1280

        # Generate 17 random (x, y) coordinates
        coords = np.column_stack(
            (
                np.random.randint(0, width, size=17),  # x-coordinates
                np.random.randint(0, height, size=17),  # y-coordinates
            )
        )
        for i, pos in enumerate(positions):
            boxes.append(
                BoundingBox(
                    coords=np.array([pos[0][0], pos[0][1], 1.0, 1.0]),
                    box_type=BoxType.CXCYWH,
                )
            )
            pose_mock = Pose(
                coords=coords,
                visibility=np.ones(17),
            )
            poses.append(pose_mock)
            track_ids.append(None)
            frame_time.append(
                FrameTime(
                    frame_number=i,
                    frame_time=timedelta(seconds=i),
                    time_since_start=timedelta(seconds=i),
                )
            )
        person = Person(
            boxes=boxes, poses=poses, track_ids=track_ids, frame_time=frame_time
        )

        output_positions = RunStartActionRecognizer.extract_positions_from_person(
            person, keypoints=DEFAULT_KEYPOINTS, smooth_window_size=1
        )

        if (output_positions != expected_positions).any().any():
            pytest.fail(
                "extract_positions_from_persons failed to compute correct positions.",
            )

    @pytest.mark.parametrize(
        ("fixture_name"),
        ["run_start_detected"],
    )
    def test_recognize_run_start(
        self,
        fixture_name: str,
        request: pytest.FixtureRequest,
    ) -> None:
        """Test regognize_run_start method."""
        np.random.seed(42)  # For reproducibility
        config, scene_registry, expected_action = request.getfixturevalue(fixture_name)
        recognizer = RunStartActionRecognizer(config)
        with patch("uuid.uuid4", return_value="run_start_action"):
            action = recognizer.recognize_run_start(
                scene_registry,
                config,
                backend_start_frame=0,
                fps=30.0,
            )
        if action != expected_action:
            pytest.fail(
                "Recognition of run start failed. Actions do not match."
                f" Expected: {expected_action.model_dump()}, got: {action.model_dump()}"
            )

    @pytest.mark.parametrize(
        ("action_position", "frame_index", "pixel2cm", "expected_distance_m"),
        [
            pytest.param(np.array([1.0, 1.0]), 0, 1.0, 0.01),
            pytest.param(np.array([10.0, 10.0]), 0, 1.0, 0.1),
            pytest.param(np.array([10.0, 10.0]), 1, 0.5, 0.05),
            pytest.param(np.array([10.0, 10.0]), 2, 2.0, 0.2),
        ],
    )
    def test_compute_distance_to_start_cone(
        self,
        action_position: np.ndarray,
        frame_index: int,
        pixel2cm: np.ndarray,
        expected_distance_m: float,
        cone_mock: Cone,
    ) -> None:
        """Test the computation of the distance to the start cone."""
        distance_m, _ = RunStartActionRecognizer.compute_distance_to_first_cone(
            action_position, cone_mock, frame_index, pixel2cm
        )
        if abs(distance_m - expected_distance_m) > 1e-6:
            pytest.fail(
                "Distance to start cone computation failed. "
                f"Expected: {expected_distance_m}, got: {distance_m}"
            )

    def test_runstart_fails_no_mainperson(
        self, valid_config: RunStartActionConfig
    ) -> None:
        """Tests whether the run start fails when no main person is in the registry."""
        scene_registry = SceneRegistry()
        if (
            RunStartActionRecognizer.recognize_run_start(
                scene_registry,
                valid_config,
                backend_start_frame=0,
            )
            is not None
        ):
            pytest.fail("Recognition of run start when no main person is not None.")

    def test_understand_scene_success(
        self,
        run_start_detected: tuple[RunStartActionConfig, SceneRegistry, RunStartAction],
    ) -> None:
        """Tests the 'local_apply' method of the 'RunStartActionRecognizer' node."""
        config, scene_registry, expected_action = run_start_detected
        recognizer = RunStartActionRecognizer(config)
        with patch("uuid.uuid4", return_value="run_start_action"):
            recognizer._understand_scene(scene_registry)  # noqa: SLF001
        action = scene_registry.retrieve_actions(SceneActionFilter(RunStartAction))[0]
        expected_action.distance_to_start_cone = 0.3
        expected_action.distance_unit = MeasurementUnit.METERS
        if action != expected_action:
            pytest.fail(
                "Recognition of run start in local_apply failed. Actions do not match."
                f" Expected: {expected_action.model_dump()}, got: {action.model_dump()}"
            )

    def test_understand_scene_fails_no_setup_action(
        self,
        run_start_detected: tuple[RunStartActionConfig, SceneRegistry, RunStartAction],
    ) -> None:
        """Test if _local_apply fails correctly if no SetupCheckedAction is present."""
        config, scene_registry, _ = run_start_detected
        del scene_registry._actions_registry["setup-check"]  # noqa: SLF001
        recognizer = RunStartActionRecognizer(config)
        exp_err = r"^Could not retrieve starting cone or SetupCheckedAction .*"
        with pytest.raises(
            InvalidSetupError,
            match=exp_err,
        ):
            recognizer._understand_scene(scene_registry)  # noqa: SLF001


@pytest.mark.unittest
class TestNodeRegister:
    """A test class for the 'RunStartActionRecognizer' registration.

    This class tests the node registration for 'RunStartActionRecognizer', ensuring that
    it is correctly registered within the plugin manager and available in the
    'ObjectBuilder' registry.
    """

    def test_node_register(self) -> None:
        """Test the registration of the 'RunStartActionRecognizer' node.

        This test verifies that the 'RunStartActionRecognizer' node is properly
        registered in the plugin manager and added to the 'ObjectBuilder' registry.
        """
        # --- create plugin manager from scratch ---
        registry = deepcopy(ObjectBuilder._registry)  # noqa: SLF001
        ObjectBuilder._registry = {}  # noqa: SLF001
        pm = PluginManager(PLUGIN_NAMESPACE)
        pm.add_hookspecs(HookSpecs)
        if (
            pm.is_registered(RunStartActionRecognizer)
            or "RunStartActionRecognizer" in ObjectBuilder._registry  # noqa: SLF001
        ):
            pytest.fail("'RunStartActionRecognizer' already registered")

        # --- register node to plugin manager ---
        if not pm.is_registered(RunStartActionRecognizer):
            pm.register(RunStartActionRecognizer)
        pm.hook.node_register()

        # --- check whether registration has been successfully ---
        if (
            not pm.is_registered(RunStartActionRecognizer)
            or "RunStartActionRecognizer" not in ObjectBuilder._registry  # noqa: SLF001
        ):
            pytest.fail("'RunStartActionRecognizer' could not have been registered")

        # --- check whether registered object can be actually build ---
        pipe_config = {
            "id": "app_recognize_run_start",
            "type": "RunStartActionRecognizer",
            "param": {
                "threshold": 0.2,
                "normalize": True,
            },
        }
        (builder_class, _) = ObjectBuilder.get_builder_class(
            "RunStartActionRecognizer",
        )
        pipe_obj = builder_class()(pipe_config, resolve_references=False)
        if not isinstance(pipe_obj, RunStartActionRecognizer):
            pytest.fail(
                "'RunStartActionRecognizer' object could have been successfully built "
                "through the registered classes.",
            )
        ObjectBuilder._registry = registry  # noqa: SLF001

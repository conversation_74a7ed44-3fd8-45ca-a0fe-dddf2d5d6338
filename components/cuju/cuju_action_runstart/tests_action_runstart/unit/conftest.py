"""Fixtures for run start recognizer."""

from copy import deepcopy
from datetime import timed<PERSON><PERSON>
from unittest.mock import Magic<PERSON>ock

import numpy as np
import pytest
from cuju_action_runstart.p_run_start_recognizer import RunStartActionConfig
from cuju_data_detection.bounding_box.box_base import BoundingBox
from cuju_data_detection.bounding_box.utils import BoxType
from cuju_data_pose.pose2d.pose_base import Pose
from cuju_data_scene.actions.run_start_action import RunStartAction
from cuju_data_scene.actions.setup_checked_action import SetupCheckedAction, SetupResult
from cuju_data_scene.constants import EntityRole
from cuju_data_scene.entities.cone import Cone
from cuju_data_scene.entities.person import Person
from cuju_data_scene.p_scene_registry import MatchingCriteria, SceneRegistry
from imgeta.plugin_base.imgeta_tree.frame_time import FrameTime


@pytest.fixture
def valid_config() -> RunStartActionConfig:
    """Return a valid configuration for run start recognizer."""
    state_storage = MagicMock()
    state = {
        "frame_rate": 30.0,  # Default frame rate
        "start_frame": 0,  # Default start frame
    }
    state_storage.get_state = lambda key: state.get(key)
    return RunStartActionConfig(
        ids="some_ids",
        threshold=0.5,
        normalize=False,
        variance_keypoints=["left_hip"],
        state_storage=state_storage,
    )


def create_run_simulation(
    starting_position: tuple[int, int],
    starting_frame: int,
    num_frames: int,
    acceleration: float,
) -> list[tuple[float, float]]:
    """Create a run simulation that returns marker positions.

    The simulation assumes that the marker (e.g., a runner) remains stationary until
    the specified starting_frame. At starting_frame, the marker begins moving
    along the x-axis with zero initial velocity and a constant acceleration
    thereafter.

    Positions for all frames from 0 to num_frames-1 (inclusive) are returned.
    The y-position remains constant throughout the simulation.

    Parameters
    ----------
    starting_position : tuple[int, int]
        The starting (x, y) position of the simulation. The marker remains at this
        position up until the starting_frame.
    starting_frame : int
        The frame number at which the marker starts moving. Before this frame,
        the marker does not move.
    num_frames : int
        The total number of frames to simulate.
    acceleration : float
        The constant acceleration (in units per frame^2) of the marker in x-direction
        after starting_frame.

    Returns
    -------
    list[tuple[float, float]]
        A list of (x, y) positions of the marker for each frame.
    """
    x0, y0 = starting_position
    positions = []

    for frame in range(num_frames):
        if frame < starting_frame:
            # No movement before starting_frame
            x = x0
            y = y0
        else:
            # Time since start of movement
            t = frame - starting_frame
            # x = x0 + 0.5 * a * t^2 (with initial velocity = 0)
            x = x0 + 0.5 * acceleration * (t**2)
            y = y0
        positions.append((x, y))

    return positions


def bounding_box(marker: tuple[float, float]) -> BoundingBox:
    """Create a simple bounding box."""
    return BoundingBox(coords=np.array([*marker, 1, 1]), box_type=BoxType.CXCYWH)


def pose(marker: tuple[float, float]) -> Pose:
    """Create a pose from marker (marker will be left hip)."""
    coords = np.random.randn(17, 2)
    coords[11, :] = marker  # Left hip
    coords[12, :] = marker + np.array([1.0, 0.0])  # Right hip
    coords[5, :] = marker - np.array([0.0, 1.0])  # Left Shoulder
    coords[6, :] = marker + np.array([1.0, -1.0])  # Right Shoulder

    return Pose(coords=coords)


def create_scene_registry(
    run_simulation_params: tuple[tuple, int, int, float],
    cone_mock: Cone,
) -> SceneRegistry:
    """Create a scene registry with a run simulation over a given number of frames."""
    track = create_run_simulation(*run_simulation_params)
    scene_registry = SceneRegistry()
    boxes, poses, tids, frame_times = [], [], [], []
    for i, marker in enumerate(track):
        tids.append("track-0")
        boxes.append(
            bounding_box(marker),
        )
        frame_times.append(
            FrameTime(
                frame_number=i,
                frame_time=timedelta(seconds=i),
                time_since_start=timedelta(seconds=i),
            )
        )
        poses.append(pose(marker))

    scene_registry.upsert_entity(
        Person(
            id="track-0",
            boxes=boxes,
            poses=poses,
            track_ids=tids,
            frame_time=frame_times,
            role=EntityRole.MAIN_PERSON,
        ),
        matching_criteria=MatchingCriteria.ID,
    )

    setup_action = SetupCheckedAction(
        id="setup-check",
        time=frame_times[0],
        pixel2cm=10.0,
        setup_result=SetupResult.ACCEPTED,
    )
    scene_registry.upsert_action(setup_action)
    scene_registry.upsert_entity(cone_mock, matching_criteria=MatchingCriteria.ROLE)
    return scene_registry


@pytest.fixture
def run_start_detected(
    valid_config: RunStartActionConfig,
    cone_mock: Cone,
) -> tuple[RunStartActionConfig, SceneRegistry, RunStartAction]:
    """Fixture for recognize_run_start, where running onset should be detected."""
    frame_time = FrameTime(
        frame_number=17,
        frame_time=timedelta(seconds=17),
        time_since_start=timedelta(seconds=17),
    )
    registry = create_scene_registry(((1, 2), 15, 100, 1.0), cone_mock)
    expected_action = RunStartAction(
        id="run_start_action", time=frame_time, position=(3, 2.0), entity_id="track-0"
    )

    return deepcopy((valid_config, registry, expected_action))


@pytest.fixture
def cone_mock() -> Cone:
    """Return a Cone."""
    box = BoundingBox(coords=np.array([0.0, 0.0, 22.0, 22.0]), box_type=BoxType.CXCYWH)
    return Cone(
        role=EntityRole.CONE_1,
        track_ids=["2"] * 25,
        initial_position=box,
        boxes=[box] * 25,
        frame_time=[
            FrameTime(
                frame_number=i,
                frame_time=timedelta(seconds=i),
                time_since_start=timedelta(seconds=i),
            )
            for i in range(25)
        ],
    )

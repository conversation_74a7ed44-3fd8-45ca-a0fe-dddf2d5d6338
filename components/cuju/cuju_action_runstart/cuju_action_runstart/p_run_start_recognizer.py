"""Run Start Action."""

from __future__ import annotations

from itertools import takewhile
from typing import TYPE_CHECKING

import pandas as pd
from cuju_data_scene.actions.filter import SceneActionFilter
from cuju_data_scene.actions.line_crossed_action import LineCrossedAction
from cuju_data_scene.actions.run_start_action import RunStartAction
from cuju_data_scene.actions.setup_checked_action import SetupCheckedAction
from cuju_data_scene.constants import Entity<PERSON><PERSON>
from cuju_data_scene.entities.cone import Cone
from cuju_data_scene.entities.person import Person
from cuju_data_scene.entities.smoothing import SmoothMethod
from cuju_data_scene.entities.tracked_entity import ToPointMethod
from cuju_data_scene.p_scene_registry import MatchingCriteria, SceneRegistry
from cuju_data_scene.scene_exceptions import InvalidSetupError
from cuju_data_scene.scene_node import SceneNode
from cuju_data_scene.scene_result import MeasurementUnit
from imgeta.pipeline.hook_markers import hook_impl
from imgeta.plugin_base.bases.config_base import ConfigBase
from imgeta.plugin_base.bases.control_flag import ControlFlag
from imgeta.plugin_base.builder.object_builder import ObjectBuilder
from loguru import logger as logging
from pydantic import Field

if TYPE_CHECKING:
    from cuju_data_scene.entities.direction import Direction

DEFAULT_KEYPOINTS = [
    "left_shoulder",
    "left_hip",
    "right_shoulder",
    "right_hip",
]
UNNORMALIZED_THRESHOLD = 50


class RunStartActionConfig(ConfigBase):
    """Configuration for RunStartAction.

    The `threshold` and `normalize` parameters influence each other. If normalize is
    toggled, one should also adjust the threshold accordingly.

    Attributes
    ----------
        smooth_window_size : int (default: 3)
            The size of the rolling window for smoothing poses.
        smooth_method : SmoothMethod (default: SmoothMethod.ROLLING)
            The method to use for smoothing the poses. Defaults to ROLLING.
        var_window_size : int (default: 10)
            The size of the rolling window for computing the variance
        on_control_flag : ControlFlag
            The control flag for the action recognizer. Defaults on END_OF_STREAM.
        variance_keypoint : list[str]
            The keypoints to use for the variance. Defaults to
            ["left_hip", "right_hip"].
        keypoints : list[str]
            The keypoints to use for the action recognizer. Defaults to
            ["left_shoulder", "left_hip", "right_shoulder", "right_hip"].
        seconds_after_start : float
            The number of seconds after the start of the scene to consider for the
            action recognizer. Defaults to 2 seconds.
    """

    smooth_window_size: int = 3
    smooth_method: SmoothMethod = SmoothMethod.ROLLING
    var_window_size: int = 10
    on_control_flag: ControlFlag = ControlFlag.END_OF_STREAM
    variance_keypoints: list[str] = Field(
        default_factory=lambda: ["left_hip", "right_hip"]
    )
    keypoints: list[str] = Field(default_factory=lambda: DEFAULT_KEYPOINTS)
    seconds_after_start: float = 2


class RunStartActionRecognizer(SceneNode):
    """Run Start Action.

    Prerequisites:
    --------------
    Requires SetupCheckedAction already present in SceneRegistry.
    """

    def __init__(self, config: RunStartActionConfig) -> None:
        """Initialize RunStartAction."""
        super().__init__(
            ids=config.ids,
            on_control_flag=config.on_control_flag,
            state_storage=config.state_storage,
        )
        self._cfg = config
        self._setup_direction: Direction | None = None

    @staticmethod
    def extract_positions_from_person(
        person: Person,
        keypoints: list[str],
        smooth_window_size: int = 3,
        smooth_method: SmoothMethod = SmoothMethod.ROLLING,
        to_frame: int = -1,
    ) -> pd.DataFrame:
        """Extract the positions from a person entity.

        Parameter
        ----------
        person: Person
            The person entity to extract the positions from.
        keypoints : list[str]
            The keypoints to use for the action recognizer.
        smooth_window_size : int
            The size of the rolling window for smoothing poses.
        smooth_method : SmoothMethod
            The method to use for smoothing the poses. Defaults to ROLLING.
        to_frame : int
            The frame number to which the positions should be converted.
            If -1, the positions are returned as is.

        Return
        -------
        pd.DataFrame
            A DataFrame with the positions of the person entity.
            The DataFrame contains the columns "frame", "x", and the keypoint names.
            The "x" column contains the x-coordinates of the keypoints.
        """
        mp_t_df = person.get_points_as_df(
            conversion_method=ToPointMethod.KPT_NAME,
            keypoints=keypoints,
            smooth_window=smooth_window_size,
            smooth_method=smooth_method,
            to_frame=to_frame,
        ).reset_index()[["frame", "x", "conv_method"]]
        positions_df = pd.DataFrame()
        positions_df["frame"] = mp_t_df["frame"].unique()
        for col in mp_t_df["conv_method"].unique():
            kpt_name = col.replace("kpt_name_", "")
            positions_df = pd.merge(
                positions_df,
                mp_t_df[mp_t_df["conv_method"] == col][["frame", "x"]],
                on="frame",
            )
            positions_df[kpt_name] = positions_df["x"]
            positions_df = positions_df.drop(["x"], axis=1)

        return positions_df

    @staticmethod
    def compute_distance_to_first_cone(
        action_position: tuple[float, float],
        cone_1: Cone,
        frame_number: int | None,
        pixel2cm: float,
    ) -> tuple[float, MeasurementUnit]:
        """Compute the distance between the positions of run start and the first cone.

        Parameters
        ----------
        action_position: tuple[float, float]
            The position of the run start action.
        cone_1: Cone
            The Cone with the role CONE_1.
        frame_number : int
            The frame number of the run_start action.
        pixel2cm : float
            The pixel to cm ratio for this scene.

        Returns
        -------
        tuple[float, MeasurementUnit]
            The distance to the first cone and its unit.
        """
        cone_position = cone_1.to_point(frame=frame_number)
        distance = abs(
            cone_position[0] - action_position[0]
        )  # only horizontal distance
        return round(distance * pixel2cm / 100, 2), MeasurementUnit.METERS

    @staticmethod
    def _get_main_person(scene_registry: SceneRegistry) -> Person | None:
        mp = scene_registry.retrieve_entity(
            criteria=EntityRole.MAIN_PERSON,
            matching_criteria=MatchingCriteria.ROLE,
            entity_type=Person,
        )
        if not mp:
            logging.error("No main person found in scene registry. skipping run-start.")
            return None
        return mp

    @staticmethod
    def _add_variance_columns(
        df: pd.DataFrame,
        config: RunStartActionConfig,
    ) -> pd.DataFrame:
        for kpt in config.variance_keypoints:
            if df.empty:
                continue
            var_col = f"var_{kpt}"
            df[var_col] = (
                df[kpt].rolling(window=config.var_window_size, center=True).var()
            )
        return df.dropna(subset=[f"var_{kpt}" for kpt in config.variance_keypoints])

    @staticmethod
    def recognize_run_start(
        scene_registry: SceneRegistry,
        config: RunStartActionConfig,
        backend_start_frame: int,
        fps: float = 30.0,
        setup_direction: Direction | None = None,
    ) -> RunStartAction | None:
        """Try to recognize the run start.

        If no run start is detected, returns None.

        Parameters
        ----------
        scene_registry : SceneRegistry
            The scene registry for this scene.
        config: RunStartActionConfig
            The configuration for this action recognizer.
        backend_start_frame: int
            The frame number at which the scene starts.
        fps: float
            The frame rate of the scene. Defaults to 30.0.

        Returns
        -------
        RunStartAction
            An RunStartAction object containing RunStartAction if run start was
            detected. Otherwise None.
        """
        mp = RunStartActionRecognizer._get_main_person(scene_registry)
        if not mp:
            return None
        cone_1_crossing_frame = (
            RunStartActionRecognizer.determine_start_cone_crossing_frame(
                scene_registry,
                mp,
                setup_direction=setup_direction,
            )
        )

        mp_trj_df: pd.DataFrame = (
            RunStartActionRecognizer.extract_positions_from_person(
                mp,
                keypoints=config.variance_keypoints,
                smooth_window_size=config.smooth_window_size,
                smooth_method=config.smooth_method,
                to_frame=max(
                    backend_start_frame + config.seconds_after_start * fps,
                    cone_1_crossing_frame + (config.seconds_after_start / 2) * fps,
                ),
            )
        )

        mp_trj_df = RunStartActionRecognizer._add_variance_columns(mp_trj_df, config)
        index_list = []
        if not mp_trj_df.empty:
            for kpt in config.variance_keypoints:
                for i in range(len(mp_trj_df) - 1):
                    if (f"var_{kpt}" in mp_trj_df.columns) and (
                        (
                            mp_trj_df[f"var_{kpt}"].iloc[i + 1 :]
                            > UNNORMALIZED_THRESHOLD
                        ).all()
                    ):
                        index_list.append(i)
                        break
        # If no index is found, use the backend start frame as the run start frame
        first_frame_idx = min(index_list) if index_list else backend_start_frame
        first_frame_number = mp_trj_df.iloc[first_frame_idx]["frame"]
        run_start_action = RunStartAction(
            time=mp.frame_time[mp.get_frame_index(first_frame_number)],
            entity_id=mp.id,
            position=mp.to_point(
                frame=first_frame_number,
                keypoints=config.keypoints,
            ),
        )
        logging.info(f"Run start detected at frame {first_frame_number}.")
        return run_start_action

    @staticmethod
    def determine_start_cone_crossing_frame(
        scene_registry: SceneRegistry,
        mp: Person,
        setup_direction: Direction,
    ) -> int:
        """Determine the frame number at which the main person crosses the first cone.

        This is used to determine the frame number until which the main person
        trajectory is considered for the run start action recognizer.

        Parameters
        ----------
        scene_registry : SceneRegistry
            The scene registry for this scene.
        mp : Person
            The main person entity in the scene.

        Returns
        -------
        int
            The frame number at which the main person crosses the first cone.
            If no crossing is detected, returns -1.
        """
        cone_1_crossing_frame = -1
        if setup_direction is not None:
            cone_line_cross_actions = scene_registry.retrieve_actions(
                SceneActionFilter(
                    LineCrossedAction,
                    attributes={
                        "entity_id": mp.id,
                    },
                ),
                sort=True,
            )
            cone1_action_indices = [
                i
                for i, a in enumerate(cone_line_cross_actions)
                if (
                    a.anchor_role == EntityRole.CONE_1
                    and a.crossing_direction == setup_direction
                )
            ]

            if not cone1_action_indices:
                logging.warning(
                    "No cone line crossing actions for cone_1 found for main person."
                )
                return cone_1_crossing_frame

            cone_line_cross_actions = cone_line_cross_actions[cone1_action_indices[0] :]

            if cone_line_cross_actions:
                # take latest cone1 crossing before any other cone crossing
                cone_1_crossing_frame = max(
                    takewhile(
                        lambda a: (
                            a.anchor_role == EntityRole.CONE_1
                            and a.crossing_direction == setup_direction
                        ),
                        cone_line_cross_actions,
                    ),
                    key=lambda x: x.time.frame_number,
                    default=None,
                ).frame_number
        return cone_1_crossing_frame

    def _understand_scene(
        self,
        scene_registry: SceneRegistry,
    ) -> None:
        """Apply the RunStartAction recognizer."""
        if self._state_storage is None:
            msg = f"Invalid or no global state storage provided in {self.ids}."
            raise RuntimeError(msg)
        backend_start_frame = self._state_storage.get_state("start_frame")
        fps = self._state_storage.get_state("frame_rate")
        if backend_start_frame is None:
            msg = "No start frame found in state storage. Cannot recognize run start."
            logging.error(msg)
            return
        first_cone, setup_action = self._init_setup(scene_registry)

        run_start_action = RunStartActionRecognizer.recognize_run_start(
            scene_registry=scene_registry,
            config=self._cfg,
            backend_start_frame=backend_start_frame,
            fps=fps,
            setup_direction=self._setup_direction,
        )
        if run_start_action is None:
            return

        distance_to_first_cone, measurement_unit = (
            RunStartActionRecognizer.compute_distance_to_first_cone(
                run_start_action.position,
                first_cone,
                run_start_action.time.frame_number,
                setup_action.pixel2cm,
            )
        )

        run_start_action.distance_to_start_cone = distance_to_first_cone
        run_start_action.distance_unit = measurement_unit

        if run_start_action:
            scene_registry.upsert_action(run_start_action)

    def _init_setup(
        self, scene_registry: SceneRegistry
    ) -> tuple[Cone, SetupCheckedAction]:
        first_cone = scene_registry.retrieve_entity(
            EntityRole.CONE_1, MatchingCriteria.ROLE, entity_type=Cone
        )
        setup_actions = scene_registry.retrieve_actions(
            SceneActionFilter(SetupCheckedAction)
        )
        setup_action = setup_actions[0] if len(setup_actions) > 0 else None
        if not first_cone or setup_action is None:
            msg = (
                "Could not retrieve starting cone or SetupCheckedAction from scene"
                " registry. Make sure the SetupChecker ran prior to this plugin."
            )
            raise InvalidSetupError(msg)
        self._setup_direction = setup_action.setup_direction
        return first_cone, setup_action

    @staticmethod
    @hook_impl
    def node_register() -> None:
        """Register a RunStartActionRecognizer."""
        ObjectBuilder.direct_register(
            id_str="RunStartActionRecognizer",
            object_type=(
                "RunStartActionRecognizer",
                "cuju_action_runstart.p_run_start_recognizer",
            ),
            config_type=(
                "RunStartActionConfig",
                "cuju_action_runstart.p_run_start_recognizer",
            ),
        )

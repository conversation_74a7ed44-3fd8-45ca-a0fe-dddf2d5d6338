"""Test the pipe codes utility functions."""

from unittest.mock import <PERSON><PERSON>ock

import pytest
from imgeta.utils.pipe_codes import Pipe<PERSON>ode, update_pipe_codes


@pytest.fixture
def mock_state_store() -> MagicMock:
    """Fixture to create a mock state store."""
    return MagicMock()


def get_pipe_codes(param: str) -> dict[str, list[PipeCode]]:
    """Get pipe codes based on the parameter.

    Parameters
    ----------
    param : str
        The parameter to determine the pipe codes.

    Returns
    -------
    dict[str, list[PipeCodes]]
        A dictionary with the key "scene" and a list of PipeCodes.
    """
    if param == "all":
        return {
            "scene": [
                PipeCode.ROUTE_EVALUATION_FAILED,
                PipeCode.NOT_STANDING_STRAIGHT,
            ]
        }
    if param == "single":
        return {"scene": [PipeCode.ROUTE_EVALUATION_FAILED]}
    if param == "empty":
        return {}
    if param == "key_empty":
        return {"scene": []}
    if param == "none":
        return None
    msg = f"Invalid parameter: {param}"
    raise ValueError(msg)


@pytest.fixture
def mock_pipe_codes(request: pytest.FixtureRequest) -> dict[str, list[PipeCode]]:
    """Fixture to create a mock pipe codes dictionary."""
    param = "all" if request.param is None else request.param
    return get_pipe_codes(param)


@pytest.fixture
def expected_final(request: pytest.FixtureRequest) -> dict[str, list[PipeCode]]:
    """Fixture to create an expected final pipe codes dictionary."""
    param = "all" if request.param is None else request.param
    return get_pipe_codes(param)


@pytest.mark.unittest
class TestUpdatePipeCodes:
    """Test the update_pipe_codes function."""

    @pytest.mark.parametrize(
        ("mock_pipe_codes", "key", "new_codes", "expected_final"),
        [
            ("none", "scene", [PipeCode.ROUTE_EVALUATION_FAILED], "single"),
            ("empty", "scene", [PipeCode.ROUTE_EVALUATION_FAILED], "single"),
            ("key_empty", "scene", [PipeCode.ROUTE_EVALUATION_FAILED], "single"),
            ("single", "scene", [PipeCode.ROUTE_EVALUATION_FAILED], "single"),
            ("single", "scene", [PipeCode.NOT_STANDING_STRAIGHT], "all"),
            (
                "single",
                "scene",
                [PipeCode.ROUTE_EVALUATION_FAILED, PipeCode.NOT_STANDING_STRAIGHT],
                "all",
            ),
        ],
        indirect=["mock_pipe_codes", "expected_final"],
    )
    def test_update_pipe_codes_behavior(
        self,
        mock_pipe_codes: dict | None,
        key: str,
        new_codes: list,
        expected_final: dict,
        mock_state_store: MagicMock,
    ) -> None:
        """Test the behavior of update_pipe_codes function."""
        mock_state_store.get_state.return_value = mock_pipe_codes

        update_pipe_codes(mock_state_store, key, new_codes)

        mock_state_store.set_state.assert_called_once()
        args = mock_state_store.set_state.call_args[0]

        if args[0] != "pipe_codes":
            pytest.fail(f"Expected set_state with 'pipe_codes', got {args[0]}")

        if args[1] != expected_final:
            pytest.fail(f"Expected final codes {expected_final}, got {args[1]}")

    def test_does_nothing_if_state_store_is_none(self) -> None:
        """Test that update_pipe_codes does nothing if state_store is None."""
        with pytest.raises(RuntimeError, match="State store is None"):
            update_pipe_codes(None, "scene", [PipeCode.ROUTE_EVALUATION_FAILED])  # type: ignore

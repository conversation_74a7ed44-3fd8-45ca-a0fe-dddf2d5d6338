"""Fixtures for IR1RInferencer testing."""

from unittest.mock import Mock

import numpy as np
import pytest
from imgeta.plugin_base.bases.result_base import ResultBase
from imgeta.plugin_base.imgeta_tree.imgeta_node import ImgetaNodeBase
from imgeta.plugin_base.imgeta_tree.imgeta_views import ImgetaViews
from imgeta.plugin_base.transformer.ir1r_inferencer import IR1RInferencer


class ConcreteInferencer(IR1RInferencer):
    """Concrete implementation of IR1RInferencer for testing purposes."""

    def inference(self, imgs: list, states: dict, **kwargs: dict) -> tuple:
        """Implement the abstract method for testing.

        Args:
            imgs: List of input images
            states: State dictionary
            kwargs: Additional keyword arguments

        Returns
        -------
            Tuple containing processed results
        """
        _ = states, kwargs
        result = []
        for img in imgs:
            if img is not None:
                result.append(np.ones((10, 10), dtype=np.float32))
        return (result,)

    def postprocess(
        self, img: np.ndarray, node_state: dict, node_result: np.ndarray, **kwargs: dict
    ) -> dict:
        """Implement the abstract method for testing.

        Args:
            img: Input image
            node_state: Node state dictionary
            node_result: Result from node processing
            kwargs: Additional keyword arguments

        Returns
        -------
            Dictionary of processed results
        """
        _ = node_state
        result_map = {}
        if img is not None and node_result is not None:
            mock_result = Mock(spec=ResultBase)
            key = kwargs.get("key", "test_result")
            result_map[key] = mock_result
        return result_map

    @classmethod
    def get_exceptions_to_catch(cls) -> tuple[type[Exception]]:
        """Return exceptions to catch during inference.

        Returns
        -------
            Tuple of exception types to catch
        """
        return (Exception,)


@pytest.fixture
def mock_imgeta_views() -> Mock:
    """Create a mock ImgetaViews instance.

    Returns
    -------
        Mock object of ImgetaViews
    """
    mock_views = Mock(spec=ImgetaViews)
    mock_views.imgs = [np.ones((1280, 720, 3)), np.zeros((1280, 720, 3))]
    return mock_views


@pytest.fixture
def sample_img() -> np.ndarray:
    """Create a sample image for testing.

    Returns
    -------
        NumPy array representing an image
    """
    return np.ones((1280, 720, 3), dtype=np.uint8)


@pytest.fixture
def concrete_inferencer() -> ConcreteInferencer:
    """Create a concrete inferencer instance for testing.

    Returns
    -------
        Instance of ConcreteInferencer
    """
    return ConcreteInferencer(ids="test_inferencer")


@pytest.fixture
def concrete_inferencer_init_test() -> ConcreteInferencer:
    """Create a concrete inferencer instance for testing initialization.

    Returns
    -------
        Instance of ConcreteInferencer
    """
    return ConcreteInferencer(
        ids="test",
        max_num_outputs=5,
    )


@pytest.fixture
def concrete_inferencer_preprocess_test() -> ConcreteInferencer:
    """Create a concrete inferencer instance for testing preprocess method.

    Returns
    -------
        Instance of ConcreteInferencer
    """
    return ConcreteInferencer()


@pytest.fixture
def mock_node(sample_img: np.ndarray) -> Mock:
    """Create a mock ImgetaNodeBase instance.

    Returns
    -------
        Mock object of ImgetaNodeBase
    """
    mock = Mock(spec=ImgetaNodeBase)
    mock.img = sample_img
    mock.results = {}
    mock.meta = {}
    return mock

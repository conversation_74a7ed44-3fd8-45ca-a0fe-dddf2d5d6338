"""Unit tests for IR1RTransformer node."""

from __future__ import annotations

from typing import TYPE_CHECKING
from unittest.mock import patch

import pytest
from imgeta.plugin_base.bases.node_base import NodeBase
from imgeta.plugin_base.imgeta_tree.imgeta_node import ImgetaNode
from imgeta.plugin_base.imgeta_tree.imgeta_views import ImgetaViews
from imgeta.plugin_base.transformer.ir1r_transformer import IR1RTransformer

if TYPE_CHECKING:
    from imgeta.utils.image import ImageType


class MockTransformerImpl(IR1RTransformer):
    """Test implementation of IR1RTransformer."""

    @staticmethod
    def transform(img: ImageType, **kwargs: any) -> any:
        """Transform the image for testing purposes.

        Args:
            img: Input image.
            **kwargs: Additional keyword arguments.

        Returns
        -------
            Transformed result or None if input is None.
        """
        if img is None:
            return None
        return {"transformed": True, "original_shape": img.shape, **kwargs}


@pytest.mark.unittest
class TestIR1RTransformer:
    """Comprehensive test suite for IR1RTransformer class."""

    def test_initialization(self) -> None:
        """Test initialization of IR1RTransformer with various parameters."""
        transformer = MockTransformerImpl()
        if transformer._max_num_outputs is not None:  # noqa: SLF001
            pytest.fail("_max_num_outputs should be None")
        if transformer._args_config != {}:  # noqa: SLF001
            pytest.fail("_args_config should be an empty dictionary")

        custom_transformer = MockTransformerImpl(
            ids="custom_id",
            on_control_flag="test_flag",
            max_num_outputs=5,
            custom_arg="test",
        )
        if custom_transformer._max_num_outputs != 5:  # noqa: PLR2004, SLF001
            pytest.fail("_max_num_outputs should be 5")
        if custom_transformer.ids != "custom_id":
            pytest.fail("ids should be 'custom_id'")
        if custom_transformer._args_config != {}:  # noqa: SLF001
            pytest.fail("_args_config should be an empty dictionary")

    def test_transform_abstract_method(self) -> None:
        """Test that the base transform method raises NotImplementedError."""
        with pytest.raises(NotImplementedError):
            IR1RTransformer.transform(None)

    def test_iterate_nodes(
        self,
        transformer: MockTransformerImpl,
        imgeta_views: ImgetaViews,
        additional_node: ImgetaNode,
    ) -> None:
        """Test node iteration functionality.

        Args:
            transformer: Test transformer instance.
            imgeta_views: Test views instance.
            additional_node: Additional node fixture to append.
        """
        views = imgeta_views
        views.imgeta_nodes.append(additional_node)

        nodes = list(transformer._iterate_nodes(views))  # noqa: SLF001
        if len(nodes) != 2:  # noqa: PLR2004
            pytest.fail("Should have 2 nodes")
        for node in nodes:
            if not isinstance(node, ImgetaNode):
                pytest.fail("All items should be ImgetaNode instances")

    def test_local_apply_with_none_input(
        self, transformer: MockTransformerImpl
    ) -> None:
        """Test local_apply with None input.

        Args:
            transformer: Test transformer instance.
        """
        result = transformer._local_apply(None)  # noqa: SLF001
        if result != [None]:
            pytest.fail("Result should be None")

    def test_local_apply_with_empty_views(
        self, transformer: MockTransformerImpl
    ) -> None:
        """Test local_apply with empty views.

        Args:
            transformer: Test transformer instance.
        """
        empty_views = ImgetaViews()
        result = transformer._local_apply(empty_views)  # noqa: SLF001
        if result != [None]:
            pytest.fail("Result should be None")

    def test_basic_transformation(
        self, transformer: MockTransformerImpl, imgeta_views: ImgetaViews
    ) -> None:
        """Test basic transformation functionality.

        Args:
            transformer: Test transformer instance.
            imgeta_views: Test views instance.
        """
        views = imgeta_views
        node = views.imgeta_nodes[0]

        result = transformer._local_apply(views, kres_out="test_result")  # noqa: SLF001
        if result != [None]:
            pytest.fail("Result should be None")

        if "test_result" not in node.results:
            pytest.fail("'test_result' should be in node.results")
        if node.results["test_result"]["transformed"] is not True:
            pytest.fail("transformed should be True")
        if node.results["test_result"]["original_shape"] != node.img.shape:
            pytest.fail("original_shape should match node.img.shape")

    def test_transformation_with_config_args(
        self, transformer: MockTransformerImpl, imgeta_views: ImgetaViews
    ) -> None:
        """Test transformation with configuration arguments.

        Args:
            transformer: Test transformer instance.
            imgeta_views: Test views instance.
        """
        views = imgeta_views
        node = views.imgeta_nodes[0]

        transformer._args_config = {"config_arg": "config_value"}  # noqa: SLF001

        result = transformer._local_apply(views, kres_out="test_result")  # noqa: SLF001
        if result != [None]:
            pytest.fail("Result should be None")

        if node.results["test_result"]["config_arg"] != "config_value":
            pytest.fail("config_arg should be 'config_value'")

    def test_transformation_with_kres_in(
        self, transformer: MockTransformerImpl, imgeta_views: ImgetaViews
    ) -> None:
        """Test transformation with input results.

        Args:
            transformer: Test transformer instance.
            imgeta_views: Test views instance.
        """
        views = imgeta_views
        node = views.imgeta_nodes[0]

        node.results["input_key"] = {"data": "test_input_data"}

        result = transformer._local_apply(  # noqa: SLF001
            views, kres_out="test_result", kres_in="input_key"
        )
        if result != [None]:
            pytest.fail("Result should be None")

        if node.results["test_result"]["state"] != {"data": "test_input_data"}:
            pytest.fail("state should contain input data")

    def test_transformation_with_kmeta_in(
        self, transformer: MockTransformerImpl, imgeta_views: ImgetaViews
    ) -> None:
        """Test transformation with input metadata.

        Args:
            transformer: Test transformer instance.
            imgeta_views: Test views instance.
        """
        views = imgeta_views
        node = views.imgeta_nodes[0]

        if node.meta is None:
            node.meta = {}
        node.meta["meta_key"] = {"metadata": "test_meta_data"}

        result = transformer._local_apply(  # noqa: SLF001
            views, kres_out="test_result", kmeta_in="meta_key"
        )
        if result != [None]:
            pytest.fail("Result should be None")

        if node.results["test_result"]["meta"] != {"metadata": "test_meta_data"}:
            pytest.fail("meta should contain metadata")

    def test_transformation_with_none_image(
        self, transformer: MockTransformerImpl, imgeta_views: ImgetaViews
    ) -> None:
        """Test transformation with nodes having None images.

        Args:
            transformer: Test transformer instance.
            imgeta_views: Test views instance.
        """
        views = imgeta_views
        node = views.imgeta_nodes[0]

        node.img = None

        result = transformer._local_apply(views, kres_out="test_result")  # noqa: SLF001
        if result != [None]:
            pytest.fail("Result should be None")

        if "test_result" in node.results:
            pytest.fail("test_result should not be in node.results")

    def test_transformation_with_multiple_nodes(
        self, transformer: MockTransformerImpl, multi_views: ImgetaViews
    ) -> None:
        """Test transformation with multiple nodes.

        Args:
            transformer: Test transformer instance.
            multi_views: Test views instance with multiple nodes.
        """
        result = transformer._local_apply(multi_views, kres_out="test_result")  # noqa: SLF001
        if result != [None]:
            pytest.fail("Result should be None")

        for node in multi_views.imgeta_nodes:
            if "test_result" not in node.results:
                pytest.fail("test_result should be in node.results")
            if node.results["test_result"]["transformed"] is not True:
                pytest.fail("transformed should be True")
            if "original_shape" not in node.results["test_result"]:
                pytest.fail("original_shape should be in test_result")

    def test_validate_integration(
        self, transformer: MockTransformerImpl, imgeta_views: ImgetaViews
    ) -> None:
        """Test integration with NodeBase.validate_is_not_none.

        Args:
            transformer: Test transformer instance.
            imgeta_views: Test views instance.
        """
        with patch.object(
            NodeBase, "validate_is_not_none", return_value=False
        ) as mock_validate:
            result = transformer._local_apply(imgeta_views)  # noqa: SLF001
            if result != [None]:
                pytest.fail("Result should be None")
            mock_validate.assert_called_once()

    def test_end_to_end_transformation(
        self, transformer: MockTransformerImpl, prepared_node_with_inputs: ImgetaViews
    ) -> None:
        """Test complete end-to-end transformation workflow.

        Args:
            transformer: Test transformer instance.
            prepared_node_with_inputs: Views instance with prepared inputs.
        """
        views = prepared_node_with_inputs
        node = views.imgeta_nodes[0]

        transformer._args_config = {"config_param": "config_value"}  # noqa: SLF001
        result = transformer._local_apply(  # noqa: SLF001
            views,
            kres_out="output_result",
            kres_in="input_result",
            kmeta_in="input_meta",
            calculate_time=True,
        )
        if result != [None]:
            pytest.fail("Result should be None")

        if "output_result" not in node.results:
            pytest.fail("output_result should be in node.results")
        if node.results["output_result"]["transformed"] is not True:
            pytest.fail("transformed should be True")
        if node.results["output_result"]["original_shape"] != node.img.shape:
            pytest.fail("original_shape should match node.img.shape")
        if node.results["output_result"]["state"] != {"data": "result_value"}:
            pytest.fail("state should contain result data")
        if node.results["output_result"]["meta"] != {"data": "meta_value"}:
            pytest.fail("meta should contain meta data")
        if node.results["output_result"]["config_param"] != "config_value":
            pytest.fail("config_param should be 'config_value'")

    def test_uninitialized_meta_handling(
        self, transformer: MockTransformerImpl, imgeta_views: ImgetaViews
    ) -> None:
        """Test handling of uninitialized metadata.

        Args:
            transformer: Test transformer instance.
            imgeta_views: Test views instance.
        """
        views = imgeta_views
        node = views.imgeta_nodes[0]

        node.meta = None

        result = transformer._local_apply(  # noqa: SLF001
            views, kres_out="test_result", calculate_time=True
        )
        if result != [None]:
            pytest.fail("Result should be None")

        if node.meta is None:
            pytest.fail("node.meta should not be None")
        if not isinstance(node.meta, dict):
            pytest.fail("node.meta should be a dictionary")

    def test_combined_inputs(
        self, transformer: MockTransformerImpl, node_with_combined_inputs: ImgetaViews
    ) -> None:
        """Test using both result and meta inputs together.

        Args:
            transformer: Test transformer instance.
            node_with_combined_inputs: Views instance with both meta and result inputs.
        """
        views = node_with_combined_inputs
        node = views.imgeta_nodes[0]

        result = transformer._local_apply(  # noqa: SLF001
            views,
            kres_out="combined_result",
            kres_in="result_input",
            kmeta_in="meta_input",
        )
        if result != [None]:
            pytest.fail("Result should be None")
        if "combined_result" not in node.results:
            pytest.fail("combined_result should be in node.results")
        if node.results["combined_result"]["state"]["result_value"] != 456:  # noqa: PLR2004
            pytest.fail("state result_value should be 456")
        if node.results["combined_result"]["meta"]["meta_value"] != 123:  # noqa: PLR2004
            pytest.fail("meta meta_value should be 123")

    def test_missing_input_keys(
        self, transformer: MockTransformerImpl, imgeta_views: ImgetaViews
    ) -> None:
        """Test behavior with missing input keys.

        Args:
            transformer: Test transformer instance.
            imgeta_views: Test views instance.
        """
        views = imgeta_views
        node = views.imgeta_nodes[0]

        # Try to access non-existent keys
        result = transformer._local_apply(  # noqa: SLF001
            views,
            kres_out="test_result",
            kres_in="nonexistent_key",
            kmeta_in="nonexistent_meta",
        )
        if result != [None]:
            pytest.fail("Result should be None")

        if "test_result" not in node.results:
            pytest.fail("test_result should be in node.results")
        if node.results["test_result"]["transformed"] is not True:
            pytest.fail("transformed should be True")
        if "state" not in node.results["test_result"]:
            pytest.fail("state should be in test_result")
        if node.results["test_result"]["state"] is not None:
            pytest.fail("state should be None")
        if "meta" not in node.results["test_result"]:
            pytest.fail("meta should be in test_result")
        if node.results["test_result"]["meta"] is not None:
            pytest.fail("meta should be None")

"""Test frame time."""

from datetime import timedelta

import pytest
from imgeta.plugin_base.imgeta_tree.frame_time import FrameTime


def get_frame_time(frame_number: int, interpolated: float | None = None) -> FrameTime:
    """Get Frame Time."""
    return FrameTime(
        frame_number=frame_number,
        interpolated_frame_number=interpolated,
        frame_rate=30,
        frame_time=timedelta(seconds=1 / 30),
        time_since_start=timedelta(seconds=(frame_number) / 30),
    )


@pytest.mark.unittest
class TestFrameTime:
    """Test frame time."""

    @pytest.mark.parametrize(
        ("a", "b"),
        [
            (get_frame_time(10, 0.5), get_frame_time(10, 0.75)),
            (get_frame_time(9), get_frame_time(10)),
            pytest.param(
                get_frame_time(9), 1, marks=pytest.mark.xfail(raises=TypeError)
            ),
        ],
    )
    def test_lt(self, a: FrameTime, b: FrameTime) -> None:
        """Test less than."""
        if not a < b:
            pytest.fail(f"{a} is not less than {b} but it should be")

    @pytest.mark.parametrize(
        ("a", "b"),
        [
            (get_frame_time(10, 0.5), get_frame_time(10, 0.5)),  # equal
            (get_frame_time(10, 0.5), get_frame_time(10, 0.75)),  # less than
            pytest.param(
                get_frame_time(9), 1, marks=pytest.mark.xfail(raises=TypeError)
            ),
        ],
    )
    def test_le(self, a: FrameTime, b: FrameTime) -> None:
        """Test less than equal."""
        if not a <= b:
            pytest.fail(f"{a} is not less than or equal to {b} but it should be")

    @pytest.mark.parametrize(
        ("a", "b"),
        [
            (get_frame_time(10, 0.75), get_frame_time(10, 0.5)),
            (get_frame_time(11), get_frame_time(10)),
            pytest.param(
                get_frame_time(9), 1, marks=pytest.mark.xfail(raises=TypeError)
            ),
        ],
    )
    def test_gt(self, a: FrameTime, b: FrameTime) -> None:
        """Test greater than."""
        if not a > b:
            pytest.fail(f"{a} is not greater than {b} but it should be")

    @pytest.mark.parametrize(
        ("a", "b"),
        [
            (get_frame_time(10, 0.75), get_frame_time(10, 0.5)),
            (get_frame_time(10, 0.5), get_frame_time(10, 0.5)),
            pytest.param(
                get_frame_time(9), 1, marks=pytest.mark.xfail(raises=TypeError)
            ),
        ],
    )
    def test_ge(self, a: FrameTime, b: FrameTime) -> None:
        """Test greater than equal."""
        if not a >= b:
            pytest.fail(f"{a} is not greater than or equal to {b} but it should be")

    @pytest.mark.parametrize(
        ("a", "b"),
        [
            (get_frame_time(10, 0.5), get_frame_time(10, 0.5)),
            pytest.param(
                get_frame_time(9), 1, marks=pytest.mark.xfail(raises=TypeError)
            ),
        ],
    )
    def test_ne(self, a: FrameTime, b: FrameTime) -> None:
        """Test not equal."""
        if a != b:
            pytest.fail(f"{a} and {b} should be equal but are not")

    @pytest.mark.parametrize(
        ("a", "b"),
        [
            (get_frame_time(10, 0.5), get_frame_time(10, 0.25)),
            (get_frame_time(10), get_frame_time(11)),
            pytest.param(
                get_frame_time(9), 1, marks=pytest.mark.xfail(raises=TypeError)
            ),
        ],
    )
    def test_eq(self, a: FrameTime, b: FrameTime) -> None:
        """Test equal."""
        if a == b:
            pytest.fail(f"{a} and {b} should not be equal but are")

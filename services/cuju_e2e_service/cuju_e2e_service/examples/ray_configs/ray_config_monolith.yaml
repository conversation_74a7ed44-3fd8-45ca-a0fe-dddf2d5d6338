# https://docs.ray.io/en/latest/serve/production-guide/config.html#serve-in-production-config-file
# proxy_location: Disabled  # You can disable the HTTP processing

applications:
  - name: E2EMonolith
    import_path: cuju_e2e_service.app_builders.monolith:pipe_builder
    deployments:
      - name: RaySQSIngress
        autoscaling_config:
          min_replicas: 1
          max_replicas: 1
      - name: PipeRunnerDeployment
        autoscaling_config:
          min_replicas: 1
          max_replicas: 1
      - name: ProcessingEgress
        autoscaling_config:
          min_replicas: 1
          max_replicas: 1
      - name: TimeEstimationEgress
        autoscaling_config:
          min_replicas: 1
          max_replicas: 1
      - name: CujuMonolithRunner
        autoscaling_config:
          min_replicas: 1
          max_replicas: 1
      - name: TimeEstimation
        autoscaling_config:
          min_replicas: 1
          max_replicas: 1
    args:
      flow_watcher_args:
        enable_flow_watcher: true
        database_mode: "off"
        root_deployment_name: "RaySQSIngress"
        flush_interval: 10
      ray_sqs_ingress_args:
        source_queue_url: "https://sqs.eu-central-1.amazonaws.com/235103184542/ray-sandbox-in-queue-1"
        max_number_of_messages: 10
      egress_args:
        sqs_target_url_processing: "https://sqs.eu-central-1.amazonaws.com/235103184542/ray-sandbox-out-queue-1"
        sqs_target_url_estimated_time: "https://sqs.eu-central-1.amazonaws.com/235103184542/ray-sandbox-est-queue-1"
      pipeline_args:
        pipe_runner_params:
          pipe_config_file: "e2e-pipelines/scene/pipe_e2e_monolith-scene.json.j2"
          settings_name: "settings_loop"
          gc_frequency: 200
          settings_toml: "params_e2e.toml"
          pipe_config_args:
            use_local_input: false
            use_local_model: false
            use_local_output: false
            # -- settings_toml overrides using jinja variables --
            # data_bucket_name: ai-bucket-137068251345
            # model_store_bucket_name: ai-bucket-137068251345
            # vidproc_probe_store_video: true
            # ball_probe_store_video: true
            # person_probe_store_video: true

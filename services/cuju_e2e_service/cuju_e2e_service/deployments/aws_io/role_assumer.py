"""Class to assume roles for connecting to AWS S3 storage asynchronously."""

import enum
from typing import Any

from aiobotocore.client import AioBaseClient
from aiobotocore.credentials import AioRefreshableCredentials
from aiobotocore.session import get_session
from imgeta.utils.json_logger import configure_logger


class ServiceName(enum.Enum):
    """ServiceName to create AWS client for."""

    SNS = "sns"
    SQS = "sqs"


class AWSRoleAssumer:
    """AWS Role Assumer for AWS S3 Storage connection (async)."""

    def __init__(self, role_name: str | None) -> None:
        """Create a new instance of a RoleAssumer.

        Parameters
        ----------
        role_name : str | None
            Name of the role to be assumed.
        """
        self._role_name = role_name
        self._session = get_session()
        self._logger = configure_logger()

    async def _refresh_credentials(self) -> dict[str, Any]:
        """Refresh STS credentials for RefreshableCredentials (async)."""
        self._logger.info("Refreshing AWS STS assumed role credentials.")
        session = get_session()
        async with session.create_client("sts") as sts_client:
            identity = await sts_client.get_caller_identity()
            account_id = identity["Account"]
            role_arn = f"arn:aws:iam::{account_id}:role/{self._role_name}"

            assumed_role = await sts_client.assume_role(
                RoleArn=role_arn, RoleSessionName="RaySQSRoleSession"
            )
            credentials = assumed_role["Credentials"]

            return {
                "access_key": credentials["AccessKeyId"],
                "secret_key": credentials["SecretAccessKey"],
                "token": credentials["SessionToken"],
                "expiry_time": credentials["Expiration"].isoformat(),
            }

    async def create_client(self, service_name: ServiceName) -> AioBaseClient:
        """Create a client with automatically refreshing credentials (async)."""
        session = get_session()
        if self._role_name:
            refreshable_creds = AioRefreshableCredentials.create_from_metadata(
                metadata=await self._refresh_credentials(),
                refresh_using=self._refresh_credentials,
                method="sts-assume-role",
            )
            session._credentials = refreshable_creds  # noqa: SLF001
            return session.create_client(service_name.value)

        return session.create_client(service_name.value)

"""Exercise Runner Ray Deployment."""

import uuid
from typing import Any

from imgeta.plugin_basic.imgeta_dto import ImgetaDTO
from ray import serve
from ray.serve.handle import DeploymentHandle

from cuju_e2e_service.data_conversion.converter_registry import ConverterRegistry
from cuju_e2e_service.deployments.deployment_base import DeploymentBase
from cuju_e2e_service.schemas.backend.request import BackendRequest
from cuju_e2e_service.schemas.backend.response import AnalysisResultsResponse
from cuju_e2e_service.schemas.deployments import (
    ExceptionDetailsForResponse,
    ExceptionWithStackTrace,
    ImgetaDTOWithBackendRequest,
    ProcessingStatus,
    TimeEstimationInput,
)


@serve.deployment
class MockupRunner(DeploymentBase):
    """A class to run a mockup processing and time estimations deployments.

    This class inherits from AsyncPipeRunnerBase and is designed to handle
    asynchronous predictions using Ray Serve deployments. It takes in the handles
    of the individual pipeline deployments and orchestrates their predictions.
    """

    def __init__(
        self,
        processing_mock: DeploymentHandle,
        time_estimation_mock: DeploymentHandle,
        processing_egress: DeploymentHandle,
        time_estimation_egress: DeploymentHandle,
        *args: Any,
        **kwargs: Any,
    ) -> None:
        """Initialize the runner with the handles of the pipeline deployments.

        Parameters
        ----------
        processing_mock : DeploymentHandle
            Handle to the mock processing deployment.
        time_estimation_mock : DeploymentHandle
            Handle to the estimated processing time deployment.
        processing_egress : DeploymentHandle
            Handle to process the output of the pipeline.
        time_estimation_egress : DeploymentHandle
            Handle to time estimation output of the pipeline.
        """
        super().__init__(*args, **kwargs)
        self._processing_mock = processing_mock
        self._time_estimation_mock = time_estimation_mock
        self._processing_egress = processing_egress
        self._time_estimation_egress = time_estimation_egress

    async def _process(
        self,
        inputs: BackendRequest,
        flow_uuid: uuid.UUID,
    ) -> ProcessingStatus:
        """Asynchronously predict using the deployments.

        Parameters
        ----------
        inputs : Dict
            The input data to be processed
        flow_uuid : uuid.UUID
            The Flow UUID used for to be deployment monitoring

        Returns
        -------
        Dict
            A dict object containing all relevant metadata.
        """
        try:
            time_estimation_input = ConverterRegistry.convert(
                inputs, TimeEstimationInput
            )
            time_estimation_response = self._time_estimation_mock.remote(
                time_estimation_input, flow_uuid=flow_uuid
            )
            time_estimation_output = self._time_estimation_egress.remote(
                time_estimation_response, flow_uuid=flow_uuid
            )

            video_processing_input = ConverterRegistry.convert(inputs, ImgetaDTO)
            processing_response: ImgetaDTO = await self._processing_mock.remote(
                video_processing_input, flow_uuid=flow_uuid
            )

            analysis_result = ConverterRegistry.convert(
                ImgetaDTOWithBackendRequest(
                    backend_request=inputs,
                    imgeta_dto=processing_response,
                ),
                AnalysisResultsResponse,
            )
            processing_output = self._processing_egress.remote(
                analysis_result, flow_uuid=flow_uuid
            )

            await time_estimation_output
            await processing_output
        except Exception as e:  # noqa: BLE001
            exception_details_for_response = ExceptionDetailsForResponse(
                request_id=str(inputs.id),
                exercise_event_id=str(inputs.data.exercise_event_id),
                exception=e,
            )
            exception_response = ConverterRegistry.convert(
                exception_details_for_response, AnalysisResultsResponse
            )
            await self._processing_egress.remote(
                exception_response, flow_uuid=flow_uuid
            )
            exception_details = ExceptionWithStackTrace.from_exception(e)
            return ProcessingStatus(success=False, exception_details=exception_details)

        return ProcessingStatus(success=True)

"""Converter functions to different backend response types."""

from dataclasses import dataclass
from typing import Any, Final

from imgeta.utils.exceptions import (
    CUJUError,
    InitialConesOutOfImageError,
    InvalidVideoOrientationError,
    InvalidVideoResolutionError,
    JSONFileNotFoundError,
    NoBallDetectionError,
    NoPersonDetectionError,
    NoPipelineFoundError,
    S3UploadError,
)
from imgeta.utils.pipe_codes import PipeCode
from loguru import logger as logging
from ray.exceptions import RayTaskError

from cuju_e2e_service.data_conversion.converter_registry import ConverterRegistry
from cuju_e2e_service.schemas.backend.estimated_time import (
    EstimatedTimeData,
    EstimatedTimeResponse,
)
from cuju_e2e_service.schemas.backend.response import (
    AnalysisResultsData,
    AnalysisResultsResponse,
    Count,
    Distance,
    MeasurementData,
    Time,
    WarningItem,
)
from cuju_e2e_service.schemas.backend.status_codes import (
    STATUS_CODE_PRIORITY_MAP,
    StatusCode,
)
from cuju_e2e_service.schemas.backend.warnings import (
    WarningFPS,
    WarningNotCountedPasses,
    WarningPersonCloseToStartCone,
)
from cuju_e2e_service.schemas.deployments import (
    ExceptionDetailsForResponse,
    ImgetaDTOWithBackendRequest,
)


# ==============================================================================
# Internal data structures
# ==============================================================================
@dataclass
class ExtractedErrorsAndWarnings:
    """Extracted errors and warnings."""

    warnings: list[WarningItem] | None
    errors: list[StatusCode] | None


@dataclass
class PipeCodeToWarningCfg:
    """Pipe code to warnings mapping.

    Attributes
    ----------
    warning_item: type[WarningItem]
      Class of warning item that contains the desired warning_id
    pipe_metric_key: str
      Key for warning item in pipe_metrics to be used as warning value
    rounding_precision: int | None
        the number of decimal places the value should be rounded to. If None,
        no rounding is applied.
    """

    warning_item: type[WarningItem]
    pipe_metric_key: str
    rounding_precision: int | None = None


# ==============================================================================
# Exceptions and Pipe Codes to Errors, Warnings and Status code mappings
# ==============================================================================

EXCEPTION_TO_STATUS: Final[dict[type[CUJUError], StatusCode]] = {
    InitialConesOutOfImageError: StatusCode.CONES_OUT_OF_FRAME,
    InvalidVideoResolutionError: StatusCode.VIDEO_RESOLUTION_LOW,
    InvalidVideoOrientationError: StatusCode.VIDEO_IN_PORTRAIT_MODE,
    NoBallDetectionError: StatusCode.BALL_NOT_DETECTED_IN_VIDEO,
    NoPersonDetectionError: StatusCode.PERSON_NOT_DETECTED_IN_VIDEO,
    NoPipelineFoundError: StatusCode.LAB_EXERCISE,
    JSONFileNotFoundError: StatusCode.UNEXPECTED_ERROR,
    S3UploadError: StatusCode.UNEXPECTED_ERROR,
}


PIPE_CODE_TO_STATUS_CODE = {
    PipeCode.ROUTE_EVALUATION_FAILED: StatusCode.ROUTE_EVALUATION_FAILED,
    PipeCode.NOT_STANDING_STRAIGHT: StatusCode.NO_STAND_STRAIGHT,
    PipeCode.INVALID_SETUP: StatusCode.CONE_POSITIONS_NOT_AS_EXPECTED,
    PipeCode.EARLY_START: StatusCode.EARLY_START,
    PipeCode.DISTANCE_GREATER_THAN_REQ: StatusCode.DISTANCE_GREATER_THAN_REQ,
    PipeCode.DISTANCE_SMALLER_THAN_REQ: StatusCode.DISTANCE_SMALLER_THAN_REQ,
    PipeCode.RUN_UP: StatusCode.PERSON_FAR_FROM_START_CONE,
    PipeCode.RESULT_EXCEEDS_SANITY_VALUES: StatusCode.EXERCISE_EVALUATION_FAILED,
    PipeCode.MAIN_BALL_NOT_VISIBLE: StatusCode.BALL_NOT_VERIFIED_IN_2_5_SECS,
    PipeCode.UNBALANCED_LANDING: StatusCode.JUMP_LANDING_UNBALANCED,
    PipeCode.MOVEMENT_BEFORE_JUMP: StatusCode.JUMP_START_POSITION_NOT_STATIONARY,
    PipeCode.SQUARE_WRONG_START: StatusCode.SQUARE_WRONG_START,
    PipeCode.SQUARE_START_POSITION_WRONG: StatusCode.SQUARE_START_POSITION_WRONG,
    PipeCode.FULL_BODY_NOT_VISIBLE_IN_STAND_STRAIGHT: StatusCode.FULL_BODY_NOT_VISIBLE_IN_STAND_STRAIGHT,  # noqa: E501
    PipeCode.WRONG_EXERCISE_UPLOAD: StatusCode.WRONG_EXERCISE_UPLOADED,
    PipeCode.START_TRIGGER_NOT_FOUND: StatusCode.PERSON_NOT_DETECTED_WHEN_CROSS_START_CONE,  # noqa: E501
    PipeCode.END_TRIGGER_NOT_FOUND: StatusCode.PERSON_NOT_IDENTIFIED_BY_EXERCISE_END,
    PipeCode.RECORDING_STOPPED_BEFORE_EXERCISE_DONE: StatusCode.RECORDING_STOPPED_BEFORE_EXERCISE_DONE,  # noqa: E501
    PipeCode.MAIN_PERSON_NOT_DETECTED: StatusCode.MAIN_PERSON_NOT_DETECTED,
    PipeCode.PERSON_NOT_PRESENT_NEAR_CONES: StatusCode.PERSON_NOT_PRESENT_NEAR_CONES,
    PipeCode.START_POS_DET_FAILED: StatusCode.START_POS_DET_FAILED,
    PipeCode.CONE_POSITION_NOT_UNIFORM: StatusCode.CONE_POSITION_NOT_UNIFORM,
    PipeCode.JUMP_EVALUATION_FAILED: StatusCode.JUMP_EVALUATION_FAILED,
    PipeCode.INVALID_CONES_NUMBER: StatusCode.INVALID_CONES_NUMBER,
    PipeCode.CONES_NOT_ALIGNED: StatusCode.CONES_NOT_ALIGNED,
    PipeCode.RUN_START_NOT_DETECTED: StatusCode.UNEXPECTED_ERROR,
}


PIPE_CODE_TO_WARNING_MAPPING = {
    PipeCode.VIDEO_INVALID_FPS: PipeCodeToWarningCfg(
        warning_item=WarningFPS,
        pipe_metric_key="frame_rate",
        rounding_precision=0,
    ),
    PipeCode.PERSON_CLOSE_TO_START_CONE: PipeCodeToWarningCfg(
        warning_item=WarningPersonCloseToStartCone,
        pipe_metric_key="distance_to_start_cone",
        rounding_precision=0,
    ),
    PipeCode.WARN_INVALID_PASSES: PipeCodeToWarningCfg(
        warning_item=WarningNotCountedPasses,
        pipe_metric_key="not_counted_passes",
    ),
}


# ==============================================================================
# Local functions
# ==============================================================================


def _scene_result_dict_to_measurement_data(scene_result: dict) -> MeasurementData:
    """Convert scene result dictionary to MeasurementData.

    Parameters
    ----------
    scene_result : dict
        The scene result dictionary.
        Currently only supports measurement_type of time, distance, and count.

        The "measurement_unit" is not included in the conversion. The default units
        are defined in the MeasurementData class.

        Example scene_result:
            {
                "measurement_type": "time",
                "measurement": 2.5,
                "measurement_unit": "s",  # currently not used.
            }

    Returns
    -------
    MeasurementData
        The converted MeasurementData object.
    """
    if scene_result is None:
        return MeasurementData()
    measurement_type_mapping = {
        "time": Time,
        "distance": Distance,
        "count": Count,
    }

    measurement_type = scene_result["measurement_type"]
    measurement_item_model = measurement_type_mapping[measurement_type]

    measurement_item_dict = {
        measurement_type: measurement_item_model(value=scene_result["measurement"])
    }
    return MeasurementData(**measurement_item_dict)


def _flatten_pipe_codes_dict_to_list(pipe_codes: dict) -> list[PipeCode]:
    """Flatten pipe codes dictionary to a list.

    Parameters
    ----------
    pipe_codes : dict
        The pipe codes dictionary.

    Returns
    -------
    list[int]
        The flattened list of errors.
    """
    if not pipe_codes:
        return []

    flattened_pipe_codes_list = []
    for pipe_code_list in pipe_codes.values():
        if pipe_code_list:
            flattened_pipe_codes_list.extend(pipe_code_list)
    return flattened_pipe_codes_list


def _process_errors_and_warnings(
    pipe_codes: dict | None, pipe_metrics: dict | None
) -> ExtractedErrorsAndWarnings:
    """Process codes and metrics into structured warnings and errors."""
    pipe_codes = pipe_codes or {}
    pipe_metrics = pipe_metrics or {}

    warnings = []
    errors = []

    for code in _flatten_pipe_codes_dict_to_list(pipe_codes):
        error = _extract_error(code)
        if error:
            errors.append(error)
            continue

        warning = _extract_warning(code, pipe_metrics)
        if warning:
            warnings.append(warning)

    return ExtractedErrorsAndWarnings(
        warnings=warnings or None,
        errors=errors or None,
    )


def _extract_error(code: PipeCode) -> str | None:
    """Extract error message from pipe code."""
    return PIPE_CODE_TO_STATUS_CODE.get(code)


def _extract_warning(code: PipeCode, pipe_metrics: dict) -> Any | None:
    """Extract warning message from pipe code."""
    mapping = PIPE_CODE_TO_WARNING_MAPPING.get(code)
    if not mapping:
        return None

    value = pipe_metrics.get(mapping.pipe_metric_key)
    if value is None:
        logging.warning(
            f"Warning value not found for pipe metric key: {mapping.pipe_metric_key}"
        )
        return mapping.warning_item(value="N/A")

    if mapping.rounding_precision is not None:
        value = _round_value(value, mapping.rounding_precision)

    return mapping.warning_item(value=str(value))


def _round_value(value: float, precision: int) -> float:
    """Round a float value to a specified precision."""
    return round(value) if precision == 0 else round(value, precision)


# ==============================================================================
# Registered converters
# ==============================================================================


@ConverterRegistry.register(ImgetaDTOWithBackendRequest, AnalysisResultsResponse)
def imgeta_dto_to_analysis_result(
    imgeta_dto_with_backend_request: ImgetaDTOWithBackendRequest,
) -> AnalysisResultsResponse:
    """Convert ImgetaDTOWithBackendRequest to AnalysisResultsResponse."""
    backend_request = imgeta_dto_with_backend_request.backend_request
    request_id = backend_request.id
    exercise_event_id = str(backend_request.data.exercise_event_id)

    imgeta_dto = imgeta_dto_with_backend_request.imgeta_dto
    scene_result = imgeta_dto.first_meta["scene_result"]
    pipe_codes = imgeta_dto.first_meta["pipe_codes"]
    pipe_metrics = imgeta_dto.first_meta["pipe_metrics"]

    measurement_data = _scene_result_dict_to_measurement_data(scene_result)

    additional_values = {
        **(scene_result.get("additional_values", {}) if scene_result else {}),
        "request_id": request_id,
    }

    errors_and_warnings = _process_errors_and_warnings(pipe_codes, pipe_metrics)

    if errors_and_warnings.errors:
        # assign the highest priority error code to the status code
        status_code = min(
            errors_and_warnings.errors, key=lambda x: STATUS_CODE_PRIORITY_MAP[x]
        )
        errors = [error.value for error in errors_and_warnings.errors]
    else:
        status_code = StatusCode.SUCCESS.value
        errors = None

    data = AnalysisResultsData(
        exercise_event_id=exercise_event_id,
        status_code=status_code,
        errors=errors,
        warnings=errors_and_warnings.warnings,
        measurement_data=measurement_data,
        additional_values=additional_values,
    )
    logging.debug(f"data: {data}")
    return AnalysisResultsResponse(data=data)


@ConverterRegistry.register(ExceptionDetailsForResponse, AnalysisResultsResponse)
def exception_details_to_analysis_results_response(
    exception_details: ExceptionDetailsForResponse,
) -> AnalysisResultsResponse:
    """Convert ExceptionDetailsForResponse to AnalysisResultsResponse.

    Parameters
    ----------
    exception_details : ExceptionDetailsForResponse
        The exception details to convert.

    Returns
    -------
    AnalysisResultsResponse
        The AnalysisResultsResponse with the converted status code and error message.
    """
    raised_exception = exception_details.exception
    if isinstance(raised_exception, RayTaskError):
        raised_exception = raised_exception.cause

    try:
        if isinstance(raised_exception, CUJUError):
            status_code = EXCEPTION_TO_STATUS[type(raised_exception)]
            error_message = str(raised_exception)
        else:
            status_code = StatusCode.UNEXPECTED_ERROR
            error_message = "Unexpected exception raised."
    except KeyError:
        status_code = StatusCode.UNEXPECTED_ERROR
        error_message = "Unmapped CUJU exception to a status code."

    additional_values = {
        "request_id": exception_details.request_id,
    }

    data = AnalysisResultsData(
        exercise_event_id=exception_details.exercise_event_id,
        status_code=status_code.value,
        error_message=error_message,
        warnings=None,
        errors=[status_code.value],
        measurement_data=MeasurementData(),
        additional_values=additional_values,
    )
    return AnalysisResultsResponse(data=data)


@ConverterRegistry.register(EstimatedTimeData, EstimatedTimeResponse)
def estimated_data_to_estimated_time_response(
    estimated_data: EstimatedTimeData,
) -> EstimatedTimeResponse:
    """Convert EstimatedTimeData to EstimatedTimeResponse."""
    return EstimatedTimeResponse(data=estimated_data)

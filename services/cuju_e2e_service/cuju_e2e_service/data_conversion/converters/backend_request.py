"""Converter functions from backend request."""

from imgeta.plugin_basic.imgeta_dto import ImgetaD<PERSON>

from cuju_e2e_service.data_conversion.converter_registry import ConverterRegistry
from cuju_e2e_service.schemas.backend.request import BackendRequest
from cuju_e2e_service.schemas.deployments import (
    TimeEstimationInput,
)


@ConverterRegistry.register(BackendRequest, TimeEstimationInput)
def backend_request_to_time_estimation_input(
    backend_request: BackendRequest,
) -> TimeEstimationInput:
    """Convert BackendRequest to TimeEstimationInput."""
    return TimeEstimationInput(
        exercise_event_id=backend_request.data.exercise_event_id,
        exercise_id=backend_request.data.exercise_id,
    )


@ConverterRegistry.register(BackendRequest, ImgetaDTO)
def backend_request_to_video_processing_input(
    backend_request: BackendRequest,
) -> ImgetaDTO:
    """Convert BackendRequest to ImgetaDTO."""
    pipe_request = {
        "exercise_event_id": str(backend_request.data.exercise_event_id),
        "exercise_id": backend_request.data.exercise_id,
        "video_file_path": backend_request.data.bucket_original_video.key,
        "video_bucket_name": backend_request.data.bucket_original_video.name,
        "user_height": backend_request.data.user_height,
        "start_frame": backend_request.data.meta_data.start_frame,
        "output_data_prefix": backend_request.data.bucket_ai_videos.key_prefix,
        "output_data_bucket_name": backend_request.data.bucket_ai_videos.name,
        "cones_coords": [
            cone.model_dump() for cone in backend_request.data.meta_data.cones_position
        ],
        "timestamp": backend_request.data.timestamp,
        "birthday": backend_request.data.birthday,
        "gender": backend_request.data.gender,
    }
    meta_dict = {
        "pipe_request": pipe_request,
        "pipe_states": {},
        "pipe_codes": {},
    }
    return ImgetaDTO(meta=[meta_dict])

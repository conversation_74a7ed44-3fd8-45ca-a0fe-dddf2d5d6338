"""A Ray Serve Application Builder for a Distributed E2E Pipeline Service."""

import json
import uuid

import yaml
from imgeta.plugin_basic.imgeta_dto import ImgetaDTO
from loguru import logger
from ray import serve

from cuju_e2e_service import PATH_TO_MODULE_ROOT_DIRECTORY
from cuju_e2e_service.app_builders.common import (
    CommonApplicationArguments,
    PipelineArgs,
    initialize_actors,
    initialize_egress_deployments,
    initialize_sqs_ingress_deployment,
    initialize_time_estimation_deployment,
)
from cuju_e2e_service.deployments.orchestrators.exercise_runner import (
    CujuExerciseRunner,
)
from cuju_e2e_service.deployments.workers.pipe_runner import PipeRunnerDeployment
from cuju_e2e_service.schemas.backend.request import BackendRequest
from cuju_e2e_service.schemas.deployments import ProcessingStatus


class DistributedApplicationArguments(CommonApplicationArguments):
    """Arguments for configuring the distributed application.

    Attributes
    ----------
    pipelines : dict[str, PipelineArgs]
        A dictionary of pipeline names and their respective arguments.
    """

    pipelines: dict[str, PipelineArgs]


def _build_cuju_deployments(
    pipelines_config: dict[str, PipelineArgs],
    *,
    enable_flow_watcher: bool,
) -> dict:
    """Build the Ray Serve deployments for the specified pipeline configurations.

    Parameters
    ----------
    pipelines_args : dict[str, bool | dict[str, str | int]]
        The arguments for the pipeline deployments, where the keys are the
        pipeline names and the values are either a verbose boolean, or a dictionary
        containing the pipeline arguments.

    Returns
    -------
    serve.Application
        The Ray Serve application instance, ready for deployment, with configured
        inference pipelines.
    """
    pipeline_deployments = {}
    for pipeline_name, pipeline_args in pipelines_config.items():
        pipeline_deployments[pipeline_name] = PipeRunnerDeployment.options(  # type: ignore
            name=pipeline_name,
        ).bind(
            app_name=pipeline_name,
            pipe_runner_params=pipeline_args.pipe_runner_params,
            input_model=ImgetaDTO,
            output_model=ImgetaDTO,
            enable_flow_watcher=enable_flow_watcher,
        )

    return pipeline_deployments


def pipe_builder(args: dict) -> serve.Application:
    """Build and deploy the inference server with specified pipelines and services.

    Parameters
    ----------
    args : dict
        The arguments for building and deploying the inference server. The dictionary
        should be compatible with the `DistributedApplicationArguments` pydantic model.

    Returns
    -------
    serve.Application
        The Ray Serve application instance, ready for deployment, with configured
        inference pipelines and API ingress.
    """
    app_args = DistributedApplicationArguments(**args)
    initialize_actors(app_args.flow_watcher_args)

    egress_deployments = initialize_egress_deployments(
        args=app_args.egress_args,
        enable_flow_watcher=app_args.flow_watcher_args.enable_flow_watcher,
    )

    time_estimation = initialize_time_estimation_deployment(
        enable_flow_watcher=app_args.flow_watcher_args.enable_flow_watcher,
    )

    pipeline_deployments = _build_cuju_deployments(
        app_args.pipelines,
        enable_flow_watcher=app_args.flow_watcher_args.enable_flow_watcher,
    )

    cuju_exercise_runner = CujuExerciseRunner.bind(  # type: ignore
        **pipeline_deployments,
        time_estimation=time_estimation,
        processing_egress=egress_deployments.processing,
        time_estimation_egress=egress_deployments.time_estimation,
        input_model=BackendRequest,
        output_model=ProcessingStatus,
        enable_flow_watcher=app_args.flow_watcher_args.enable_flow_watcher,
    )

    return initialize_sqs_ingress_deployment(
        processing_deployment=cuju_exercise_runner,
        args=app_args.ray_sqs_ingress_args,
        enable_flow_watcher=app_args.flow_watcher_args.enable_flow_watcher,
    )


def _local_main() -> None:
    """Run the local inference server.

    This function sets up a local Ray cluster, runs the inference server, and
    predicts using the provided example request.

    The Ray Serve is configured to run in local testing mode, which means that the
    deployments will run in a single process but within multiple threads. This allows
    for quick testing and debugging.
    """
    ray_config_path = (
        PATH_TO_MODULE_ROOT_DIRECTORY
        / "examples"
        / "ray_configs"
        / "ray_config_distributed.yaml"
    )
    with ray_config_path.open() as f:
        ray_config = yaml.safe_load(f)

    args = ray_config["applications"][0]["args"]
    app = _build_cuju_deployments(
        args["pipelines"],
        enable_flow_watcher=False,
    )

    example_request_path = (
        PATH_TO_MODULE_ROOT_DIRECTORY
        / "examples"
        / "requests"
        / "example_backend_request.json"
    )
    with example_request_path.open() as f:
        request_dict = json.load(f)

    handle = serve.run(
        app,
        _local_testing_mode=True,
    )

    response = handle.remote(
        BackendRequest(**request_dict),
        uuid.uuid4(),
    )
    logger.debug(
        f"Output Object: {response.result().model_dump_json()}",
    )


if __name__ == "__main__":
    _local_main()

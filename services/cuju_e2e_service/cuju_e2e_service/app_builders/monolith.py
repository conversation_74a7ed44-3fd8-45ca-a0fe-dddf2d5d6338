"""A Ray Serve Application Builder for a Monolith E2E Pipeline Service."""

import json
import uuid
from typing import Any

import yaml
from imgeta.plugin_basic.imgeta_dto import ImgetaDTO
from loguru import logger
from ray import serve

from cuju_e2e_service import PATH_TO_MODULE_ROOT_DIRECTORY
from cuju_e2e_service.app_builders.common import (
    CommonApplicationArguments,
    PipelineArgs,
    initialize_actors,
    initialize_egress_deployments,
    initialize_sqs_ingress_deployment,
    initialize_time_estimation_deployment,
)
from cuju_e2e_service.data_conversion.converter_registry import ConverterRegistry
from cuju_e2e_service.deployments.orchestrators.monolith_runner import (
    CujuMonolithRunner,
)
from cuju_e2e_service.deployments.workers.pipe_runner import PipeRunnerDeployment
from cuju_e2e_service.schemas.backend.request import BackendRequest
from cuju_e2e_service.schemas.backend.response import AnalysisResultsResponse
from cuju_e2e_service.schemas.deployments import (
    ImgetaDTOWithBackendRequest,
    ProcessingStatus,
)


class MonolithApplicationArguments(CommonApplicationArguments):
    """Model representing the arguments for building the Monolith E2E Pipeline Service.

    Attributes
    ----------
    pipeline_args : PipelineArgs
        The arguments for configuring the pipeline deployments.
    """

    pipeline_args: PipelineArgs


def pipe_builder(args: dict[str, Any]) -> serve.Application:
    """Build and deploy the inference server with specified pipelines and services.

    Parameters
    ----------
    args : dict[str, Any]
        The arguments for building and deploying the inference server. The dictionary
        should be compatible with the `MonolithApplicationArguments` pydantic model.

    Returns
    -------
    serve.Application
        The Ray Serve application instance, ready for deployment, with configured
        inference pipelines and API ingress.
    """
    app_args = MonolithApplicationArguments(**args)
    initialize_actors(app_args.flow_watcher_args)

    egress_deployments = initialize_egress_deployments(
        args=app_args.egress_args,
        enable_flow_watcher=app_args.flow_watcher_args.enable_flow_watcher,
    )

    time_estimation = initialize_time_estimation_deployment(
        enable_flow_watcher=app_args.flow_watcher_args.enable_flow_watcher,
    )

    runner_deployment = PipeRunnerDeployment.bind(  # type: ignore
        app_name="e2e_pipeline_monolith",
        pipe_runner_params=app_args.pipeline_args.pipe_runner_params,
        input_model=ImgetaDTO,
        output_model=ImgetaDTO,
        enable_flow_watcher=app_args.flow_watcher_args.enable_flow_watcher,
    )

    cuju_monolith_runner = CujuMonolithRunner.bind(  # type: ignore
        end_to_end_processing=runner_deployment,
        time_estimation=time_estimation,
        processing_egress=egress_deployments.processing,
        time_estimation_egress=egress_deployments.time_estimation,
        input_model=BackendRequest,
        output_model=ProcessingStatus,
        enable_flow_watcher=app_args.flow_watcher_args.enable_flow_watcher,
    )

    return initialize_sqs_ingress_deployment(
        processing_deployment=cuju_monolith_runner,
        args=app_args.ray_sqs_ingress_args,
        enable_flow_watcher=app_args.flow_watcher_args.enable_flow_watcher,
    )


def _local_main() -> None:
    """Run the local inference server.

    This function sets up a local Ray cluster, runs the inference server, and
    predicts using the provided example request.

    The Ray Serve is configured to run in local testing mode, which means that the
    deployments will run in a single process but within multiple threads. This allows
    for quick testing and debugging.
    """
    print("test d")
    ray_config_path = (
        PATH_TO_MODULE_ROOT_DIRECTORY
        / "examples"
        / "ray_configs"
        / "ray_config_monolith.yaml"
    )
    with ray_config_path.open() as f:
        ray_config = yaml.safe_load(f)

    args = ray_config["applications"][0]["args"]
    app_args = MonolithApplicationArguments(**args)
    app = PipeRunnerDeployment.bind(  # type: ignore
        app_name="e2e_pipeline_monolith",
        pipe_runner_params=app_args.pipeline_args.pipe_runner_params,
        input_model=ImgetaDTO,
        output_model=ImgetaDTO,
        enable_flow_watcher=False,
    )

    example_request_path = (
        PATH_TO_MODULE_ROOT_DIRECTORY
        / "examples"
        / "requests"
        / "example_backend_request_ttest.json"
        # / "example_backend_request_ttest_with_ball.json"
    )
    with example_request_path.open() as f:
        request_dict = json.load(f)

    handle = serve.run(app, _local_testing_mode=True)

    backend_request = BackendRequest(**request_dict)
    imgeta_dto_input = ConverterRegistry.convert(backend_request, ImgetaDTO)
    response = handle.remote(
        imgeta_dto_input,
        uuid.uuid4(),
    )
    result: ImgetaDTO = response.result()

    analysis_result = ConverterRegistry.convert(
        ImgetaDTOWithBackendRequest(
            backend_request=backend_request,
            imgeta_dto=result,
        ),
        AnalysisResultsResponse,
    )
    logger.info(f"Final Output Object: {analysis_result}")


if __name__ == "__main__":
    _local_main()

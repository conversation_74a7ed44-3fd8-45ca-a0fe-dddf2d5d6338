"""A Ray Serve Application Builder for an Backend Mock Service."""

from typing import Any

from imgeta.plugin_basic.imgeta_dto import ImgetaDTO
from ray import serve

from cuju_e2e_service.app_builders.common import (
    CommonApplicationArguments,
    initialize_actors,
    initialize_egress_deployments,
    initialize_sqs_ingress_deployment,
)
from cuju_e2e_service.deployments.orchestrators.mockup_runner import <PERSON><PERSON>up<PERSON>un<PERSON>
from cuju_e2e_service.deployments.workers.backend_mock import ProcessingMock
from cuju_e2e_service.schemas.backend.estimated_time import (
    EstimatedTimeData,
)
from cuju_e2e_service.schemas.backend.request import BackendRequest
from cuju_e2e_service.schemas.deployments import ProcessingStatus, TimeEstimationInput
from cuju_e2e_service.time_estimation.deployment import TimeEstimation


class BackendMockApplicationArguments(CommonApplicationArguments):
    """Arguments for configuring the backend mock application."""


def pipe_builder(args: dict[str, Any]) -> serve.Application:
    """Build and deploy the mockup deployments.

    Parameters
    ----------
    args : dict[str, Any]
        The arguments for building and deploying the inference server. The dictionary
        should be compatible with the `BackendMockApplicationArguments` pydantic model.

    Returns
    -------
    serve.Application
        The Ray Serve application instance, ready for deployment, with configured
        inference pipelines and API ingress.
    """
    app_args = BackendMockApplicationArguments(**args)
    initialize_actors(app_args.flow_watcher_args)

    processing_mock = ProcessingMock.bind(  # type: ignore
        input_model=ImgetaDTO,
        output_model=ImgetaDTO,
        enable_flow_watcher=app_args.flow_watcher_args.enable_flow_watcher,
    )
    estimated_time_mock = TimeEstimation.bind(  # type: ignore
        input_model=TimeEstimationInput,
        output_model=EstimatedTimeData,
        enable_flow_watcher=app_args.flow_watcher_args.enable_flow_watcher,
    )
    egress_deployments = initialize_egress_deployments(
        args=app_args.egress_args,
        enable_flow_watcher=app_args.flow_watcher_args.enable_flow_watcher,
    )

    mockup_runner = MockupRunner.bind(  # type: ignore
        processing_mock=processing_mock,
        time_estimation_mock=estimated_time_mock,
        processing_egress=egress_deployments.processing,
        time_estimation_egress=egress_deployments.time_estimation,
        input_model=BackendRequest,
        output_model=ProcessingStatus,
        enable_flow_watcher=app_args.flow_watcher_args.enable_flow_watcher,
    )

    return initialize_sqs_ingress_deployment(
        processing_deployment=mockup_runner,
        args=app_args.ray_sqs_ingress_args,
        enable_flow_watcher=app_args.flow_watcher_args.enable_flow_watcher,
    )

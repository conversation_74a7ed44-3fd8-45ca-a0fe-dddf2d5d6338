NAME = "e2e-pipelines"

[cuju]

# This is a shared configuration. Do not commit your changes!
# Instead, we recommend to use local environment variables to override the default specified here.

# --- Parameters 'pipe_main_local.json.j2' ---
PIPE_E2E = "e2e-pipelines/scene/pipe_e2e_monolith-scene.json.j2"

PATH_TO_VIDEO_FILE = "datasets/cuju-exercise-session/5de07ba9-0169-41d6-ad8a-b3e98a7bbaea.mov"
EXERCISE_ID = "29"
EXERCISE_EVENT_ID = "5de07ba9-0169-41d6-ad8a-b3e98a7bbaea"

# ==============================================================================
#   General
# ==============================================================================
LOG_LEVEL = "ERROR"
STRUCTURED_LOGGING = "true"

# PIPE_WORKING_DIR = "~/work/cuju/repo/cuju-ai-workbench/services/cuju_e2e_service/cuju_e2e_service/"

# --- Define defaults for data input/output unless overwritten by Backend Request message ---
# Used with S3RawStorageConnector
DATA_BUCKET_NAME = "ai-bucket-137068251345"
DATA_BASE_DIR = "pipeline_outputs_sandbox/rk_tmp/meta"
# Used with DiskRawStorageConnector
DATA_LOCAL_PATH = "~/work/cuju/repo/cuju-ai-workbench/data/videos/"

# --- Define model input ---
# Used with S3RawStorageConnector
MODEL_STORE_BUCKET_NAME = "ai-bucket-137068251345"
MODEL_STORE_BASE_DIR = "model-store/"
# Used with DiskRawStorageConnector
MODEL_LOCAL_STORE_PATH = "~/work/cuju/repo/cuju-ai-workbench/model-store/"

# --- Cache path  ---
PATH_TO_CACHE = "~/.cuju/cache"

# --- Batch size ---
BATCH_SIZE = 32

# ==============================================================================
#   VIDPROC: Video Processing Pipelines
# ==============================================================================

# --- Cores ---
PIPE_PREPROC_CORE = "e2e-pipelines/vidproc/preproc/pipe_preproc_core.json.j2"
PIPE_VST_CORE = "e2e-pipelines/vidproc/vst/pipe_vst_core-affine.json.j2"
PIPE_CONES_CORE = "e2e-pipelines/vidproc/cones/pipe_cones_core-fixed-threshold.json.j2"

# --- Probes ---
PIPE_VIDPROC_PROBE = "e2e-pipelines/vidproc/pipe_vidproc_probe.json.j2"
VIDPROC_PROBE_HTTP = false
VIDPROC_PROBE_HTTP_PORT = 8001
VIDPROC_PROBE_STORE_VIDEO = true
VIDPROC_PROBE_STORE_JSON = true

# --- Metrics ---
PIPE_VIDPROC_METRICS = "e2e-pipelines/vidproc/pipe_vidproc_metrics.json.j2"


# ==============================================================================
#   BALL: Detection Pipelines
# ==============================================================================

# --- Cores ---
PIPE_BALL_CORE = "e2e-pipelines/ball/pipe_ball_core-rtdetr.json.j2"

# --- Probes ---
PIPE_BALL_PROBE = "e2e-pipelines/ball/pipe_ball_probe.json.j2"
BALL_PROBE_HTTP = false
BALL_PROBE_HTTP_PORT = 8002
BALL_PROBE_STORE_VIDEO = false
BALL_PROBE_STORE_JSON = true

# --- Metrics ---
PIPE_BALL_METRICS = "e2e-pipelines/ball/pipe_ball_metrics.json.j2"

# ==============================================================================
#   Person: Detection Pipelines
# ==============================================================================

# --- Cores ---
PIPE_PERSON_CORE = "e2e-pipelines/person/pipe_person_core-rtmdet-vitpose-boxmot-diffmot.json.j2"

# --- Probes ---
PIPE_PERSON_PROBE = "e2e-pipelines/person/pipe_person_probe.json.j2"
PERSON_PROBE_HTTP = false
PERSON_PROBE_HTTP_PORT = 8003
PERSON_PROBE_STORE_VIDEO = false
PERSON_PROBE_STORE_JSON = true

# --- Metrics ---
PIPE_PERSON_METRICS = "e2e-pipelines/person/pipe_person_metrics.json.j2"

# ==============================================================================
#   Scene: Exercise Pipelines
# ==============================================================================

# --- Cores ---
PIPE_NAVETTE_CORE = "e2e-pipelines/scene/pipe_navette.json.j2"
PIPE_CORRIDOR_WB_CORE = "e2e-pipelines/scene/pipe_corridor_wb.json.j2"
PIPE_SPRINT_CORE = "e2e-pipelines/scene/pipe_sprint.json.j2"
PIPE_SQUARE_WB_CORE = "e2e-pipelines/scene/pipe_square_wb.json.j2"
PIPE_T_TEST_CORE = "e2e-pipelines/scene/pipe_t_test.json.j2"
PIPE_T_TEST_WB_CORE = "e2e-pipelines/scene/pipe_t_test_wb.json.j2"
PIPE_JUMP_CORE = "e2e-pipelines/scene/pipe_jump.json.j2"
PIPE_WALL_BALL_WB_CORE = "e2e-pipelines/scene/pipe_wall_ball.json.j2"

# ==============================================================================
#   Scene: Exercise SPRINT
# ==============================================================================
CFG_EX24_SETUP_DISTANCE = 15
CFG_EX24_SETUP_MAX_PERCENTAGE = 0.15
CFG_EX24_SETUP_MIN_PERCENTAGE = 0.05
CFG_EX24_SANITY_MIN_VALUE = 1.7
CFG_EX24_PLAYER_MAX_DISTANCE_TO_START_CONE = 1.5
CFG_EX24_PLAYER_MIN_DISTANCE_TO_START_CONE = 0.2
CFG_EX24_STANDSTRAIGHT_THRESHOLD = 0.9
CFG_EX24_STANDSTRAIGHT_MIN_FRAMES = 2
CFG_EX24_RUN_START_SECONDS_AFTER_START = 1
# ==============================================================================
#   Scene: Exercise TTEST
# ==============================================================================
CFG_EX29_SETUP_DISTANCE = 9
CFG_EX29_SETUP_MAX_PERCENTAGE = 0.15
CFG_EX29_SETUP_MIN_PERCENTAGE = 0.05
CFG_EX29_SANITY_MIN_VALUE = 7
CFG_EX29_PLAYER_MAX_DISTANCE_TO_START_CONE = 1.5
CFG_EX29_PLAYER_MIN_DISTANCE_TO_START_CONE = 0.2
CFG_EX29_STANDSTRAIGHT_THRESHOLD = 0.59
CFG_EX29_STANDSTRAIGHT_MIN_FRAMES = 2
CFG_EX29_RUN_START_SECONDS_AFTER_START = 1
# ==============================================================================
#   Scene: Exercise TTEST WITH BALL
# ==============================================================================
CFG_EX32_SETUP_DISTANCE = 9
CFG_EX32_SETUP_MAX_PERCENTAGE = 0.15
CFG_EX32_SETUP_MIN_PERCENTAGE = 0.05
CFG_EX32_SANITY_MIN_VALUE = 8.9
CFG_EX32_PLAYER_MAX_DISTANCE_TO_START_CONE = 1.5
CFG_EX32_PLAYER_MIN_DISTANCE_TO_START_CONE = 0.2
CFG_EX32_STANDSTRAIGHT_THRESHOLD = 0.59
CFG_EX32_STANDSTRAIGHT_MIN_FRAMES = 2
CFG_EX32_RUN_START_SECONDS_AFTER_START = 1

# ==============================================================================
#   Scene: Exercise SQUARE WITH BALL
# ==============================================================================
CFG_EX27_SETUP_DISTANCE = 4.5
CFG_EX27_SETUP_MAX_PERCENTAGE = 0.2
CFG_EX27_SETUP_MIN_PERCENTAGE = 0.06
CFG_EX27_SANITY_MIN_VALUE = 8.9
CFG_EX27_PLAYER_MAX_DISTANCE_TO_START_CONE = 1.5
CFG_EX27_PLAYER_MIN_DISTANCE_TO_START_CONE = 0.2
CFG_EX27_STANDSTRAIGHT_THRESHOLD = 0.59
CFG_EX27_STANDSTRAIGHT_MIN_FRAMES = 2
CFG_EX27_RUN_START_SECONDS_AFTER_START = 1

# ==============================================================================
#   Scene: Exercise NAVETTE
# ==============================================================================
CFG_EX5_SETUP_DISTANCE = 9
CFG_EX5_SETUP_MAX_PERCENTAGE = 0.1
CFG_EX5_SETUP_MIN_PERCENTAGE = 0.05
CFG_EX5_SANITY_MAX_VALUE = 200
CFG_EX5_STANDSTRAIGHT_THRESHOLD = 0.59
CFG_EX5_STANDSTRAIGHT_MIN_FRAMES = 2

# ==============================================================================
#   Scene: Exercise WALL BALL WITH BALL
# ==============================================================================
CFG_EX36_SETUP_DISTANCE = 3
CFG_EX36_SETUP_MAX_PERCENTAGE = 0.5
CFG_EX36_SETUP_MIN_PERCENTAGE = 0.05
CFG_EX36_SANITY_MAX_VALUE = 75
CFG_EX36_STANDSTRAIGHT_THRESHOLD = 0.59
CFG_EX36_STANDSTRAIGHT_MIN_FRAMES = 2

# ==============================================================================
#   Scene: Exercise CORRIDOR WITH BALL
# ==============================================================================
CFG_EX6_SETUP_DISTANCE = 3
CFG_EX6_SETUP_MAX_PERCENTAGE = 0.5
CFG_EX6_SETUP_MIN_PERCENTAGE = 0.05
CFG_EX6_SANITY_MAX_VALUE = 40
CFG_EX6_STANDSTRAIGHT_THRESHOLD = 0.59
CFG_EX6_STANDSTRAIGHT_MIN_FRAMES = 2

# ==============================================================================
#   Scene: Exercise JUMP
# ==============================================================================
CFG_EX34_SETUP_DISTANCE = 3
CFG_EX34_SETUP_MAX_PERCENTAGE = 0.5
CFG_EX34_SETUP_MIN_PERCENTAGE = 0.5
CFG_EX34_SANITY_MAX_VALUE = 3.51
CFG_EX34_SANITY_MIN_VALUE = 0.15
CFG_EX34_STANDSTRAIGHT_THRESHOLD = 0.9
CFG_EX34_STANDSTRAIGHT_MIN_FRAMES = 2
